<?php

namespace App\Providers;

// use Illuminate\Support\Facades\Gate;
use App\Models\Customer\Employee;
use App\Models\User;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        //
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        // Auth::viaRequest('custom-token', function (Request $request) {
            // return User::first();
            // return Employee::where('token', (string) $request->token)->first();
        // });
    }
}
