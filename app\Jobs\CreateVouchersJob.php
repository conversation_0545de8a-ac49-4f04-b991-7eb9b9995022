<?php

namespace App\Jobs;

use App\Models\Customer\Voucher\VoucherTohirgoo;
use App\Services\EmployeeVoucherService;
use App\Services\ConnectionService;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Collection;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class CreateVouchersJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public Collection $records;
    public string $connectionName;

    public function __construct(string $connectionName, Collection $records)
    {
        $this->connectionName = $connectionName;
        $this->records        = $records;
    }

    public function handle(): void
    {
        resolve(EmployeeVoucherService::class)->createEmployeeVouchersByVoucherTohirgoos($this->connectionName, $this->records);
    }
}
