<?php

namespace App\Http\Resources;

use App\Http\Resources\Employee;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class Apartment
 *
 * @mixin \App\Models\Apartment
 */
class DeviceConfig extends JsonResource
{
    const ID                    = 'id';
    const NAME                  = 'name';
    const SERIAL_NUMBER         = 'serial_number';
    const LAST_DOWNLOAD_DATE    = 'last_download_date';
    const EMPLOYEE_COUNT        = 'employee_count';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                    => $this->id,
            self::NAME                  => $this->name,
            self::SERIAL_NUMBER         => $this->serial_number,
            self::LAST_DOWNLOAD_DATE    => $this->last_download_date,
            self::EMPLOYEE_COUNT        => $this->employee_count,
        ];
    }
}
