<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class EmployeeVoucher extends JsonResource
{
    const ID                                = 'id';
    const AMOUNT                            = 'amount';
    const CREATED_AT                        = 'created_at';
    const VOUCHER_TOHIRGOO_name             = 'voucher_tohirgoo_name';
    
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'                                => $this->id,
            'amount'                            => $this->amount,
            'created_at'                        => $this->created_at,
            'voucher_tohirgoo_name'             => $this->voucher_tohirgoo ? $this->voucher_tohirgoo->name : '',
            'is_active'                         => $this->is_active
        ];
    }
}
