<?php

namespace App\Filament\Resources\Admin\MerchantResource\Pages;

use App\Models\Constant\ConstData;
use App\Models\EmployeeUser;
use App\Models\EmployeeRole;
use App\Models\Customer\MerchantEmployeeUser;
use App\Filament\Resources\Admin\MerchantResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditMerchant extends EditRecord
{
    protected static string $resource = MerchantResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['employee_users'] = MerchantEmployeeUser::where('merchant_id', $this->record->id)->pluck('employee_user_id')->toArray();
        return $data;
    }

    protected function afterSave(): void
    {
        $merchant = $this->record;
        $selectedEmployeeIds = $this->data['employee_users'];

        // 1. Merchant role-г олно
        $merchantRole = EmployeeRole::where(EmployeeRole::NAME, ConstData::EMPLOYEE_ROLE_MERCHANT)->first();
        if (!$merchantRole) {
            throw new \RuntimeException('Merchant role not found!');
        }

        // 2. Merchant-д өмнө холбогдсон employee_user_id-уудыг авна
        $existingEmployeeIds = MerchantEmployeeUser::where('merchant_id', $merchant->id)->pluck('employee_user_id')->toArray();

        // 3. Хасагдсан employee_user_id-уудыг тодорхойлно
        $removedIds = array_diff($existingEmployeeIds, $selectedEmployeeIds);

        // 4. merchant_employee_users хүснэгтийг шинэчилнэ
        MerchantEmployeeUser::where('merchant_id', $merchant->id)->delete();
        foreach ($selectedEmployeeIds as $employeeUserId) {
            MerchantEmployeeUser::create([
                MerchantEmployeeUser::MERCHANT_ID      => $merchant->id,
                MerchantEmployeeUser::EMPLOYEE_USER_ID => $employeeUserId,
            ]);
        }

        // 5. Нэмэгдсэн болон үлдсэн employeeUser-уудад merchant role оноох
        foreach ($selectedEmployeeIds as $employeeUserId) {
            $employeeUser = EmployeeUser::find($employeeUserId);
            if ($employeeUser && !$employeeUser->employee_roles()->where('employee_roles.id', $merchantRole->id)->exists()) {
                $employeeUser->employee_roles()->attach($merchantRole->id);
            }
        }

        // 6. Хасагдсан employeeUser-уудаас merchant role-г устгах
        foreach ($removedIds as $removedEmployeeUserId) {
            $employeeUser = EmployeeUser::find($removedEmployeeUserId);
            if ($employeeUser && $employeeUser->employee_roles()->where('employee_roles.id', $merchantRole->id)->exists()) {
                $employeeUser->employee_roles()->detach($merchantRole->id);
            }
        }
    }
}
