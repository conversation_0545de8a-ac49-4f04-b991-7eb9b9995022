<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmployeeAttendancesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('employee_attendances'))
            return;
        Schema::create('employee_attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_id')->constrained();
            $table->date('att_date')->nullable();
            $table->string('io_string')->nullable();
            $table->string('time_string', 1000)->nullable();
            $table->string('schedule_string', 1000)->nullable();
            $table->string('device_attendance_strids', 1000)->nullable();
            $table->unsignedBigInteger('schedule_time')->nullable();
            $table->boolean('has_conflict')->default(0);
            $table->unsignedBigInteger('work_time')->default(0);
            $table->unsignedBigInteger('late_time')->default(0);
            $table->unsignedBigInteger('absent_time')->default(0);
            $table->unsignedBigInteger('sick_time')->default(0);
            $table->unsignedBigInteger('effort_time')->default(0);
            $table->unsignedBigInteger('break_time')->default(0);
            $table->unsignedBigInteger('break_effort_time')->default(0);
            $table->unsignedBigInteger('furlough_time')->default(0);
            $table->unsignedBigInteger('salary_furlough_time')->default(0);
            $table->unsignedBigInteger('over_time')->default(0);
            $table->unsignedBigInteger('maternity_time')->default(0);
            $table->unsignedBigInteger('vacation_time')->default(0);
            $table->unsignedBigInteger('unknown_work_time')->default(0);
            $table->unsignedBigInteger('unknown_effort')->default(0);
            $table->unsignedBigInteger('night_time')->default(0);
            $table->unsignedBigInteger('compensatory_rest_time')->default(0);
            $table->unsignedBigInteger('request_work_time')->default(0);
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employee_attendances');
    }
}
