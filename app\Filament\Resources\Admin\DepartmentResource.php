<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Models\Constant\ConstData;
use App\Models\Customer\Department;

use App\Filament\Resources\Admin\DepartmentResource\Pages;
use App\Filament\Resources\Admin\DepartmentResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;

class DepartmentResource extends Can
{
    protected static ?string $model = Department::class;

    protected static ?string $navigationIcon = 'heroicon-o-briefcase';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Хэлтэс';
    protected static ?string $modelLabel = 'хэлтэс';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationGroup = 'Бүртгэл';
    protected static ?string $slug = 'departments';

    public static function form(Form $form): Form
    {
        return $form
                ->schema([
                    Forms\Components\Section::make()
                        ->schema([
                            Forms\Components\TextInput::make('name')
                                ->label('Нэр')
                                ->maxLength(50)
                                ->required(),

                            Forms\Components\TextInput::make('short_name')
                                ->label('Богино нэр')
                                ->maxLength(3)
                                ->required(),

                            Forms\Components\TextInput::make('rn')
                                ->label('Регистрийн №')
                                ->length(7),

                            Forms\Components\Select::make('parent_id')
                                ->label('Дээд хэлтэс')
                                ->options(Department::all()->pluck(ConstData::NAME, ConstData::ID))
                                ->searchable(),

                        ])
                        ->columns(1)
                        ->columnSpan(['lg' => fn (?Department $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Department $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Department $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Department $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('short_name')->label('Богино нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('parent_department.name')->label('Дээд хэлтэс')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDepartments::route('/'),
            'create' => Pages\CreateDepartment::route('/create'),
            'edit' => Pages\EditDepartment::route('/{record}/edit'),
        ];
    }
}
