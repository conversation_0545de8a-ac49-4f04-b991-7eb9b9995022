<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class Apartment
 *
 * @mixin \App\Models\Apartment
 */
class EmployeeAttendanceDtl extends JsonResource
{
    const ID                    = 'id';
    const BEGIN_DATE            = 'begin_date';
    const END_DATE              = 'end_date';
    const STATUS                = 'attendance_status';
    const STATUS_NAME           = 'attendance_status_name';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                => $this->id,
            self::BEGIN_DATE        => $this->begin_date,
            self::END_DATE          => $this->end_date,
            self::STATUS            => $this->attendance_status,
            self::STATUS_NAME       => $this->getAttStatusName()
        ];
    }
}
