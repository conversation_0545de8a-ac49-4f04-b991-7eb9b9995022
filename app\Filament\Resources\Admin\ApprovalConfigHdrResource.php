<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Models\Customer\Employee;
use App\Models\Customer\Time\Approval\ApprovalConfigHdr;
use App\Models\Customer\Time\Approval\ApprovalConfigDtl;
use App\Filament\Resources\Admin\ApprovalConfigHdrResource\Pages;
use App\Filament\Resources\Admin\ApprovalConfigHdrResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;


class ApprovalConfigHdrResource extends Can
{
    protected static ?string $model = ApprovalConfigHdr::class;

    protected static ?string $navigationIcon = 'heroicon-o-check-circle';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Батлах тохиргоо';
    protected static ?string $modelLabel = 'Батлах тохиргоо';
    protected static ?int $navigationSort = 6;
    protected static ?string $navigationGroup = 'Цаг';
    protected static ?string $slug = 'approval_config_hdr';

    public static function form(Form $form): Form
    {
        return $form
                ->schema([
                    Forms\Components\Section::make()
                        ->schema([
                            Forms\Components\TextInput::make('name')
                                ->label('Нэр')
                                ->maxLength(50)
                                ->required(),

                            Forms\Components\Repeater::make(ApprovalConfigHdr::RELATION_APPROVAL_CONFIG_DTLS)
                                ->label('Ажилчид')
                                ->relationship()
                                ->minItems(1)
                                ->defaultItems(1)
                                ->addActionLabel('Ажилтан нэмэх')
                                ->mutateRelationshipDataBeforeCreateUsing(function (array $data): array {
                                    $data[ApprovalConfigDtl::CREATED_BY] = auth()->user()->id;
                                    return $data;
                                })
                                ->simple(
                                    Forms\Components\Select::make(ApprovalConfigDtl::EMPLOYEE_ID)
                                        ->label('Ажилтан')
                                        ->options(Employee::all()->pluck(Employee::FULL_NAME, Employee::ID))
                                        ->searchable()
                                        ->distinct()
                                        ->searchingMessage('Хайж байна...')
                                        ->loadingMessage('Ачаалж байна байна...')
                                        ->noSearchResultsMessage('Ажилтан олдсонгүй.')
                                        ->suffixIcon('heroicon-m-face-smile')
                                        ->disabled(fn (?ApprovalConfigDtl $record) => $record != null)
                                        ->required(),
                                ),
        
                            Forms\Components\Hidden::make(ApprovalConfigHdr::CREATED_BY)
                                    ->default(auth()->user()->id)
                                    ->disabled(fn (?ApprovalConfigHdr $record) => $record != null),
                        ])
                        ->columns(1)
                        ->columnSpan(['lg' => fn (?ApprovalConfigHdr $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (ApprovalConfigHdr $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (ApprovalConfigHdr $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?ApprovalConfigHdr $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(ApprovalConfigHdr::NAME)->label('Нэр')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListApprovalConfigHdrs::route('/'),
            'create' => Pages\CreateApprovalConfigHdr::route('/create'),
            'edit' => Pages\EditApprovalConfigHdr::route('/{record}/edit'),
        ];
    }

    public static function canViewAny(): bool
    {
        return false;
    }
}
