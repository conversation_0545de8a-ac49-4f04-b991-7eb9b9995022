<?php

namespace App\Filament\Resources\Admin;

use App\Models\Customer\Voucher\EmployeeVoucherSpent;
use App\Http\Tools\DateTool;
use App\Models\Class\Can;
use App\Exports\EmployeeVoucherSpent2Export;
use App\Filament\Resources\Admin\EmployeeVoucherSpentResource\Pages;
use App\Filament\Resources\Admin\EmployeeVoucherSpentResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\DatePicker;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Facades\Excel;
use Filament\Tables\Actions\Action;

class EmployeeVoucherSpentResource extends Can
{
    protected static ?string $model = EmployeeVoucherSpent::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Зарцуулалт';
    protected static ?string $modelLabel = 'Зарцуулалт';
    protected static ?int $navigationSort = 113;
    protected static ?string $navigationGroup = 'Ваучер';
    protected static ?string $slug = 'employee-voucher-spents';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->headerActions([
                 Action::make('export_excel')
                    ->label('Download Excel')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->action(function ($livewire) {
                        // Table-ийн фильтертэй query-г авах
                        $filteredQuery = $livewire->getFilteredTableQuery();

                        // Query-г export-д дамжуулах
                        $employeeVoucherSpents = $filteredQuery->with([
                            'employee',
                        ])->get();
                        return Excel::download(new EmployeeVoucherSpent2Export($employeeVoucherSpents), 'employee_voucher_spent.xlsx');
                    }),
            ])
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('employee.full_name')->label('Овог нэр')->sortable()->searchable(['first_name', 'last_name']),
                Tables\Columns\TextColumn::make('employee.department.name')->label('Хэлтэс')->sortable()->searchable()->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('employee.position.name')->label('Албан тушаал')->sortable()->searchable()->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('merchant_company_name')->label('Байгууллага')->sortable()->searchable()->toggleable(isToggledHiddenByDefault: false),
                Tables\Columns\TextColumn::make('merchant_location_name')->label('Байршил')->sortable()->searchable()->toggleable(isToggledHiddenByDefault: false),
                Tables\Columns\TextColumn::make('merchant_employee_display_name')->label('Нэр')->toggleable(isToggledHiddenByDefault: false),
                Tables\Columns\TextColumn::make('amount')->label('Дүн')->sortable()->money('MNT')->searchable(),
                Tables\Columns\TextColumn::make('created_at')->label('Огноо')->sortable()->searchable(),
            ])
            ->filters([
                Filter::make('created_at')
                    ->form([
                        DatePicker::make('begin_date')->default(DateTool::setOnlyDay(now(), 1)),
                        DatePicker::make('end_date')->default(DateTool::getLastDayOfMonth(now())),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['begin_date'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date)
                            )
                            ->when(
                                $data['end_date'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date)
                            );
                    })
            ])
            ->actions([
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEmployeeVoucherSpents::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->orderBy('created_at', 'desc');
    }
}
