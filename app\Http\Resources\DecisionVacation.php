<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class DecisionVacation
 *
 * @mixin \App\Models\Customer\Time\Decision\DecisionVacation
 */
class DecisionVacation extends JsonResource
{
    const ID                         = 'id';
    const DECISION_ID                = 'decision_id';
    const VACATION_DATE              = 'vacation_date';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                            => $this->id,
            self::DECISION_ID                   => $this->decision_id,
            self::VACATION_DATE                 => $this->vacation_date,
        ];
    }
}
