<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class DecisionLongTermFurlough
 *
 * @mixin \App\Models\Customer\Time\Decision\DecisionLongTermFurlough
 */
class DecisionLongTermFurlough extends JsonResource
{
    const ID                         = 'id';
    const DECISION_ID                = 'decision_id';
    const BEGIN_DATE                 = 'begin_date';
    const END_DATE                   = 'end_date';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                            => $this->id,
            self::DECISION_ID                   => $this->decision_id,
            self::BEGIN_DATE                    => $this->begin_date,
            self::END_DATE                      => $this->end_date,
        ];
    }
}
