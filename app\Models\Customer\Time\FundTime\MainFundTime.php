<?php

namespace App\Models\Customer\Time\FundTime;

use App\Enums\FundTimeTypeEnum;
use App\Http\Tools\DateTool;
use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MainFundTime extends Model
{
    use HasFactory;

    const ID                     = 'id';
    const SALARY_PERIOD_ID       = 'salary_period_id';
    const TYPE                   = 'type';
    const WD_COUNT               = 'wd_count';
    const WD_MIN                 = 'wd_min';
    const MONDAY_MIN             = 'monday_min';
    const TUESDAY_MIN            = 'tuesday_min';
    const WEDNESDAY_MIN          = 'wednesday_min';
    const THURSDAY_MIN           = 'thursday_min';
    const FRIDAY_MIN             = 'friday_min';
    const SATURDAY_MIN           = 'saturday_min';
    const SUNDAY_MIN             = 'sunday_min';
    const CREATED_BY             = 'created_by';
    const UPDATED_BY             = 'updated_by';

    const TYPE_CONSTANT    = 'constant';
    const TYPE_INCONSTANT  = 'inconstant';

    const WD_TIME          = 'wd_time';
    const MONDAY_TIME      = 'monday_time';
    const WD_TOTAL_TIME    = 'wd_total_time';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::SALARY_PERIOD_ID,
        self::TYPE,
        self::WD_COUNT,
        self::WD_MIN,
        self::MONDAY_MIN,
        self::TUESDAY_MIN,
        self::WEDNESDAY_MIN,
        self::THURSDAY_MIN,
        self::FRIDAY_MIN,
        self::SATURDAY_MIN,
        self::SUNDAY_MIN,
        self::CREATED_BY,
    ];

    protected $casts = [
        self::TYPE => FundTimeTypeEnum::class,
    ];

    public function getWdTimeAttribute() {  
        return DateTool::convertTime($this->wd_min);
    }

    public function getWdTotalTimeAttribute() {  
        return $this->wd_count * $this->wd_min / 60;
    }

    public function getTotalWdMinute() {  
        return $this->wd_count * $this->wd_min;
    }
}
