<?php

namespace App\Filament\Resources\Admin\HolidayResource\Pages;

use App\Filament\Resources\Admin\HolidayResource;
use App\Models\Customer\Time\Holiday;
use Filament\Resources\Pages\CreateRecord;

class CreateHoliday extends CreateRecord
{
    protected static string $resource = HolidayResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data[Holiday::CREATED_BY] = auth()->user()->id;
        return $data;
    }
}
