<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Models\Customer\Decision\Decision;
use App\Filament\Resources\Admin\DecisionResource\Pages;
use App\Filament\Resources\Admin\DecisionResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;

class DecisionResource extends Can
{
    protected static ?string $model = Decision::class;

    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Ажилд авах';
    protected static ?string $modelLabel = 'ажилд авах';
    protected static ?int $navigationSort = 4;
    protected static ?string $navigationGroup = 'Тушаал';
    protected static ?string $slug = 'decision_hires';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('typeName')->label('Тушаалын нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('number')->label('Тушаалын №')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('decision_date')->label('Тушаалын огноо')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('employee.fullName')->label('Ажилтаны нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('hire.hire_date')->label('Ажиллаж эхэлсэн огноо')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDecisions::route('/'),
            'create' => Pages\CreateDecision::route('/create'),
        ];
    }
}
