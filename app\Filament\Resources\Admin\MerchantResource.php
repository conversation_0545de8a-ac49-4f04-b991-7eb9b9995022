<?php

namespace App\Filament\Resources\Admin;

use App\Models\Customer\Merchant;
use App\Models\Class\Can;
use App\Services\ConnectionService;
use App\Services\EmployeeUserService;
use App\Filament\Resources\Admin\MerchantResource\Pages;
use App\Filament\Resources\Admin\MerchantResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;

class MerchantResource extends Can
{
    protected static ?string $model = Merchant::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Бүртгэл';
    protected static ?string $modelLabel = 'Бүртгэл';
    protected static ?int $navigationSort = 114;
    protected static ?string $navigationGroup = 'Мерчант';
    protected static ?string $slug = 'merchants';

    public static function form(Form $form): Form
    {
        return $form
                ->schema([
                    Forms\Components\Section::make()
                        ->schema([
                            Forms\Components\TextInput::make(Merchant::NAME)
                                ->label('Нэр')
                                ->maxLength(50)
                                ->required(),
                            Forms\Components\TextInput::make(Merchant::LOCATION_NAME)
                                ->label('Хаяг байршил')
                                ->maxLength(50)
                                ->required(),
                            Forms\Components\Select::make(Merchant::EMPLOYEE_USERS)
                                ->label('Ажилтан')
                                ->options(function () {
                                    $cn = ConnectionService::connectionName();  
                                    return resolve(EmployeeUserService::class)->getEmployeeUsersWithFullDisplayName($cn);
                                })
                                ->multiple()
                                ->searchingMessage('Хайж байна...')
                                ->loadingMessage('Ачаалж байна байна...')
                                ->noSearchResultsMessage('Хэлтэс олдсонгүй.')
                                ->required(),
                        ])
                        ->columns(1)
                        ->columnSpan(['lg' => fn (?Merchant $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Merchant $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Merchant $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Merchant $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Merchant::NAME)->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Merchant::LOCATION_NAME)->label('Хаяг байршил')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
            ]);
    }

    public static function getRelations(): array
    {
        return [
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMerchants::route('/'),
            'create' => Pages\CreateMerchant::route('/create'),
            'edit' => Pages\EditMerchant::route('/{record}/edit'),
        ];
    }
}
