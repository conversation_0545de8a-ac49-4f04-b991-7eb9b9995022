<?php

namespace App\Models\Customer\Time\SuperSalaryPeriod;

use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SuperSalaryPeriodSubOneDtl extends Model
{
    use HasFactory;

    const SUPER_SALARY_PERIOD_DTL_ID = 'super_salary_period_dtl_id';
    const DEPARTMENT_ID              = 'department_id';

    const RELATION_SUPER_SALARY_PERIOD_DTL = 'super_salary_period_dtl';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::SUPER_SALARY_PERIOD_DTL_ID,
        self::DEPARTMENT_ID,  
    ];

    public function super_salary_period_dtl()
    {
        return $this->belongsTo(SuperSalaryPeriodDtl::class);
    }
}
