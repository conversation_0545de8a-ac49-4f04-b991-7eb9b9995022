<?php

namespace App\Services\Time;

use App\Models\Class\BusAttendance;

use App\Models\Customer\Employee;
use App\Models\Customer\Time\TimeRequest\TimeRequest;
use App\Models\Customer\Time\Schedule\MainTimeSchedule\MainTimeScheduleDtl;
use App\Models\Customer\Time\Attendance\RawAttendance;
use App\Models\Customer\Time\Attendance\EmployeeAttendance;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Services\Time\OtherTimeScheduleService;
use App\Services\ConnectionService;
use App\Http\Tools\DateTool;
use App\Models\Customer\Time\Schedule\TimeScheduleEmployee;

class PrepareEmployeeAttendancesService
{
    private $busAttendance;
    public function __construct(BusAttendance $busAttendance) {
        $this->busAttendance = $busAttendance;
    }

    public function prepareAllEmployeeAttendances() {
        $connectionName               = $this->busAttendance->connection_name;
        $employees                    = $this->busAttendance->employees;
        $superSalaryPeriodDtl         = $this->busAttendance->super_salary_period_dtl;
        $holidates                    = $this->busAttendance->holidates;
        $otherTimeScheduleEmployees   = $this->busAttendance->other_time_schedule_employees;
        $rawAttendances               = $this->busAttendance->raw_attendances;
        $confirmedTimeRequests        = $this->busAttendance->confirmed_time_requests;
        $employeeAttendaces           = $this->busAttendance->employee_attendances;
        $nowDateTime                  = $this->busAttendance->now_date_time;
        $nowDate                      = DateTool::getDate($nowDateTime);
        $beginDate                    = $superSalaryPeriodDtl->getBeginDate();
        $endDate                      = $superSalaryPeriodDtl->getEndDate();
        
        while ($beginDate <= $endDate) {
            $salaryDate = $beginDate;
            if ($salaryDate >= $nowDate)
                break;
            $hasHoliday = collect($holidates)->contains($salaryDate);
            // ТУХАЙН ӨДРИЙН ҮНДСЭН ЦАГИЙН ХУВААРЬ
            $mainTimeScheduleService = resolve(MainTimeScheduleService::class);
            $mainTimeSchedule        = $mainTimeScheduleService->getMainTimeScheduleByAttDate($connectionName, $salaryDate);
            // $mainTimeScheduleDtl     = null;

            if (isset($mainTimeSchedule))
                $mainTimeScheduleDtl = $mainTimeSchedule->getMainTimeScheduleDtlByDate($salaryDate);

            // БУСАД ЦАГИЙН ХУВААРЬ
            $filteredOtherTimeScheduleEmployees = $otherTimeScheduleEmployees->filter(function ($value) use ($salaryDate) {
                return $value->begin_date <= $salaryDate;
            })->sortByDesc(TimeScheduleEmployee::BEGIN_DATE)->values();

            // БАТЛАГДСАН ХҮСЭЛТ
            $filteredConfirmedTimeRequests = $confirmedTimeRequests->filter(function ($value, $key) use ($salaryDate) {
                return DateTool::getDate($value->begin_date) <= $salaryDate && DateTool::getDate($value->end_date) >= $salaryDate;
            })->values();

            //ҮҮССЭН ИРЦ
            $filteredEmployeeAttendances = $employeeAttendaces->filter(function ($value, $key) use ($salaryDate) {
                return $value->att_date == $salaryDate;
            })->values();
            
            foreach ($employees as $key => $employee) {
                if ($employee->employee_dtl->company_work_date > $salaryDate)
                    continue;

                $currentOtherTimeScheduleEmployee = $filteredOtherTimeScheduleEmployees->first(function ($value, $key) use ($employee) {
                    return $value->employee_id == $employee->id;
                });

                $currentRawAttendances = $rawAttendances->filter(function ($value, $key) use ($employee) {
                    return $value->attendance_code == $employee->employee_dtl->attendance_code;
                })->values();

                $currentConfirmedTimeRequests = $filteredConfirmedTimeRequests->filter(function ($value, $key) use ($employee) {
                    return $value->employee_id == $employee->id;
                })->values();
                $currentEmployeeAttendance = $filteredEmployeeAttendances->first(function ($value, $key) use ($employee) {
                    return $value->employee_id == $employee->id;
                });
                $employeeAttendaces = $employeeAttendaces->merge($this->prepareEmployeeAttendance($employee, $salaryDate, $mainTimeScheduleDtl, $currentOtherTimeScheduleEmployee, $currentRawAttendances, $currentConfirmedTimeRequests, $currentEmployeeAttendance, $hasHoliday));
            }
            $this->busAttendance->employee_attendances = $employeeAttendaces;
            $beginDate = DateTool::addDays($beginDate, 1)->format('Y-m-d');   
        }
    }

    public function prepareEmployeeAttendancesByEmployee($user, $employeeId, $beginDate, $endDate) {
        $cn                        = ConnectionService::connectionName($user);
        $this->busAttendance->connection_name = $cn;
        $this->busAttendance->user = $user;
        $employee                  = Employee::on($cn)->find($employeeId);
        $holidates                 = resolve(HolidayService::class)->getHolidatesByBetweenDates($cn, $beginDate, $endDate);
        $otherTimeScheduleService  = resolve(OtherTimeScheduleService::class);
        while ($beginDate < $endDate) {
            $salaryDate                = DateTool::getDate($beginDate);
            $mainTimeSchedule          = resolve(MainTimeScheduleService::class)->getMainTimeScheduleByAttDate($cn, $salaryDate);
            $mainTimeScheduleDtl       = $mainTimeSchedule->getMainTimeScheduleDtlByDate($salaryDate);
            $otherTimeScheduleEmployee = $otherTimeScheduleService->getOtherTimeScheduleEmployeeByDate($cn, $employeeId, $salaryDate);

            $rawAttendances      = RawAttendance::on($cn)->where(RawAttendance::ATTENDANCE_CODE, $employee->employee_dtl->attendance_code)
                                    ->where(RawAttendance::REGISTER_DATE, '>= ', DateTool::getPrevDate($salaryDate))
                                    ->where(RawAttendance::REGISTER_DATE, '<=', DateTool::getTomorrowFromDate($salaryDate))
                                    ->get();

            $timeRequests       = TimeRequest::on($cn)->where(TimeRequest::EMPLOYEE_ID, $employeeId)
                                    ->whereDate(TimeRequest::BEGIN_DATE, '<= ', $salaryDate)->whereDate(TimeRequest::END_DATE, '>=', $salaryDate)
                                    ->get(); 
            $employeeAttendance = EmployeeAttendance::on($cn)->where(EmployeeAttendance::EMPLOYEE_ID, $employeeId)
                                    ->where(EmployeeAttendance::ATT_DATE, $salaryDate)
                                    ->first();    

            $hasHoliday = collect($holidates)->contains($salaryDate);
            
            $this->prepareEmployeeAttendance($employee, $salaryDate, $mainTimeScheduleDtl, $otherTimeScheduleEmployee, $rawAttendances, $timeRequests, $employeeAttendance, $hasHoliday);
            $beginDate = DateTool::addDays($beginDate, 1);
        }
    }

    public function getTimeScheduleDtlByEmployeeAndDate($salaryDate, $hasHoliday, $otherTimeScheduleEmployee, $mainTimeScheduleDtl) {
        $timeScheduleDtls = collect();
        if ($hasHoliday)
            return $timeScheduleDtls;
        $filteredTimeSchedule = $otherTimeScheduleEmployee ? $otherTimeScheduleEmployee->time_schedule : null;

        if ($filteredTimeSchedule) {
            $timeScheduleDtl = $filteredTimeSchedule->getOtherTimeScheduleDtlByDate($salaryDate);
            if ($timeScheduleDtl) {
                $timeScheduleDtls->push($timeScheduleDtl);
            }
        }
        if (count($timeScheduleDtls) === 0) {
            if ($mainTimeScheduleDtl)
                $timeScheduleDtls->push($mainTimeScheduleDtl);
        }
        return $timeScheduleDtls;
    }

    public function prepareEmployeeAttendance($employee, $salaryDate, $mainTimeScheduleDtl, $otherTimeScheduleEmployee, $rawAttendances, $timeRequests, $employeeAttendance, $hasHoliday) {
        $timeScheduleDtls = $this->getTimeScheduleDtlByEmployeeAndDate($salaryDate, $hasHoliday, $otherTimeScheduleEmployee, $mainTimeScheduleDtl);
        // ТУХАЙН ӨДӨР ЦАГИЙН ХУВААРИГҮЙ БӨГӨӨД АМРАЛТЫН ӨДӨР БАЙВАЛ
        if (isset($timeScheduleDtls) && count($timeScheduleDtls) == 1 && $timeScheduleDtls[0]->type == 3) {
            $this->deleteEmployeeAttendance($employee->id, $salaryDate);
            
            $bd = DateTool::setZeroTime($salaryDate);
            $ed = DateTool::setZeroTime(DateTool::addDays($salaryDate, 1));
            $filteredDeviceAttendances = $rawAttendances->filter(function ($value, $key) use ($bd, $ed) {
                return $value->register_date >= $bd && $value->register_date <= $ed;
            })->values();
            return $this->createEmployeeAttendanceWithDtlsByRawAttendances($salaryDate, $employee, $filteredDeviceAttendances);
        }
        return $this->calcEmployeeAttendance($employee, $salaryDate, $timeScheduleDtls, $rawAttendances, $timeRequests, $employeeAttendance);
    }

    // create employee_attendances by raw_attendances
    public function createEmployeeAttendanceWithDtlsByRawAttendances($attDate, $employee, $rawAttendances) {
        if (!$rawAttendances || count($rawAttendances) == 0)
            return collect();
        $cn                           = $this->busAttendance->connection_name;    
        $user                         = $this->busAttendance->user;
        $newEmployeeAttendanceDtls    = collect();
        $currentEmployeeAttendanceDtl = new EmployeeAttendanceDtl();
        $timeStr                      = '';
        $ioStr                        = '';
        $deviceAttendanceStrids       = '';
        foreach ($rawAttendances as $key => $rawAttendance) {
            $isFirst = $key == 0;
            $isLast  = $key == count($rawAttendances) - 1;
            if ($key/2 == 0) {
                $rawAttendance->io_str = EmployeeAttendanceDtl::INPUT_STR;
                $employeeAttendanceDtl = new EmployeeAttendanceDtl();
                $employeeAttendanceDtl->setConnection($cn);
                $employeeAttendanceDtl->begin_date       = $rawAttendance->register_date;
                $employeeAttendanceDtl->end_date         = null;
                $employeeAttendanceDtl->check_io         = true;
                $employeeAttendanceDtl->type             = EmployeeAttendanceDtl::TYPE_EFFORT;
                $employeeAttendanceDtl->first_str        = EmployeeAttendanceDtl::INPUT_STR;
                $employeeAttendanceDtl->second_str       = EmployeeAttendanceDtl::OUTPUT_STR;
                $employeeAttendanceDtl->first_behavior   = $isFirst ? EmployeeAttendanceDtl::LIMIT_STR : EmployeeAttendanceDtl::INPUT_STR;
                $employeeAttendanceDtl->second_behavior  = $isLast ? EmployeeAttendanceDtl::LIMIT_STR : EmployeeAttendanceDtl::OUTPUT_STR;
                $employeeAttendanceDtl->created_by       = $user->id;
                $currentEmployeeAttendanceDtl = $employeeAttendanceDtl;
            } else {
                $rawAttendance->io_str = EmployeeAttendanceDtl::OUTPUT_STR;
                $employeeAttendanceDtl           = $currentEmployeeAttendanceDtl;
                $employeeAttendanceDtl->end_date = $rawAttendance->register_date;
                $newEmployeeAttendanceDtls->push($employeeAttendanceDtl);
            }
            $timeStr                .= $rawAttendance->register_date . ($isLast ? '' : ',');
            $ioStr                  .= $rawAttendance->io_str . ($isLast ? '' : ',');
            $deviceAttendanceStrids .= $rawAttendance->id . ($isLast ? '' : ',');
        }
        $newEmployeeAttendance = new EmployeeAttendance();
        $newEmployeeAttendance->setConnection($cn);
        $newEmployeeAttendance->employee_id              = $employee->id;
        $newEmployeeAttendance->att_date                 = $attDate;
        $newEmployeeAttendance->io_string                = $ioStr;
        $newEmployeeAttendance->time_string              = $timeStr;
        $newEmployeeAttendance->schedule_string          = '';
        $newEmployeeAttendance->device_attendance_strids = $deviceAttendanceStrids;
        $newEmployeeAttendance->schedule_time            = 0;
        $newEmployeeAttendance->created_by               = $user->id;
    
        foreach ($newEmployeeAttendanceDtls as $key => $newEmployeeAttendanceDtl) {
            $newEmployeeAttendanceDtl->setAttendanceDate();
            $newEmployeeAttendanceDtl->setStatus();
            $newEmployeeAttendanceDtl->setBeginAndEndSign();
            $newEmployeeAttendanceDtl->clearOtherColumns();
            $newEmployeeAttendance->setTimeColumn($newEmployeeAttendanceDtl);
        }
        $newEmployeeAttendance->save();
        $newEmployeeAttendance->employee_attendance_dtls()->saveMany($newEmployeeAttendanceDtls);
        return collect([$newEmployeeAttendance]);
    }

    public function calcEmployeeAttendance($employee, $salaryDate, $timeScheduleDtls, $rawAttendances, $timeRequests, $employeeAttendace) {
        $employeeAttendances       = collect();
        $nowDateTime               = $this->busAttendance->now_date_time;
        $scheduleStr               = '';
        $minBeginDate              = null;
        $maxEndDate                = null;
        $filteredDeviceAttendances = collect();
        foreach ($timeScheduleDtls as $key => $timeScheduleDtl) {
            $scheduleStr  .= $timeScheduleDtl->getHashStr();
            if (MainTimeScheduleDtl::TYPE_DYNAMIC === $timeScheduleDtl->type) {
                $bDate = DateTool::setZeroTime($salaryDate);
                $eDate = DateTool::setZeroTime(DateTool::addDays($salaryDate, 1));

                $filteredDeviceAttendances = $rawAttendances->filter(function ($value, $key) use ($bDate, $eDate) {
                    return $value->register_date >= $bDate && $value->register_date <= $eDate;
                })->values();

                $timeScheduleDtl->setDynamicBeginAndEndTime($salaryDate, $filteredDeviceAttendances, $timeRequests);
            } else if (MainTimeScheduleDtl::TYPE_SIMPLE == $timeScheduleDtl->type) {
                $timeScheduleDtl->setSimpleBeginAndEndTime($salaryDate);
            }

            if (!isset($minBeginDate) || $minBeginDate > $timeScheduleDtl->begin_date_time) {
                $minBeginDate = DateTool::setZeroTime($timeScheduleDtl->begin_date_time);
            }
            if (!$maxEndDate || $maxEndDate < $timeScheduleDtl->end_date_time) {
                $afrMin     = $timeScheduleDtl->afr_min;
                $maxEndDate = DateTool::setZeroTime(DateTool::addDays($timeScheduleDtl->end_date_time, 1));
                if ($afrMin > 0)
                    $maxEndDate = DateTool::addMinute($maxEndDate, $afrMin);
            }
        }
        // ЦАГИЙН ХУВААРЬ НЬ ДУУСААГҮЙ БОЛ ТООЦООЛОЛ ХИЙХГҮЙ.
        if (DateTool::getDate($maxEndDate) > DateTool::getDate($nowDateTime))
            return $employeeAttendances;

        $ioStr                  = '';
        $timeStr                = '';
        $deviceAttendanceStrids = '';
        if (count($filteredDeviceAttendances) == 0) {
            $filteredDeviceAttendances = $rawAttendances->filter(function ($value, $key) use ($minBeginDate, $maxEndDate) {
                return $value->register_date >= $minBeginDate && $value->register_date <= $maxEndDate;
            })->values();
        }
        $countDeviceAttendances    = count($filteredDeviceAttendances);
        $filteredDeviceAttendances = $this->getNonDuplicatedDeviceAttendances($filteredDeviceAttendances);

        foreach ($filteredDeviceAttendances as $key => $value) {
            if ($key%2===0)
                $value->io_str = EmployeeAttendanceDtl::INPUT_STR;
            else
                $value->io_str = EmployeeAttendanceDtl::OUTPUT_STR;

            $value->register_date    = DateTool::setZeroSecond($value->register_date);
            $isLastElement           = $key === $countDeviceAttendances - 1;
            $timeStr                .= $value->register_date . ($isLastElement ? '' : ',');
            $ioStr                  .= $value->io_str . ($isLastElement ? '' : ',');
            $deviceAttendanceStrids .= $value->id . ($isLastElement ? '' : ',');
        }

        $hasUnUsedTimeRequest = false;
        foreach ($timeRequests as $key => $timeRequest) {
            if ($timeRequest->is_used) 
                continue;
            $hasUnUsedTimeRequest = true;
            break;
        }

        if (isset($employeeAttendace) && $employeeAttendace->schedule_string === $scheduleStr && $deviceAttendanceStrids === $employeeAttendace->device_attendance_strids && !$hasUnUsedTimeRequest)
            return $employeeAttendances;

        if (isset($employeeAttendace))
            $this->deleteEmployeeAttendance($employee->id, $salaryDate);

        $initialEmployeeTimeAttendanceDtls = $this->createInitialEmployeeAttendanceDtls($employee, $timeScheduleDtls, $minBeginDate, $maxEndDate);
        $employeeAttendanceDtls            = $this->createEmployeeAttendanceDtls($employee, $filteredDeviceAttendances, $initialEmployeeTimeAttendanceDtls);
        $employeeAttendances               = $this->createEmployeeAttendances($salaryDate, $employee->id, $ioStr, $timeStr, $scheduleStr, $deviceAttendanceStrids, $employeeAttendanceDtls, $timeRequests);
        return $employeeAttendances;
    }

    public function getNonDuplicatedDeviceAttendances($deviceAttendances) {
        $nonDuplicatedDeviceAttendances = collect([]);
        foreach ($deviceAttendances as $key => $deviceAttendance) {
            if ($nonDuplicatedDeviceAttendances->contains(RawAttendance::REGISTER_DATE, $deviceAttendance->register_date))
                continue;
            $nonDuplicatedDeviceAttendances->push($deviceAttendance);
        }
        return $nonDuplicatedDeviceAttendances;
    }

    public function deleteEmployeeAttendance($employeeId, $attDate) {
        $connectionName    = $this->busAttendance->connection_name;
        $employeeAttendace = new EmployeeAttendance();
        $employeeAttendace->setConnection($connectionName);
        $employeeAttendace = $employeeAttendace
                                ->where(EmployeeAttendance::EMPLOYEE_ID, $employeeId)
                                ->whereDate(EmployeeAttendance::ATT_DATE, $attDate)
                                ->first();
        if (isset($employeeAttendace)) {
            $employeeAttendace->employee_attendance_dtls()->delete();
            $employeeAttendace->delete();
        }
    }

    public function deleteEmployeeAttendanceByBetweenDates($employeeId, $beginDate, $endDate = null) {
        $connectionName     = $this->busAttendance->connection_name;
        $employeeAttendaces = new EmployeeAttendance();
        $employeeAttendaces->setConnection($connectionName);

        if ($endDate) {
            $employeeAttendaces = $employeeAttendaces
                                ->where(EmployeeAttendance::EMPLOYEE_ID, $employeeId)
                                ->whereBetween(EmployeeAttendance::ATT_DATE, [$beginDate, $endDate])
                                ->get();
        } else {
            $employeeAttendaces = $employeeAttendaces
                                ->where(EmployeeAttendance::EMPLOYEE_ID, $employeeId)
                                ->whereDate(EmployeeAttendance::ATT_DATE, '>=', $beginDate)
                                ->get();
        }

        if ($employeeAttendaces) {
            foreach ($employeeAttendaces as $key => $employeeAttendace) {
                $employeeAttendace->employee_attendance_dtls()->delete();
                $employeeAttendace->delete();
            }
        }
    }

    public function createInitialEmployeeAttendanceDtls($employee, $currentTimeScheduleDtls, $minBeginDate, $maxEndDate) {
        $connectionName         = $this->busAttendance->connection_name;
        $user                   = $this->busAttendance->user;
        $userId                 = $user->id;
        $employeeAttendanceDtls = collect();

        foreach ($currentTimeScheduleDtls as $key => $timeScheduleDtl) {
            $isFirst = $key === 0;
            $isLast  = $key === (count($currentTimeScheduleDtls) - 1);
            $prevTimeScheduleDtl = $isFirst ? null : $currentTimeScheduleDtls[$key-1];
            $nextTimeScheduleDtl = $isLast  ? null : $currentTimeScheduleDtls[$key+1];
            $beginDateWithTime  = $timeScheduleDtl->begin_date_time;
            $endDateWithTime    = $timeScheduleDtl->end_date_time;

            $hasBreakTime        = $timeScheduleDtl->has_break_time;
            $admitLateMin        = $timeScheduleDtl->admit_late_min;
            $timeScheduleDtlBts  = $timeScheduleDtl->time_schedule_dtl_bts;

            $calcBeginDateWithTime = null;
            if (isset($prevTimeScheduleDtl)) {
                $prevEndDateWithTime    = $prevTimeScheduleDtl->end_date_time;
                $diffMin                = DateTool::getMinuteBetweenDates($prevEndDateWithTime, $beginDateWithTime) / 2;
                $calcBeginDateWithTime  = DateTool::addMinute($prevEndDateWithTime, $diffMin);
            }

            // 1. ЦАГИЙН ХУВААРИЙН ЭХЛЭХ ЦАГ
            $employeeAttendanceDtl = new EmployeeAttendanceDtl();
            $employeeAttendanceDtl->setConnection($connectionName);
            $employeeAttendanceDtl->begin_date       = $isFirst  ? $minBeginDate : $calcBeginDateWithTime;
            $employeeAttendanceDtl->end_date         = $beginDateWithTime;
            $employeeAttendanceDtl->check_io         = true;
            $employeeAttendanceDtl->type             = EmployeeAttendanceDtl::TYPE_BEFORE_WORK;
            $employeeAttendanceDtl->first_str        = EmployeeAttendanceDtl::INPUT_STR;
            $employeeAttendanceDtl->second_str       = EmployeeAttendanceDtl::INPUT_STR;
            $employeeAttendanceDtl->first_behavior   = EmployeeAttendanceDtl::LIMIT_STR;
            $employeeAttendanceDtl->second_behavior  = EmployeeAttendanceDtl::SCHEDULE_STR;
            $employeeAttendanceDtl->created_by       = $userId;
            $employeeAttendanceDtls->push($employeeAttendanceDtl);

            if ($hasBreakTime) {
                $countTimeScheduleDtlBts = count($timeScheduleDtlBts);
                $hasOneBreakTime = $countTimeScheduleDtlBts === 1;
                foreach ($timeScheduleDtlBts as $key => $timeScheduleDtlBt) {
                    $dtlBtBeginTimeStr       = $timeScheduleDtlBt->begin_time_str;
                    $dtlBtEndTimeStr         = $timeScheduleDtlBt->end_time_str;
                    $dtlBtBeginTimeStrArray  = explode(':', $dtlBtBeginTimeStr);
                    $dtlBtEndTimeStrArray    = explode(':', $dtlBtEndTimeStr);
                    $dtlBtBeginDateWithTime  = DateTool::setDateTime($beginDateWithTime, $dtlBtBeginTimeStrArray[0], $dtlBtBeginTimeStrArray[1]);
                    $dtlBtEndDateWithTime    = DateTool::setDateTime($beginDateWithTime, $dtlBtEndTimeStrArray[0], $dtlBtEndTimeStrArray[1]);

                    $beforeBRTBeginDate = null;
                    $beforeBRTEndDate   = null;
                    $afterBRTBeginDate  = null;
                    $afterBRTEndDate    = null;

                    if ($hasOneBreakTime) {
                        // Нэг завсарлагааны цагийн хуваарьтай бол
                        $beforeBRTBeginDate = $beginDateWithTime;
                        $beforeBRTEndDate   = $dtlBtBeginDateWithTime;
                        $afterBRTBeginDate  = $dtlBtEndDateWithTime;
                        $afterBRTEndDate    = $endDateWithTime;
                    } else {
                        // Олон завсарлагааны цагийн хуваарьтай бол
                        $beforeBRTBeginDate = null;
                        $beforeBRTEndDate   = null;
                        $afterBRTBeginDate  = null;
                        $afterBRTEndDate    = null;
                    }

                    // ЗАВСАРЛАГААНЫ ӨМНӨХ АЖЛЫН ЦАГ
                    $employeeAttendanceDtl = new EmployeeAttendanceDtl();
                    $employeeAttendanceDtl->setConnection($connectionName);
                    $employeeAttendanceDtl->begin_date       = $beforeBRTBeginDate;
                    $employeeAttendanceDtl->end_date         = $beforeBRTEndDate;
                    $employeeAttendanceDtl->check_io         = true;
                    $employeeAttendanceDtl->type             = EmployeeAttendanceDtl::TYPE_WORK;
                    $employeeAttendanceDtl->first_str        = EmployeeAttendanceDtl::INPUT_STR;
                    $employeeAttendanceDtl->second_str       = EmployeeAttendanceDtl::OUTPUT_STR;
                    $employeeAttendanceDtl->first_behavior   = EmployeeAttendanceDtl::SCHEDULE_STR;
                    $employeeAttendanceDtl->second_behavior  = EmployeeAttendanceDtl::SCHEDULE_STR;
                    $employeeAttendanceDtl->admin_late_min   = $admitLateMin;
                    $employeeAttendanceDtl->is_special       = $employee->is_special;
                    $employeeAttendanceDtl->created_by       = $userId;
                    $employeeAttendanceDtls->push($employeeAttendanceDtl);

                    // ЗАВСАРЛААГААНЫ ЦАГ
                    $employeeAttendanceDtl = new EmployeeAttendanceDtl();
                    $employeeAttendanceDtl->setConnection($connectionName);
                    $employeeAttendanceDtl->begin_date       = $dtlBtBeginDateWithTime;
                    $employeeAttendanceDtl->end_date         = $dtlBtEndDateWithTime;
                    $employeeAttendanceDtl->check_io         = $timeScheduleDtlBt->check_io;
                    $employeeAttendanceDtl->type             = EmployeeAttendanceDtl::TYPE_BREAK;
                    $employeeAttendanceDtl->first_str        = EmployeeAttendanceDtl::OUTPUT_STR;
                    $employeeAttendanceDtl->second_str       = EmployeeAttendanceDtl::INPUT_STR;
                    $employeeAttendanceDtl->first_behavior   = EmployeeAttendanceDtl::SCHEDULE_STR;
                    $employeeAttendanceDtl->second_behavior  = EmployeeAttendanceDtl::SCHEDULE_STR;
                    $employeeAttendanceDtl->admin_late_min   = $admitLateMin;
                    $employeeAttendanceDtl->created_by       = $userId;
                    $employeeAttendanceDtls->push($employeeAttendanceDtl);

                    // ЗАВСАРЛАГААНЫ ДАРААХ АЖЛЫН ЦАГ
                    $employeeAttendanceDtl = new EmployeeAttendanceDtl();
                    $employeeAttendanceDtl->setConnection($connectionName);
                    $employeeAttendanceDtl->begin_date       = $afterBRTBeginDate;
                    $employeeAttendanceDtl->end_date         = $afterBRTEndDate;
                    $employeeAttendanceDtl->check_io         = true;
                    $employeeAttendanceDtl->type             = EmployeeAttendanceDtl::TYPE_WORK;
                    $employeeAttendanceDtl->first_str        = EmployeeAttendanceDtl::INPUT_STR;
                    $employeeAttendanceDtl->second_str       = EmployeeAttendanceDtl::OUTPUT_STR;
                    $employeeAttendanceDtl->first_behavior   = EmployeeAttendanceDtl::SCHEDULE_STR;
                    $employeeAttendanceDtl->second_behavior  = EmployeeAttendanceDtl::SCHEDULE_STR;
                    $employeeAttendanceDtl->admin_late_min   = $admitLateMin;
                    $employeeAttendanceDtl->is_special       = $employee->is_special;
                    $employeeAttendanceDtl->created_by       = $userId;
                    $employeeAttendanceDtls->push($employeeAttendanceDtl);
                }
            } else {
                // АЖЛЫН ЦАГ
                $beginDateWithZeroTime = DateTool::setZeroTime($beginDateWithTime);
                $endDateWithZeroTime   = DateTool::setZeroTime($endDateWithTime);

                if ($beginDateWithZeroTime === $endDateWithZeroTime) {
                    $employeeAttendanceDtl = new EmployeeAttendanceDtl();
                    $employeeAttendanceDtl->setConnection($connectionName);
                    $employeeAttendanceDtl->begin_date       = $beginDateWithTime;
                    $employeeAttendanceDtl->end_date         = $endDateWithTime;
                    $employeeAttendanceDtl->check_io         = true;
                    $employeeAttendanceDtl->type             = EmployeeAttendanceDtl::TYPE_WORK;
                    $employeeAttendanceDtl->first_str        = EmployeeAttendanceDtl::INPUT_STR;
                    $employeeAttendanceDtl->second_str       = EmployeeAttendanceDtl::OUTPUT_STR;
                    $employeeAttendanceDtl->first_behavior   = EmployeeAttendanceDtl::SCHEDULE_STR;
                    $employeeAttendanceDtl->second_behavior  = EmployeeAttendanceDtl::SCHEDULE_STR;
                    $employeeAttendanceDtl->admin_late_min   = $admitLateMin;
                    $employeeAttendanceDtl->is_special       = $employee->is_special;
                    $employeeAttendanceDtl->created_by       = $userId;
                    $employeeAttendanceDtls->push($employeeAttendanceDtl);
                } else {
                    // ӨДӨР ДАМНАСАН ЦАГИЙН ХУВААРЬТАЙ ТОХИОЛДОЛД
                    $isFirst = true;
                    $islast  = false;
                    while ($beginDateWithZeroTime <= $endDateWithZeroTime) {
                        $islast = $beginDateWithZeroTime == $endDateWithZeroTime;
                        $nextBeginDateWithZeroTime = DateTool::addDays($beginDateWithZeroTime, 1);
                        if ($isFirst) {
                            $shiftBeginDate        = $beginDateWithTime;
                            $shiftEndDate          = $nextBeginDateWithZeroTime;
                            $isFirst = false;
                        } else if ($islast) {
                            $shiftBeginDate        = $endDateWithZeroTime;
                            $shiftEndDate          = $endDateWithTime;
                        } else {
                            $shiftBeginDate        = $beginDateWithZeroTime;
                            $shiftEndDate          = $nextBeginDateWithZeroTime;
                        }

                        $employeeAttendanceDtl = new EmployeeAttendanceDtl();
                        $employeeAttendanceDtl->setConnection($connectionName);
                        $employeeAttendanceDtl->begin_date       = $shiftBeginDate;
                        $employeeAttendanceDtl->end_date         = $shiftEndDate;
                        $employeeAttendanceDtl->check_io         = true;
                        $employeeAttendanceDtl->type             = EmployeeAttendanceDtl::TYPE_WORK;
                        $employeeAttendanceDtl->first_str        = EmployeeAttendanceDtl::INPUT_STR;
                        $employeeAttendanceDtl->second_str       = EmployeeAttendanceDtl::OUTPUT_STR;
                        $employeeAttendanceDtl->first_behavior   = EmployeeAttendanceDtl::SCHEDULE_STR;
                        $employeeAttendanceDtl->second_behavior  = EmployeeAttendanceDtl::SCHEDULE_STR;
                        $employeeAttendanceDtl->admin_late_min   = $admitLateMin;
                        $employeeAttendanceDtl->is_special       = $employee->is_special;
                        $employeeAttendanceDtl->created_by       = $userId;
                        $employeeAttendanceDtls->push($employeeAttendanceDtl);
                        $beginDateWithZeroTime = $nextBeginDateWithZeroTime;
                    }
                }
            }

            $calcEndDateWithTime = null;
            if (isset($nextTimeScheduleDtl)) {
                $nextBeginDateWithTime  = $nextTimeScheduleDtl->begin_date_time;
                $diffMin              = DateTool::getMinuteBetweenDates($endDateWithTime, $nextBeginDateWithTime) / 2;
                $calcEndDateWithTime  = DateTool::addMinute($endDateWithTime, $diffMin);
            }

            // 2. ЦАГИЙН ХУВААРЬ ДУУСАХ ЦАГ
            $employeeAttendanceDtl = new EmployeeAttendanceDtl();
            $employeeAttendanceDtl->setConnection($connectionName);
            $employeeAttendanceDtl->begin_date          = $endDateWithTime;
            $employeeAttendanceDtl->end_date            = $isLast ? $maxEndDate : $calcEndDateWithTime;
            $employeeAttendanceDtl->check_io            = true;
            $employeeAttendanceDtl->type                = EmployeeAttendanceDtl::TYPE_AFTER_WORK;
            $employeeAttendanceDtl->first_str           = EmployeeAttendanceDtl::OUTPUT_STR;
            $employeeAttendanceDtl->second_str          = EmployeeAttendanceDtl::OUTPUT_STR;
            $employeeAttendanceDtl->first_behavior      = EmployeeAttendanceDtl::SCHEDULE_STR;
            $employeeAttendanceDtl->second_behavior     = EmployeeAttendanceDtl::LIMIT_STR;
            $employeeAttendanceDtl->admin_late_min      = $admitLateMin;
            $employeeAttendanceDtl->created_by          = $userId;
            $employeeAttendanceDtls->push($employeeAttendanceDtl);
        }

        $tempEmployeeAttendanceDtls = collect();
        foreach ($employeeAttendanceDtls as $key => $employeeAttendanceDtl) {
            if ($employeeAttendanceDtl->getDifferenceMinutes() > 0)
                $tempEmployeeAttendanceDtls->push($employeeAttendanceDtl);
        }

        $tempEmployeeAttendanceDtls = $tempEmployeeAttendanceDtls->sortBy('begin_date')->values();
        return $tempEmployeeAttendanceDtls;
    }

    public function createEmployeeAttendances($salaryDate, $employeeId, $ioStr, $timeStr, $scheduleStr, $rawAttendanceStrids, $employeeAttendanceDtls, $timeRequests) {
        $user                        = $this->busAttendance->user;
        $userId                      = $user->id;
        $newEmployeeAttendances      = collect();
        $countEmployeeAttendanceDtls = count($employeeAttendanceDtls);

        // DTLS үүсээгүй бол тооцоолол хийгдэхгүй.
        if ($countEmployeeAttendanceDtls === 0)
            return $newEmployeeAttendances;

        foreach ($employeeAttendanceDtls as $key => $employeeAttendanceDtl)
            $employeeAttendanceDtl->setAttendanceDate();

        // PREPARE PREV AND NEXT ATTANDANCE
        $sortedEmployeeAttendanceDtls = $employeeAttendanceDtls->sortBy(EmployeeAttendanceDtl::ATTENDANCE_DATE)->values();
        foreach ($sortedEmployeeAttendanceDtls as $key => $sortedEmployeeAttendanceDtl) {
            $prevEmployeeAttendanceDtl = $key === 0 ? null : (object) $sortedEmployeeAttendanceDtls[$key - 1];
            $sortedEmployeeAttendanceDtl->prev_employee_attendance_dtl = $prevEmployeeAttendanceDtl;

            $nextEmployeeAttendanceDtl = $key === count($sortedEmployeeAttendanceDtls) - 1 ? null : (object) $sortedEmployeeAttendanceDtls[$key + 1];
            $sortedEmployeeAttendanceDtl->next_employee_attendance_dtl = $nextEmployeeAttendanceDtl;
        }

        // PREV БОЛОН NEXT EmployeeAttendanceDtl SET ХИЙГДСЭНИЙ ДАРАА АЖИЛЛАХ ЁСТОЙ
        foreach ($sortedEmployeeAttendanceDtls as $subKey3 => $value) {
            $value->setStatus();
        }
        $employeeAttendanceDtlGroups = $sortedEmployeeAttendanceDtls->groupBy(EmployeeAttendanceDtl::ATTENDANCE_DATE);

        foreach ($employeeAttendanceDtlGroups as $attDate => $employeeAttendanceDtlGroupValues) {
            if ($attDate != $salaryDate)
                continue;
            $employeeAttendance = new EmployeeAttendance();
            $employeeAttendance->setConnection($this->busAttendance->connection_name);
            $employeeAttendance->employee_id              = $employeeId;
            $employeeAttendance->att_date                 = $attDate;
            $employeeAttendance->io_string                = $ioStr;
            $employeeAttendance->time_string              = $timeStr;
            $employeeAttendance->schedule_string          = $scheduleStr;
            $employeeAttendance->device_attendance_strids = $rawAttendanceStrids;
            $employeeAttendance->schedule_time            = 0;
            $employeeAttendance->created_by               = $userId;

            $employeeAttendanceDtlGroupValues = $this->getEmployeeAttendanceDtlsByTimeRequest($employeeAttendanceDtlGroupValues, $timeRequests);

            // PREV БОЛОН NEXT EmployeeAttendanceDtl SET ХИЙГДСЭНИЙ ДАРАА АЖИЛЛАХ ЁСТОЙ
            $tempEmployeeAttendanceDtls = collect();
            foreach ($employeeAttendanceDtlGroupValues as $subKey3 => $employeeAttendanceDtlGroupValue) {
                if ($employeeAttendanceDtlGroupValue->getDifferenceMinutes() == 0)
                    continue;
                $employeeAttendanceDtlGroupValue->setBeginAndEndSign();
                $employeeAttendanceDtlGroupValue->clearOtherColumns();
                $employeeAttendance->schedule_time += $employeeAttendanceDtlGroupValue->getScheduleTime();
                $employeeAttendance->setTimeColumn($employeeAttendanceDtlGroupValue);
                $tempEmployeeAttendanceDtls->push($employeeAttendanceDtlGroupValue);
            }
            $employeeAttendance->save();
            $employeeAttendance->employee_attendance_dtls()->saveMany($tempEmployeeAttendanceDtls);
            $newEmployeeAttendances->push($employeeAttendance);
        }
        return $newEmployeeAttendances;
    }

    public function getEmployeeAttendanceDtlsByTimeRequest($employeeAttendanceDtls, $timeRequests) {
        $resultEmployeeAttendaceDtls = collect();
        foreach ($employeeAttendanceDtls as $key => $employeeAttendanceDtl) {
            $timeRequest = $timeRequests->first(function ($request, $key) use ($employeeAttendanceDtl) {
                return ($employeeAttendanceDtl->begin_date <= $request->begin_date && $employeeAttendanceDtl->end_date >= $request->end_date) ||
                ($employeeAttendanceDtl->begin_date > $request->begin_date && $employeeAttendanceDtl->end_date < $request->end_date) ||
                ($employeeAttendanceDtl->begin_date > $request->begin_date && $employeeAttendanceDtl->end_date >= $request->end_date) ||
                ($employeeAttendanceDtl->begin_date <= $request->begin_date && $employeeAttendanceDtl->end_date < $request->end_date);
            });

            if (isset($timeRequest)) {
                $beginDate  = DateTool::setZeroSecond($timeRequest->begin_date);
                $endDate    = DateTool::setZeroSecond($timeRequest->end_date);
                $attStr     = EmployeeAttendanceDtl::REQUEST_STR;
                $attStatus  = $timeRequest->att_status;
                if ($employeeAttendanceDtl->type === EmployeeAttendanceDtl::TYPE_BREAK || $employeeAttendanceDtl->type === EmployeeAttendanceDtl::TYPE_BEFORE_WORK || $employeeAttendanceDtl->type === EmployeeAttendanceDtl::TYPE_AFTER_WORK) {
                    $resultEmployeeAttendaceDtls->push($employeeAttendanceDtl);
                } else {
                    $newEmployeeAttendanceDtls   = $this->getNewEmployeeAttendanceDtlsByAttDtl($employeeAttendanceDtl, $beginDate, $endDate, $attStr, $attStatus);
                    if (!isset($newEmployeeAttendanceDtls) || count($newEmployeeAttendanceDtls) == 0)
                        $newEmployeeAttendanceDtls->push($employeeAttendanceDtl);
                    $resultEmployeeAttendaceDtls = $resultEmployeeAttendaceDtls->merge($newEmployeeAttendanceDtls);
                }
            } else {
                $resultEmployeeAttendaceDtls->push($employeeAttendanceDtl);
            }
        }
        return $resultEmployeeAttendaceDtls;
    }

    public function getNewEmployeeAttendanceDtlsByAttDtl($employeeAttendanceDtl, $pBeginDate, $pEndDate, $attStr, $attStatus = null) {
        $connectionName                = $this->busAttendance->connection_name;
        $user                          = $this->busAttendance->user;
        $userId                        = $user->id;
        $trBeginDate                   = DateTool::setZeroSecond($pBeginDate);
        $trEndDate                     = DateTool::setZeroSecond($pEndDate);
        $attBeginDate                  = $employeeAttendanceDtl->begin_date;
        $attEndDate                    = $employeeAttendanceDtl->end_date;
        $isDeleteEmployeeAttendanceDtl = false;
        $newEmployeeAttendaceDtls      = collect();
        if ($employeeAttendanceDtl->type === EmployeeAttendanceDtl::TYPE_BREAK || $employeeAttendanceDtl->type === EmployeeAttendanceDtl::TYPE_BEFORE_WORK || $employeeAttendanceDtl->type === EmployeeAttendanceDtl::TYPE_AFTER_WORK) {
            return $newEmployeeAttendaceDtls;
        }

        if ($attBeginDate <= $trBeginDate && $attEndDate >= $trEndDate) {
            $isDeleteEmployeeAttendanceDtl = true;
            $newEmployeeAttendanceDtl = new EmployeeAttendanceDtl();
            $newEmployeeAttendanceDtl->setConnection($connectionName);
            $newEmployeeAttendanceDtl->employee_attendance_id = $employeeAttendanceDtl->employee_attendance_id;
            $newEmployeeAttendanceDtl->begin_date             = $attBeginDate;
            $newEmployeeAttendanceDtl->end_date               = $trBeginDate;
            $newEmployeeAttendanceDtl->type                   = $employeeAttendanceDtl->type;
            $newEmployeeAttendanceDtl->first_str              = $employeeAttendanceDtl->first_str;
            $newEmployeeAttendanceDtl->second_str             = EmployeeAttendanceDtl::OUTPUT_STR;
            $newEmployeeAttendanceDtl->first_behavior         = $employeeAttendanceDtl->first_behavior;
            $newEmployeeAttendanceDtl->second_behavior        = $attStr;
            $newEmployeeAttendanceDtl->admin_late_min         = $employeeAttendanceDtl->admin_late_min;
            $newEmployeeAttendanceDtl->is_special             = $employeeAttendanceDtl->is_special;
            $newEmployeeAttendanceDtl->attendance_status      = $employeeAttendanceDtl->attendance_status;
            $newEmployeeAttendanceDtl->decision_type          = 0;
            $newEmployeeAttendanceDtl->created_by             = $userId;
            $newEmployeeAttendaceDtls->push($newEmployeeAttendanceDtl);

            $newEmployeeAttendanceDtl = new EmployeeAttendanceDtl();
            $newEmployeeAttendanceDtl->setConnection($connectionName);
            $newEmployeeAttendanceDtl->employee_attendance_id = $employeeAttendanceDtl->employee_attendance_id;
            $newEmployeeAttendanceDtl->begin_date             = $trBeginDate;
            $newEmployeeAttendanceDtl->end_date               = $trEndDate;
            $newEmployeeAttendanceDtl->type                   = $employeeAttendanceDtl->type;
            $newEmployeeAttendanceDtl->first_str              = EmployeeAttendanceDtl::INPUT_STR;
            $newEmployeeAttendanceDtl->second_str             = EmployeeAttendanceDtl::OUTPUT_STR;
            $newEmployeeAttendanceDtl->first_behavior         = $attStr;
            $newEmployeeAttendanceDtl->second_behavior        = $attStr;
            $newEmployeeAttendanceDtl->admin_late_min         = $employeeAttendanceDtl->admin_late_min;
            $newEmployeeAttendanceDtl->is_special             = $employeeAttendanceDtl->is_special;
            $newEmployeeAttendanceDtl->attendance_status      = $attStatus;
            $newEmployeeAttendanceDtl->decision_type          = 0;
            $newEmployeeAttendanceDtl->created_by             = $userId;
            $newEmployeeAttendaceDtls->push($newEmployeeAttendanceDtl);

            $newEmployeeAttendanceDtl = new EmployeeAttendanceDtl();
            $newEmployeeAttendanceDtl->setConnection($connectionName);
            $newEmployeeAttendanceDtl->employee_attendance_id = $employeeAttendanceDtl->employee_attendance_id;
            $newEmployeeAttendanceDtl->begin_date             = $trEndDate;
            $newEmployeeAttendanceDtl->end_date               = $attEndDate;
            $newEmployeeAttendanceDtl->type                   = $employeeAttendanceDtl->type;
            $newEmployeeAttendanceDtl->first_str              = EmployeeAttendanceDtl::INPUT_STR;
            $newEmployeeAttendanceDtl->second_str             = $employeeAttendanceDtl->second_str;
            $newEmployeeAttendanceDtl->first_behavior         = $attStr;
            $newEmployeeAttendanceDtl->second_behavior        = $employeeAttendanceDtl->second_behavior;
            $newEmployeeAttendanceDtl->admin_late_min         = $employeeAttendanceDtl->admin_late_min;
            $newEmployeeAttendanceDtl->is_special             = $employeeAttendanceDtl->is_special;
            $newEmployeeAttendanceDtl->attendance_status      = $employeeAttendanceDtl->attendance_status;
            $newEmployeeAttendanceDtl->decision_type          = 0;
            $newEmployeeAttendanceDtl->created_by             = $userId;
            $newEmployeeAttendaceDtls->push($newEmployeeAttendanceDtl);
        } else if (($attBeginDate > $trBeginDate && $attEndDate < $trEndDate)
            || ($attBeginDate >= $trBeginDate && $attEndDate < $trEndDate)
            || ($attBeginDate > $trBeginDate && $attEndDate <= $trEndDate)) {
            $isDeleteEmployeeAttendanceDtl = true;
            $newEmployeeAttendanceDtl = new EmployeeAttendanceDtl();
            $newEmployeeAttendanceDtl->setConnection($connectionName);
            $newEmployeeAttendanceDtl->employee_attendance_id = $employeeAttendanceDtl->employee_attendance_id;
            $newEmployeeAttendanceDtl->begin_date             = $attBeginDate;
            $newEmployeeAttendanceDtl->end_date               = $attEndDate;
            $newEmployeeAttendanceDtl->type                   = $employeeAttendanceDtl->type;
            $newEmployeeAttendanceDtl->first_str              = EmployeeAttendanceDtl::INPUT_STR;
            $newEmployeeAttendanceDtl->second_str             = EmployeeAttendanceDtl::OUTPUT_STR;
            $newEmployeeAttendanceDtl->first_behavior         = $attStr;
            $newEmployeeAttendanceDtl->second_behavior        = $attStr;
            $newEmployeeAttendanceDtl->admin_late_min         = $employeeAttendanceDtl->admin_late_min;
            $newEmployeeAttendanceDtl->is_special             = $employeeAttendanceDtl->is_special;
            $newEmployeeAttendanceDtl->attendance_status      = $attStatus;
            $newEmployeeAttendanceDtl->decision_type          = 0;
            $newEmployeeAttendanceDtl->created_by             = $userId;
            $newEmployeeAttendaceDtls->push($newEmployeeAttendanceDtl);
        } else if ($attBeginDate > $trBeginDate && $attBeginDate < $trEndDate && $attEndDate > $trEndDate) {
            $isDeleteEmployeeAttendanceDtl = true;
            $newEmployeeAttendanceDtl = new EmployeeAttendanceDtl();
            $newEmployeeAttendanceDtl->setConnection($connectionName);
            $newEmployeeAttendanceDtl->employee_attendance_id = $employeeAttendanceDtl->employee_attendance_id;
            $newEmployeeAttendanceDtl->begin_date             = $attBeginDate;
            $newEmployeeAttendanceDtl->end_date               = $trEndDate;
            $newEmployeeAttendanceDtl->type                   = $employeeAttendanceDtl->type;
            $newEmployeeAttendanceDtl->first_str              = $employeeAttendanceDtl->first_str;
            $newEmployeeAttendanceDtl->second_str             = EmployeeAttendanceDtl::OUTPUT_STR;
            $newEmployeeAttendanceDtl->first_behavior         = $employeeAttendanceDtl->first_behavior;
            $newEmployeeAttendanceDtl->second_behavior        = $attStr;
            $newEmployeeAttendanceDtl->admin_late_min         = $employeeAttendanceDtl->admin_late_min;
            $newEmployeeAttendanceDtl->is_special             = $employeeAttendanceDtl->is_special;
            $newEmployeeAttendanceDtl->attendance_status      = $attStatus;
            $newEmployeeAttendanceDtl->decision_type          = 0;
            $newEmployeeAttendanceDtl->created_by             = $userId;
            $newEmployeeAttendaceDtls->push($newEmployeeAttendanceDtl);

            $newEmployeeAttendanceDtl = new EmployeeAttendanceDtl();
            $newEmployeeAttendanceDtl->setConnection($connectionName);
            $newEmployeeAttendanceDtl->employee_attendance_id = $employeeAttendanceDtl->employee_attendance_id;
            $newEmployeeAttendanceDtl->begin_date             = $trEndDate;
            $newEmployeeAttendanceDtl->end_date               = $attEndDate;
            $newEmployeeAttendanceDtl->type                   = $employeeAttendanceDtl->type;
            $newEmployeeAttendanceDtl->first_str              = EmployeeAttendanceDtl::INPUT_STR;
            $newEmployeeAttendanceDtl->second_str             = $employeeAttendanceDtl->second_str;
            $newEmployeeAttendanceDtl->first_behavior         = $attStr;
            $newEmployeeAttendanceDtl->second_behavior        = $employeeAttendanceDtl->second_behavior;
            $newEmployeeAttendanceDtl->admin_late_min         = $employeeAttendanceDtl->admin_late_min;
            $newEmployeeAttendanceDtl->is_special             = $employeeAttendanceDtl->is_special;
            $newEmployeeAttendanceDtl->attendance_status      = $employeeAttendanceDtl->attendance_status;
            $newEmployeeAttendanceDtl->decision_type          = 0;
            $newEmployeeAttendanceDtl->created_by             = $userId;
            $newEmployeeAttendaceDtls->push($newEmployeeAttendanceDtl);
        } else if ($attBeginDate < $trBeginDate && $attEndDate > $trBeginDate && $attEndDate < $trEndDate) {
            $isDeleteEmployeeAttendanceDtl = true;
            $newEmployeeAttendanceDtl = new EmployeeAttendanceDtl();
            $newEmployeeAttendanceDtl->setConnection($connectionName);
            $newEmployeeAttendanceDtl->employee_attendance_id = $employeeAttendanceDtl->employee_attendance_id;
            $newEmployeeAttendanceDtl->begin_date             = $attBeginDate;
            $newEmployeeAttendanceDtl->end_date               = $trBeginDate;
            $newEmployeeAttendanceDtl->type                   = $employeeAttendanceDtl->type;
            $newEmployeeAttendanceDtl->first_str              = $employeeAttendanceDtl->first_str;
            $newEmployeeAttendanceDtl->second_str             = EmployeeAttendanceDtl::OUTPUT_STR;
            $newEmployeeAttendanceDtl->first_behavior         = $employeeAttendanceDtl->first_behavior;
            $newEmployeeAttendanceDtl->second_behavior        = $attStr;
            $newEmployeeAttendanceDtl->admin_late_min         = $employeeAttendanceDtl->admin_late_min;
            $newEmployeeAttendanceDtl->is_special             = $employeeAttendanceDtl->is_special;
            $newEmployeeAttendanceDtl->attendance_status      = $employeeAttendanceDtl->attendance_status;
            $newEmployeeAttendanceDtl->decision_type          = 0;
            $newEmployeeAttendanceDtl->created_by             = $userId;
            $newEmployeeAttendaceDtls->push($newEmployeeAttendanceDtl);

            $newEmployeeAttendanceDtl = new EmployeeAttendanceDtl();
            $newEmployeeAttendanceDtl->setConnection($connectionName);
            $newEmployeeAttendanceDtl->employee_attendance_id = $employeeAttendanceDtl->employee_attendance_id;
            $newEmployeeAttendanceDtl->begin_date             = $trBeginDate;
            $newEmployeeAttendanceDtl->end_date               = $attEndDate;
            $newEmployeeAttendanceDtl->type                   = $employeeAttendanceDtl->type;
            $newEmployeeAttendanceDtl->first_str              = EmployeeAttendanceDtl::INPUT_STR;
            $newEmployeeAttendanceDtl->second_str             = $employeeAttendanceDtl->second_str;
            $newEmployeeAttendanceDtl->first_behavior         = $attStr;
            $newEmployeeAttendanceDtl->second_behavior        = $employeeAttendanceDtl->second_behavior;
            $newEmployeeAttendanceDtl->admin_late_min         = $employeeAttendanceDtl->admin_late_min;
            $newEmployeeAttendanceDtl->is_special             = $employeeAttendanceDtl->is_special;
            $newEmployeeAttendanceDtl->attendance_status      = $attStatus;
            $newEmployeeAttendanceDtl->decision_type          = 0;
            $newEmployeeAttendanceDtl->created_by             = $userId;
            $newEmployeeAttendaceDtls->push($newEmployeeAttendanceDtl);
        }

        if ($isDeleteEmployeeAttendanceDtl && $employeeAttendanceDtl->id) {
            $employeeAttendanceDtl->delete();
        }

        return $newEmployeeAttendaceDtls;
    }

    public function createEmployeeAttendanceDtls($employee, $rawAttendances, $employeeAttendanceDtls) {
        $cn                         = $this->busAttendance->connection_name;    
        $user                       = $this->busAttendance->user;
        $userId                     = $user->id;
        $lastEmployeeAttendanceDtl  = null;
        $lastEmployeeAttendanceDtls = collect();
        $isLateType                 = true;
        foreach ($employeeAttendanceDtls as $key => $employeeAttendanceDtl) {
            $filterDeviceAttendances = [];
            foreach ($rawAttendances as $daKey => $value) {
                if (EmployeeAttendanceDtl::TYPE_BEFORE_WORK === $employeeAttendanceDtl->type) {
                    if ($value->register_date >= $employeeAttendanceDtl->begin_date && $value->register_date < $employeeAttendanceDtl->end_date) {
                        $filterDeviceAttendances[$daKey] = $value;
                    }
                } else {
                    if ($value->register_date >= $employeeAttendanceDtl->begin_date && $value->register_date <= $employeeAttendanceDtl->end_date) {
                        $filterDeviceAttendances[$daKey] = $value;
                    }
                }
            }
            // REMOVE
            // foreach ($filterDeviceAttendances as $fdaKey => $value) {
            //     unset($deviceAttendances[$fdaKey]);
            // }

            $countFilterDeviceAttendances = count($filterDeviceAttendances);
            if ($countFilterDeviceAttendances == 0) {
                switch ($employeeAttendanceDtl->type) {
                    case EmployeeAttendanceDtl::TYPE_OVER:
                    case EmployeeAttendanceDtl::TYPE_WORK:
                    case EmployeeAttendanceDtl::TYPE_BREAK:
                        $lastEmployeeAttendanceDtls->push($employeeAttendanceDtl);
                        break;
                    default:
                        break;
                }
            } else {
                $filterDeviceAttendances = array_values($filterDeviceAttendances);
                foreach ($filterDeviceAttendances as $fdaKey2 => $value) {
                    $prevFilterDeviceAttendance = $fdaKey2 === 0 ? null : $filterDeviceAttendances[$fdaKey2 - 1];
                    $nextFilterDeviceAttendance = $fdaKey2 === $countFilterDeviceAttendances - 1 ? null : $filterDeviceAttendances[$fdaKey2 + 1];

                    $lastEmployeeAttendanceDtl = new EmployeeAttendanceDtl();
                    $lastEmployeeAttendanceDtl->setConnection($cn);
                    $lastEmployeeAttendanceDtl->type            = $employeeAttendanceDtl->type;
                    $lastEmployeeAttendanceDtl->check_io        = $employeeAttendanceDtl->check_io;
                    $lastEmployeeAttendanceDtl->admin_late_min  = $employeeAttendanceDtl->admin_late_min;
                    $lastEmployeeAttendanceDtl->is_special      = $employee->is_special;
                    $lastEmployeeAttendanceDtl->decision_type   = $employeeAttendanceDtl->decisionType;
                    $lastEmployeeAttendanceDtl->created_by      = $userId;
                    if ($fdaKey2 === 0) {
                        switch ($employeeAttendanceDtl->type) {
                            case EmployeeAttendanceDtl::TYPE_BEFORE_WORK:
                                $lastEmployeeAttendanceDtl->begin_date      = $value->register_date;
                                $lastEmployeeAttendanceDtl->end_date        = $nextFilterDeviceAttendance ? $nextFilterDeviceAttendance->register_date : $employeeAttendanceDtl->end_date;
                                $lastEmployeeAttendanceDtl->first_str       = $value->io_str;
                                $lastEmployeeAttendanceDtl->second_str      = $nextFilterDeviceAttendance ? $nextFilterDeviceAttendance->io_str : $employeeAttendanceDtl->second_str;
                                $lastEmployeeAttendanceDtl->first_behavior  = EmployeeAttendanceDtl::MARK_STR;
                                $lastEmployeeAttendanceDtl->second_behavior = $nextFilterDeviceAttendance ? EmployeeAttendanceDtl::MARK_STR : $employeeAttendanceDtl->second_behavior;
                                break;
                            case EmployeeAttendanceDtl::TYPE_OVER:
                            case EmployeeAttendanceDtl::TYPE_WORK:
                                $lastEmployeeAttendanceDtl->is_late_type = $isLateType;
                                $isLateType = false;
                            case EmployeeAttendanceDtl::TYPE_AFTER_WORK:
                            case EmployeeAttendanceDtl::TYPE_BREAK:
                                $lastEmployeeAttendanceDtl->begin_date      = $employeeAttendanceDtl->begin_date;
                                $lastEmployeeAttendanceDtl->end_date        = $value->register_date;
                                $lastEmployeeAttendanceDtl->first_str       = $employeeAttendanceDtl->first_str;
                                $lastEmployeeAttendanceDtl->second_str      = $value->io_str;
                                $lastEmployeeAttendanceDtl->first_behavior  = EmployeeAttendanceDtl::SCHEDULE_STR;
                                $lastEmployeeAttendanceDtl->second_behavior = EmployeeAttendanceDtl::MARK_STR;
                                break;
                            default:
                                break;
                        }
                    } else {
                        switch ($employeeAttendanceDtl->type) {
                            case EmployeeAttendanceDtl::TYPE_BEFORE_WORK:
                                if ($fdaKey2%2 != 0)
                                    break;
                                $lastEmployeeAttendanceDtl->begin_date              = $value->register_date;
                                $lastEmployeeAttendanceDtl->end_date                = $nextFilterDeviceAttendance ? $nextFilterDeviceAttendance->register_date : $employeeAttendanceDtl->end_date;
                                $lastEmployeeAttendanceDtl->first_str               = $value->io_str;
                                $lastEmployeeAttendanceDtl->second_str              = $nextFilterDeviceAttendance ? $nextFilterDeviceAttendance->io_str : $employeeAttendanceDtl->second_str;
                                $lastEmployeeAttendanceDtl->first_behavior          = EmployeeAttendanceDtl::MARK_STR;
                                $lastEmployeeAttendanceDtl->second_behavior         = $nextFilterDeviceAttendance ? EmployeeAttendanceDtl::MARK_STR : $employeeAttendanceDtl->second_behavior;
                                break;
                            case EmployeeAttendanceDtl::TYPE_OVER:
                            case EmployeeAttendanceDtl::TYPE_WORK:
                            case EmployeeAttendanceDtl::TYPE_AFTER_WORK:
                            case EmployeeAttendanceDtl::TYPE_BREAK:
                                $lastEmployeeAttendanceDtl->begin_date              = $prevFilterDeviceAttendance->register_date;
                                $lastEmployeeAttendanceDtl->end_date                = $value->register_date;
                                $lastEmployeeAttendanceDtl->first_str               = $prevFilterDeviceAttendance->io_str;
                                $lastEmployeeAttendanceDtl->second_str              = $value->io_str;
                                $lastEmployeeAttendanceDtl->first_behavior          = EmployeeAttendanceDtl::MARK_STR;
                                $lastEmployeeAttendanceDtl->second_behavior         = EmployeeAttendanceDtl::MARK_STR;
                                break;
                            default:
                                break;
                        }
                    }
                    $diffMin = $lastEmployeeAttendanceDtl->getDiffMinutes();
                    if ($diffMin > 0)
                        $lastEmployeeAttendanceDtls->push($lastEmployeeAttendanceDtl);
                }

                if ($employeeAttendanceDtl->type === EmployeeAttendanceDtl::TYPE_OVER || $employeeAttendanceDtl->type === EmployeeAttendanceDtl::TYPE_WORK || $employeeAttendanceDtl->type === EmployeeAttendanceDtl::TYPE_BREAK) {
                    $anotherEmployeeAttendanceDtl = new EmployeeAttendanceDtl();
                    $anotherEmployeeAttendanceDtl->setConnection($cn);
                    $anotherEmployeeAttendanceDtl->type            = $employeeAttendanceDtl->type;
                    $anotherEmployeeAttendanceDtl->check_io        = $employeeAttendanceDtl->check_io;
                    $anotherEmployeeAttendanceDtl->admin_late_min  = $employeeAttendanceDtl->admin_late_min;
                    $anotherEmployeeAttendanceDtl->decision_type   = $employeeAttendanceDtl->decisionType;
                    $anotherEmployeeAttendanceDtl->begin_date      = $value->register_date;
                    $anotherEmployeeAttendanceDtl->end_date        = $employeeAttendanceDtl->end_date;
                    $anotherEmployeeAttendanceDtl->first_str       = $value->io_str;
                    $anotherEmployeeAttendanceDtl->second_str      = $employeeAttendanceDtl->second_str;
                    $anotherEmployeeAttendanceDtl->first_behavior  = EmployeeAttendanceDtl::MARK_STR;
                    $anotherEmployeeAttendanceDtl->second_behavior = EmployeeAttendanceDtl::SCHEDULE_STR;
                    $anotherEmployeeAttendanceDtl->is_special      = $employee->is_special;
                    $anotherEmployeeAttendanceDtl->created_by      = $userId;

                    $diffMin = $anotherEmployeeAttendanceDtl->getDiffMinutes();
                    if ($diffMin > 0)
                        $lastEmployeeAttendanceDtls->push($anotherEmployeeAttendanceDtl);
                }
            }
        }
        return $lastEmployeeAttendanceDtls;
    }
    }
