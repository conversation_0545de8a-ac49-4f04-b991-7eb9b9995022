<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetBankRequest extends FormRequest
{
    const LIMIT             = 'limit';  
    const FILTER            = 'filter';
    const FILTER_NAME       = 'name';
    const FILTER_SHORT_NAME = 'short_name';
    const SORT              = 'sort';
    const SORT_FIELD        = 'field';
    const SORT_TYPE         = 'type';    

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'limit'             => 'nullable|integer',
            'filter'            => 'nullable|array',
            'filter.name'       => ['required_with:filter', 'string', 'max:255'],
            'filter.short_name' => ['required_with:filter', 'string', 'max:255'],
            'sort'              => 'nullable|array',
            'sort.field'        => ['required_with:sort', 'string', 'in:id,name,short_name'],
            'sort.type'         => ['nullable', 'string', 'in:asc,desc'],
        ];
    }
}
