<?php

namespace App\Services\Time;

use App\Http\Tools\DateTool;
use App\Models\Customer\Time\Holiday;

class HolidayService
{
    public function getHolidatesByBetweenDates($connectionName, $beginDate, $endDate) {;
        $holidays = Holiday::on($connectionName)
            ->whereBetween(Holiday::BEGIND_DATE, [$beginDate, $endDate])
            ->orWhereBetween(Holiday::END_DATE, [$beginDate, $endDate])
            ->get();

        $holidates = [];
        foreach ($holidays as $key => $value) {
            while ($value->begin_date <= $value->end_date) {
                $holidates[] = $value->begin_date;
                $value->begin_date = DateTool::addDays($value->begin_date, 1)->format('Y-m-d');
            }
        }
        return $holidates;
    }
}
