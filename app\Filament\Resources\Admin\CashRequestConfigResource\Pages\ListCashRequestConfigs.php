<?php

namespace App\Filament\Resources\Admin\CashRequestConfigResource\Pages;

use App\Filament\Resources\Admin\CashRequestConfigResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCashRequestConfigs extends ListRecords
{
    protected static string $resource = CashRequestConfigResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
