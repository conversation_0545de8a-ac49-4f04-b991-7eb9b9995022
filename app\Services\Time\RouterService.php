<?php

namespace App\Service\Time;

use App\Models\Customer\Time\Router;
use App\Models\Customer\Time\RouterDtl;
use App\Models\Constant\ConstData;
use App\Exceptions\SystemException;

use Illuminate\Database\Eloquent\Builder;

class RouterService
{
    public function getRoutersByEmployee($connectionName, $employeeId) {
        $routers = new Router();
        $routers = $routers->on($connectionName)->whereHas(Router::RELATION_ROUTER_DTLS, function (Builder $query) use ($employeeId) {
            $query->where(RouterDtl::EMPLOYEE_ID, $employeeId);
        })->get();
        return $routers;
    }
}
