<?php

namespace App\Service\Time;

use App\Models\Customer\Employee;
use App\Models\Customer\Time\TimeSchedule;
use App\Models\Customer\Time\TimeScheduleDtl;
use App\Models\Customer\Time\TimeScheduleDtlBt;
use App\Models\Customer\Time\TimeScheduleEmployee;
use App\Models\Customer\Time\TimeScheduleEmployeeNotWork;
use App\Models\Constant\ConstData;
use App\Exceptions\SystemException;
use Illuminate\Database\Eloquent\Builder;

class TimeScheduleService
{
    public function getTimeScheduleByTimeScheduleDtlId($connectionName, $timeScheduleDtlId) {
        $timeSchedule = new TimeSchedule();
        $timeSchedule->setConnection($connectionName);
        $timeSchedule = $timeSchedule->on($connectionName)
            ->with(TimeSchedule::RELATION_TIME_SCHEDULE_DTLS, TimeSchedule::RELATION_TIME_SCHEDULE_DTLS . '.' . TimeScheduleDtl::RELATION_TIME_SCHEDULE_DTL_BTS)
            ->whereHas(TimeSchedule::RELATION_TIME_SCHEDULE_DTLS, function (Builder $query) use ($timeScheduleDtlId) {
                $query->where(TimeScheduleDtl::ID, $timeScheduleDtlId);
            })->first();
        if (!isset($timeSchedule))
            throw new SystemException(ConstData::TIME_EXCEPTION, 301);
        return $timeSchedule;
    }

    public function prepareTimeScheduleDtls($connectionName, $timeScheduleDtls, $userId) {
        $newTimeScheduleDtls = collect();
        foreach ($timeScheduleDtls as $timeScheduleDtl) {
            $newTimeScheduleDtlBts = collect();
            $timeScheduleDtlBts    = $timeScheduleDtl[TimeScheduleDtl::RELATION_TIME_SCHEDULE_DTL_BTS];
            foreach ($timeScheduleDtlBts as $timeScheduleDtlBt) {
                $timeScheduleDtlBtDatum = [
                    TimeScheduleDtlBt::BEGIN_TIME_STR           => $timeScheduleDtlBt[TimeScheduleDtlBt::BEGIN_TIME_STR],
                    TimeScheduleDtlBt::END_TIME_STR             => $timeScheduleDtlBt[TimeScheduleDtlBt::END_TIME_STR],
                    TimeScheduleDtlBt::CHECK_IO                 => $timeScheduleDtlBt[TimeScheduleDtlBt::CHECK_IO],
                    TimeScheduleDtlBt::CREATED_BY               => $userId,
                ];
                $newTimeScheduleDtlBt = new TimeScheduleDtlBt($timeScheduleDtlBtDatum);
                $newTimeScheduleDtlBt->setConnection($connectionName);
                $newTimeScheduleDtlBts->push($newTimeScheduleDtlBt);
            }

            $beginTimeStr      = isset($timeScheduleDtl[TimeScheduleDtl::BEGIN_TIME_STR]) ? $timeScheduleDtl[TimeScheduleDtl::BEGIN_TIME_STR] : null;
            $endTimeStr        = isset($timeScheduleDtl[TimeScheduleDtl::END_TIME_STR]) ? $timeScheduleDtl[TimeScheduleDtl::END_TIME_STR] : null;
            $rbTimeStr         = isset($timeScheduleDtl[TimeScheduleDtl::RB_TIME_STR]) ? $timeScheduleDtl[TimeScheduleDtl::RB_TIME_STR] : null;
            $reTimeStr         = isset($timeScheduleDtl[TimeScheduleDtl::RE_TIME_STR]) ? $timeScheduleDtl[TimeScheduleDtl::RE_TIME_STR] : null;
            $workMin           = isset($timeScheduleDtl[TimeScheduleDtl::WORK_MIN]) ? $timeScheduleDtl[TimeScheduleDtl::WORK_MIN] : null;
            $overBeginTimeStr  = isset($timeScheduleDtl[TimeScheduleDtl::OVER_BEGIN_TIME_STR]) ? $timeScheduleDtl[TimeScheduleDtl::OVER_BEGIN_TIME_STR] : null;
            $overEndTimeStr    = isset($timeScheduleDtl[TimeScheduleDtl::OVER_END_TIME_STR]) ? $timeScheduleDtl[TimeScheduleDtl::OVER_END_TIME_STR] : null;

            $timeScheduleDtlDatum = [
                TimeScheduleDtl::TYPE                => $timeScheduleDtl[TimeScheduleDtl::TYPE],
                TimeScheduleDtl::DAY_OF_WEEK         => $timeScheduleDtl[TimeScheduleDtl::DAY_OF_WEEK],
                TimeScheduleDtl::ADMIT_LATE_MIN      => $timeScheduleDtl[TimeScheduleDtl::ADMIT_LATE_MIN],
                TimeScheduleDtl::BFR_MIN             => $timeScheduleDtl[TimeScheduleDtl::BFR_MIN],
                TimeScheduleDtl::AFR_MIN             => $timeScheduleDtl[TimeScheduleDtl::AFR_MIN],
                TimeScheduleDtl::HAS_BREAK_TIME      => count($timeScheduleDtlBts) > 0,
                TimeScheduleDtl::BEGIN_TIME_STR      => $beginTimeStr,
                TimeScheduleDtl::END_TIME_STR        => $endTimeStr,
                TimeScheduleDtl::RB_TIME_STR         => $rbTimeStr,
                TimeScheduleDtl::RE_TIME_STR         => $reTimeStr,
                TimeScheduleDtl::WORK_MIN            => $workMin,
                TimeScheduleDtl::OVER_BEGIN_TIME_STR => $overBeginTimeStr,
                TimeScheduleDtl::OVER_END_TIME_STR   => $overEndTimeStr,
                TimeScheduleDtl::CREATED_BY          => $userId,
            ];

            $newTimeScheduleDtl = new TimeScheduleDtl($timeScheduleDtlDatum);
            $newTimeScheduleDtl->setConnection($connectionName);
            $newTimeScheduleDtl->calcFields();
            $newTimeScheduleDtl->time_schedule_dtl_bts = $newTimeScheduleDtlBts;
            $newTimeScheduleDtls->push($newTimeScheduleDtl);
        }
        return $newTimeScheduleDtls;
    }

    public function getTimeScheduleById($connectionName, $id) {
        $timeSchedule = new TimeSchedule();
        $timeSchedule->setConnection($connectionName);
        $timeSchedule = $timeSchedule->on($connectionName)
            ->with(TimeSchedule::RELATION_TIME_SCHEDULE_DTLS, TimeSchedule::RELATION_TIME_SCHEDULE_DTLS . '.' . TimeScheduleDtl::RELATION_TIME_SCHEDULE_DTL_BTS)
            ->find($id);
        if (!isset($timeSchedule))
            throw new SystemException(ConstData::TIME_EXCEPTION, 301);
        return $timeSchedule;
    }

    public function getTimeScheduleDtlById($connectionName, $id) {
        $timeScheduleDtl = new TimeScheduleDtl();
        $timeScheduleDtl = $timeScheduleDtl->on($connectionName)
                ->with(TimeScheduleDtl::RELATION_TIME_SCHEDULE, TimeScheduleDtl::RELATION_TIME_SCHEDULE_DTL_BTS)
                ->find($id);
        if (!$timeScheduleDtl)
            throw new SystemException(ConstData::TIME_EXCEPTION, 301);
        return $timeScheduleDtl;
    }

    public function getTimeScheduleByHashStr($connectionName, $hashStr) {
        $timeSchedule = new TimeSchedule();
        $timeSchedule->setConnection($connectionName);
        return $timeSchedule->on($connectionName)->with(TimeSchedule::RELATION_TIME_SCHEDULE_DTLS, TimeSchedule::RELATION_TIME_SCHEDULE_DTLS . '.' . TimeScheduleDtl::RELATION_TIME_SCHEDULE_DTL_BTS)->where(TimeSchedule::TYPE, TimeSchedule::CURRENT_TIME_SCHEDULE)->where(TimeSchedule::HASH_STR, $hashStr)->first();
    }

    public function getTimeScheduleEmployeeNotWorkByDate($connectionName, $employeeId, $attDate) {
        $timeScheduleEmployeeNotWork = new TimeScheduleEmployeeNotWork();
        $timeScheduleEmployeeNotWork->setConnection($connectionName);
        $timeScheduleEmployeeNotWork = $timeScheduleEmployeeNotWork->where(TimeScheduleEmployeeNotWork::EMPLOYEE_ID, $employeeId)
                                                                   ->whereDate(TimeScheduleEmployeeNotWork::NOT_WORK_DATE, $attDate)
                                                                   ->first();
        return $timeScheduleEmployeeNotWork;
    }

    public function getTimeScheduleEmployeeNotWorksByBetweenDates($connectionName, $beginDate, $endDate, $departmentIds) {
        $timeScheduleEmployeeNotWorks = new TimeScheduleEmployeeNotWork();
        $timeScheduleEmployeeNotWorks = $timeScheduleEmployeeNotWorks->on($connectionName)->whereBetween(TimeScheduleEmployeeNotWork::NOT_WORK_DATE, [$beginDate, $endDate]);
        if (!empty($departmentIds)) {
            $timeScheduleEmployeeNotWorks = $timeScheduleEmployeeNotWorks->whereHas(TimeScheduleEmployeeNotWork::RELATION_EMPLOYEE, function (Builder $query) use ($departmentIds) {
                $query->whereIn(Employee::DEPARTMENT_ID, $departmentIds);
            });
        }
        $timeScheduleEmployeeNotWorks = $timeScheduleEmployeeNotWorks->get();
        return $timeScheduleEmployeeNotWorks;
    }

    public function deleteTimeScheduleEmployees($connectionName, $timeScheduleId) {
        $deletingTimeScheduleEmployees = new TimeScheduleEmployee();
        $deletingTimeScheduleEmployees->setConnection($connectionName);
        $deletingTimeScheduleEmployees->where(TimeScheduleEmployee::TIME_SCHEDULE_ID, $timeScheduleId)->delete();
    }

    public function deleteTimeScheduleDtls($connectionName, $timeScheduleId) {
        $deletingTimeScheduleDtls = new TimeScheduleDtl();
        $deletingTimeScheduleDtls->setConnection($connectionName);
        $deletingTimeScheduleDtls = $deletingTimeScheduleDtls->where(TimeScheduleDtl::TIME_SCHEDULE_ID, $timeScheduleId)->get();

        foreach ($deletingTimeScheduleDtls as $value) {
            $deletingTimeScheduleDtlBts = new TimeScheduleDtlBt();
            $deletingTimeScheduleDtlBts->setConnection($connectionName);
            $deletingTimeScheduleDtlBts->where(TimeScheduleDtlBt::TIME_SCHEDULE_DTL_ID, $value->id)->delete();
            $value->delete();
        }
    }

    public function deleteTimeSchedule($connectionName, $timeScheduleId) {
        $timeSchedule = $this->getTimeScheduleById($connectionName, $timeScheduleId);
        $this->deleteTimeScheduleEmployees($connectionName, $timeScheduleId);
        $this->deleteTimeScheduleDtls($connectionName, $timeScheduleId);
        $timeSchedule->delete();
    }
}
