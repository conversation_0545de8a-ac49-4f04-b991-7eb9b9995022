<?php

namespace App\Models\Customer\Time\Schedule\MainTimeSchedule;

use App\Models\Customer\Time\Schedule\TimeSchedule;
use App\Http\Tools\DateTool;
use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MainTimeSchedule extends TimeSchedule
{
    use HasFactory;
    const TABLE = 'main_time_schedules';

    const DEFAULT_NAME          = 'Үндсэн цагийн хуваарь';
    const DEFAULT_SHORT_NAME    = 'ҮЦХ';

    const CREATER_NAME    = 'creater_name';

    public function __construct($attributes = array()) {
        $this->connection             = ConnectionService::connectionName();
        $attributes[self::NAME]       = self::DEFAULT_NAME;
        $attributes[self::SHORT_NAME] = self::DEFAULT_SHORT_NAME;
        $attributes[self::HASH_STR]   = '';
        parent::__construct($attributes);
    }

    public function time_schedule_dtls(): HasMany {
        return $this->hasMany(MainTimeScheduleDtl::class);
    }

    public function getMainTimeScheduleDtlByDate($salaryDate) {
        $dateOfWeek      = DateTool::getDateOfWeek($salaryDate);
        $timeScheduleDtl = collect($this->time_schedule_dtls)->first(function ($value, $key) use ($dateOfWeek) {
            return $value->day_of_week == $dateOfWeek;
        });
        return $timeScheduleDtl;
    }

    public function getDefaultMainTimeScheduleDtls() {
        $timeScheduleDtlDtls = [];
        for ($i=1; $i <= 7; $i++) {
            $isHoliday = $i == 6 || $i == 7;
            $timeScheduleDtlDtl = new MainTimeScheduleDtl([
                MainTimeScheduleDtl::TYPE           => $isHoliday ? MainTimeScheduleDtl::TYPE_HOLIDAY : MainTimeScheduleDtl::TYPE_SIMPLE,
                MainTimeScheduleDtl::DAY_OF_WEEK    => $i,
                MainTimeScheduleDtl::ADMIT_LATE_MIN => 0,
                MainTimeScheduleDtl::HAS_BREAK_TIME => !$isHoliday,
                MainTimeScheduleDtl::BEGIN_TIME_STR => $isHoliday ? '00:00' : '09:00',
                MainTimeScheduleDtl::END_TIME_STR   => $isHoliday ? '00:00' : '18:00',
            ]);
            $timeScheduleDtlDtls[] = $timeScheduleDtlDtl;
        }
        return $timeScheduleDtlDtls;
    }
}
