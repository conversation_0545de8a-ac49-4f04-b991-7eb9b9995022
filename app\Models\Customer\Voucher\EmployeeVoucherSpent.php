<?php

namespace App\Models\Customer\Voucher;

use App\Models\Customer\Employee;
use App\Services\ConnectionService;
use App\Services\EmployeeUserService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeVoucherSpent extends Model
{
    use HasFactory;

    protected $table = 'employee_voucher_spents';

    const ID                        = 'id';
    const EMPLOYEE_ID               = 'employee_id';
    const AMOUNT                    = 'amount';
    const MERCHANT_COMPANY_NAME     = 'merchant_company_name';
    const MERCHANT_LOCATION_NAME    = 'merchant_location_name';
    const MERCHANT_EMPLOYEE_USER_ID = 'merchant_employee_user_id';

    const RELATION_EMPLOYEE_VOUCHER_SPENT_DTLS = 'employee_voucher_spent_dtls';

    public function __construct($attributes = array()) {
        $this->connection             = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::EMPLOYEE_ID,
        self::AMOUNT,
        self::MERCHANT_COMPANY_NAME,
        self::MERCHANT_LOCATION_NAME,
        self::MERCHANT_EMPLOYEE_USER_ID,
    ];

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function employee_voucher_spent_dtls()
    {
        return $this->hasMany(EmployeeVoucherSpentDtl::class);
    }

    public function getMerchantEmployeeDepartmentNameAttribute() {
        $employeeUser = resolve(EmployeeUserService::class)->getEmployeeUser($this->merchant_employee_user_id);
        if (!isset($employeeUser))
            return '';
        return $employeeUser->getEmployeeDepartmentName();
    }

    public function getMerchantEmployeePositionNameAttribute() {
        $employeeUser = resolve(EmployeeUserService::class)->getEmployeeUser($this->merchant_employee_user_id);
        if (!isset($employeeUser))
            return '';
        return $employeeUser->getEmployeePositionName();
    }

    public function getMerchantEmployeeDisplayNameAttribute() {
        $employeeUser = resolve(EmployeeUserService::class)->getEmployeeUser($this->merchant_employee_user_id);
        if (!isset($employeeUser))
            return '';
        return $employeeUser->getEmployeeDisplayName();
    }
}
