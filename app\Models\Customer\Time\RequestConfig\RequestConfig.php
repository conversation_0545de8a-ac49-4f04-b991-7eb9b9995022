<?php

namespace App\Models\Customer\Time\RequestConfig;

use App\Models\Customer\Department;

use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RequestConfig extends Model
{
    use HasFactory;

    const ID            = 'id';
    const DEPARTMENT_ID = 'department_id';

    const RELATION_DEPARTMENT          = 'department';
    const RELATION_REQUEST_CONFIG_DTLS = 'request_config_dtls';

    protected $fillable = [
        self::DEPARTMENT_ID
    ];

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    public function request_config_dtls()
    {
        return $this->hasMany(RequestConfigDtl::class);
    }

    public function request_config_employees()
    {
        return $this->hasMany(RequestConfigEmployee::class);
    }
}
