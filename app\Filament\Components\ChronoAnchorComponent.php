<?php

namespace App\Filament\Components;

use Filament\Forms;

class ChronoAnchorComponent
{
    public static function make(string $name = 'chrono_anchor'): Forms\Components\Group
    {
        return Forms\Components\Group::make([
            Forms\Components\Select::make("{$name}_chrono_unit")
                ->label('Chrono Unit')
                ->options([
                    'minute' => 'Minute',
                    'hour' => 'Hour',
                    'halfday' => 'Half Day',
                    'day' => 'Day',
                    'month' => 'Month',
                    'quarter' => 'Quarter',
                    'halfyear' => 'Half Year',
                    'year' => 'Year'
                ])
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\TextInput::make("{$name}_chrono_value")
                ->label('Anchor Chrono Value')
                ->numeric()
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\Select::make("{$name}_date_source")
                ->label('Date Source')
                ->options([
                    'now' => 'Current Time',
                    // 'custom' => 'Custom Date',
                    '__separator__' => '────────',
                    'employee_dtl.company_work_date' => 'Employee Company Work Date',
                    'employee_dtl.expired_at' => 'Employee Expired Date',
                    'holiday.begin_date' => 'Holiday Begin Date',
                    'holiday.end_date' => 'Holiday End Date',
                ])
                ->default('now')
                ->required()
                ->reactive()
                ->disableOptionWhen(fn ($value): bool => $value === '__separator__')
                ->afterStateUpdated(function ($state, callable $set, callable $get) use ($name) {
                    // Clear custom date when switching to model field
                    if ($state !== 'custom') {
                        $set("{$name}_custom_date", null);
                    }
                    self::updateFormula($name, $set, $get);
                }),
            Forms\Components\DatePicker::make("{$name}_custom_date")
                ->label('Custom Anchor Date')
                ->visible(fn (callable $get) => $get("{$name}_date_source") === 'custom')
                ->required(fn (callable $get) => $get("{$name}_date_source") === 'custom')
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\Hidden::make("{$name}_formula")
        ])->columns(4);
    }

    private static function updateFormula(string $name, callable $set, callable $get): void
    {
        $dateSource = $get("{$name}_date_source");
        $customDate = $get("{$name}_custom_date");
        $chronoUnit = $get("{$name}_chrono_unit");
        $chronoValue = $get("{$name}_chrono_value");

        // Determine the date value based on source
        $date = null;
        if ($dateSource === 'custom') {
            $date = $customDate;
        } else {
            // For model fields, use the field path as the date value
            $date = $dateSource;
        }

        if ($date && $chronoUnit && $chronoValue !== null && $chronoValue !== '') {
            // New format: anchor,date,chrono_unit,chrono_value
            $formula = "anchor,{$date},{$chronoUnit},{$chronoValue}";
            $set("{$name}_formula", $formula);
        }
    }

    /**
     * Parse a formula string back into component values
     * Formula format: "anchor,date,chrono_unit,chrono_value"
     */
    public static function parseFormula(string $formula): array
    {
        $result = [
            'chrono_unit' => null,
            'chrono_value' => null,
            'date_source' => 'custom',
            'custom_date' => null,
        ];

        // Split by comma and check if it's the new format
        $parts = explode(',', $formula);

        if (count($parts) === 4 && $parts[0] === 'anchor') {
            $dateValue = $parts[1];
            $chronoUnit = $parts[2];
            $chronoValue = (int)$parts[3];

            $result['chrono_unit'] = $chronoUnit;
            $result['chrono_value'] = $chronoValue;

            // Check if it's a date string (YYYY-MM-DD format) or model field
            if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $dateValue)) {
                $result['date_source'] = 'custom';
                $result['custom_date'] = $dateValue;
            } else {
                // It's a model field
                $result['date_source'] = $dateValue;
                $result['custom_date'] = null;
            }
        }

        return $result;
    }
}