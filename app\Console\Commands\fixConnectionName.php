<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\OrganizationService;

class fixConnectionName extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:cn {orgRegNumber}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        resolve(OrganizationService::class)->fixConnectionConfig($this->argument('orgRegNumber'));
    }
}
