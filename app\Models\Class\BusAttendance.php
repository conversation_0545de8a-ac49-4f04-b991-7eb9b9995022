<?php

namespace App\Models\Class;


use Illuminate\Database\Eloquent\Model;

class BusAttendance extends Model
{
    const CONNECTION_NAME                   = 'connection_name';
    const USER                              = 'user';
    const EMPLOYEES                         = 'employees';
    const SUPER_SALARY_PERIOD_DTL           = 'super_salary_period_dtl';
    const HOLIDATES                         = 'holidates';
    const OTHER_TIME_SCHEDULE_EMPLOYEES     = 'other_time_schedule_employees';
    const RAW_ATTENDANCES                   = 'raw_attendances';
    const CONFIRMED_TIME_REQUESTS           = 'confirmed_time_requests';
    const EMPLOYEE_ATTENDANCES              = 'employee_attendances';
    const NOW_DATE_TIME                     = 'now_date_time';

    protected $fillable = [
        self::CONNECTION_NAME,
        self::USER,
        self::EMPLOYEES,
        self::SUPER_SALARY_PERIOD_DTL,
        self::HOLIDATES,
        self::OTHER_TIME_SCHEDULE_EMPLOYEES,
        self::RAW_ATTENDANCES,
        self::CONFIRMED_TIME_REQUESTS,
        self::CONFIRMED_TIME_REQUESTS,
        self::EMPLOYEE_ATTENDANCES,
        self::NOW_DATE_TIME,
    ];
}
