<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class EmployeeVoucherAmountWithEmployee extends JsonResource
{
    const ID                                = 'id';
    const UNUSED_TOTAL_AMOUNT               = 'unused_total_amount';
    const EMPLOYEE_ID                       = 'employee_id';
    const EMPLOYEE                          = 'employee';
    
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'                     => $this->id,
            'unused_total_amount'    => $this->unused_total_amount,
            'employee_id'            => $this->employee_id,
            'employee'               => new Employee($this->employee),
        ];
    }
}
