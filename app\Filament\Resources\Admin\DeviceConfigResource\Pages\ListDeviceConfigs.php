<?php

namespace App\Filament\Resources\Admin\DeviceConfigResource\Pages;

use App\Filament\Resources\Admin\DeviceConfigResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDeviceConfigs extends ListRecords
{
    protected static string $resource = DeviceConfigResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-o-plus'),
        ];
    }
}
