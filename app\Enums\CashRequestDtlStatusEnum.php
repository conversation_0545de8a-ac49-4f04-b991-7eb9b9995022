<?php

namespace App\Enums;
use Filament\Support\Contracts\HasLabel;
 
enum CashRequestDtlStatusEnum: string implements HasLabel
{
    case REJECTED      = 'rejected';
    case APPROVED      = 'approved';


    public function getLabelColor(): ?string
    {
        return match ($this) {
            self::APPROVED      => 'success',
            self::REJECTED      => 'danger',
        };
    }
    
    public function getLabel(): ?string
    {
        return match ($this) {
            self::APPROVED      => 'Баталсан',
            self::REJECTED      => 'Цуцалсан',
        };
    }
}
