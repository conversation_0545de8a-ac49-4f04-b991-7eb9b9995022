<?php

namespace App\Services;

use App\Models\Constant\ConstData;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Laravel\Firebase\Facades\Firebase;

class NotificationService
{
    public function sendPushNotification($topic, $title, $body, array $data = [])
    {
        $notification = Firebase::messaging();
        $message      = CloudMessage::fromArray([
            'notification' => [
                'title' => $title,
                'body'  => $body
            ],
            'topic' => $topic,
            'data'  => $data,
        ]);
        $notification->send($message);
        return response()->json(['message' => 'Push notification sent successfully']);
    }
}
