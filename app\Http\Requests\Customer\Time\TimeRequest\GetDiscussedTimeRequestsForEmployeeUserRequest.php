<?php

namespace App\Http\Requests\Customer\Time\TimeRequest;

use Illuminate\Foundation\Http\FormRequest;

class GetDiscussedTimeRequestsForEmployeeUserRequest extends FormRequest
{
    const FILTER = 'filter';
    const FILTER_ATT_STATUS      = 'att_status';
    const FILTER_CONFIRM_STATUS  = 'confirm_status';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            //
        ];
    }
}
