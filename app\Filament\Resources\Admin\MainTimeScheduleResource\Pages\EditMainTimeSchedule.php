<?php

namespace App\Filament\Resources\Admin\MainTimeScheduleResource\Pages;

use App\Models\Customer\Time\Schedule\MainTimeSchedule\MainTimeSchedule;

use App\Filament\Resources\Admin\MainTimeScheduleResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditMainTimeSchedule extends EditRecord
{
    protected static string $resource = MainTimeScheduleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data[MainTimeSchedule::UPDATED_BY] = auth()->user()->id;
        return $data;
    }
}
