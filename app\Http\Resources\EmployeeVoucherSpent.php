<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class EmployeeVoucherSpent extends JsonResource
{
    const ID                                = 'id';
    const EMPLOYEE_ID                       = 'employee_id';
    const AMOUNT                            = 'amount';
    const MERCHANT_COMPANY_NAME             = 'merchant_company_name';
    const MERCHANT_LOCATION_NAME            = 'merchant_location_name';
    const MERCHANT_EMPLOYEE_USER_ID         = 'merchant_employee_user_id';
    const EMPLOYEE                          = 'employee';
    const CREATED_AT                        = 'created_at';
    
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'                                => $this->id,
            'employee_id'                       => $this->employee_id,
            'amount'                            => $this->amount,
            'merchant_company_name'             => $this->merchant_company_name,
            'merchant_location_name'            => $this->merchant_location_name,
            'employee'                          => new Employee($this->employee),
            'created_at'                        => $this->created_at,
        ];
    }
}
