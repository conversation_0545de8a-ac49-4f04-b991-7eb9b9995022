<?php

namespace App\Filament\Resources\Admin\OtherTimeScheduleResource\Pages;

use App\Models\Customer\Time\Schedule\OtherTimeSchedule\OtherTimeSchedule;
use App\Models\Customer\Time\Schedule\TimeScheduleEmployee;
use App\Services\ConnectionService;
use App\Http\Tools\DateTool;
use App\Filament\Resources\Admin\OtherTimeScheduleResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;


class EditOtherTimeSchedule extends EditRecord
{
    protected static string $resource = OtherTimeScheduleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->after(function () {
                    $this->record->time_schedule_employees()->delete();
                }),
                
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {        
        foreach ($this->record->time_schedule_employees as $key => $value) {
            $value[TimeScheduleEmployee::HAS_END_DATE] = $value[TimeScheduleEmployee::END_DATE] ? true : false;
        }
        $data[OtherTimeSchedule::RELATION_TIME_SCHEDULE_EMPLOYEES] = $this->record->time_schedule_employees;
        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data[OtherTimeSchedule::UPDATED_BY] = auth()->user()->id;
        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $oldTimeSheduleEmployees = $record->time_schedule_employees;
        $newTimeSheduleEmployees = $data[OtherTimeSchedule::RELATION_TIME_SCHEDULE_EMPLOYEES];
        $oldTimeSheduleEmployees->each(function($oldTimeSheduleEmployee) use($newTimeSheduleEmployees) {
            $hasFound = false;
            foreach ($newTimeSheduleEmployees as $key => $newTimeSheduleEmployee) {
                if ($oldTimeSheduleEmployee->employee_id != $newTimeSheduleEmployee[TimeScheduleEmployee::EMPLOYEE_ID]) 
                    continue;
                $hasFound = true;
                $oldTimeSheduleEmployee->update([TimeScheduleEmployee::BEGIN_DATE => $newTimeSheduleEmployee[TimeScheduleEmployee::BEGIN_DATE]]);
                break;
            }
            if (!$hasFound) {
                $now = DateTool::nowDate();
                if ($oldTimeSheduleEmployee->begin_date == $now) {
                    $oldTimeSheduleEmployee->delete();
                } else {
                    $oldTimeSheduleEmployee->update([TimeScheduleEmployee::END_DATE => $now]);
                }
            }
        });
        $cn = ConnectionService::connectionName();
        foreach ($newTimeSheduleEmployees as $key => $newTimeSheduleEmployee) {
            $hasFound = $oldTimeSheduleEmployees->contains(function($oldTimeSheduleEmployee) use($newTimeSheduleEmployee) {
                return $oldTimeSheduleEmployee->employee_id == $newTimeSheduleEmployee[TimeScheduleEmployee::EMPLOYEE_ID];
            });
            if (!$hasFound) {
                $newTimeSheduleEmployee[TimeScheduleEmployee::TIME_SCHEDULE_ID] = $record->id;
                TimeScheduleEmployee::on($cn)->create($newTimeSheduleEmployee);
            }
        }
        $record->update($data);
        return $record;
    }
}
