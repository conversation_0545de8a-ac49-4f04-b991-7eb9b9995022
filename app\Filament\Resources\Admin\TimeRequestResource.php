<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Filament\Resources\Admin\TimeRequestResource\Pages;
use App\Filament\Resources\Admin\TimeRequestResource\RelationManagers;
use App\Models\Customer\Employee;
use App\Models\Customer\Time\TimeRequest\TimeRequest;
use App\Services\Time\PrepareEmployeeAttendancesService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class TimeRequestResource extends Can
{
    protected static ?string $model = TimeRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Хүсэлт батлах';
    protected static ?string $modelLabel = 'хүсэлт батлах';
    protected static ?int $navigationSort = 12;
    protected static ?string $navigationGroup = 'Цагийн хүсэлт';
    protected static ?string $slug = 'time-requests';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(TimeRequest::RELATION_EMPLOYEE.'.'.Employee::FULL_NAME)->label('Нэр')->sortable()->searchable(['employees.last_name', 'employees.first_name']),
                Tables\Columns\TextColumn::make(TimeRequest::BEGIN_DATE)->label('Эхлэх')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(TimeRequest::END_DATE)->label('Дуусах')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(TimeRequest::DESCRIPTION)->label('Тайлбар')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(TimeRequest::ATT_STATUS)->label('Хүсэлтийн төлөв')
                ->formatStateUsing(fn (TimeRequest $record): string => $record->getAttStatusName())
                ->sortable()->searchable(),
                Tables\Columns\SelectColumn::make(TimeRequest::CONFIRM_STATUS)
                    ->label('Төлөв')
                    ->options(fn (TimeRequest $record): array => $record->getAttStatusNames())
                    ->afterStateUpdated(function ($record) {
                        $service = resolve(PrepareEmployeeAttendancesService::class);
                        $service->prepareEmployeeAttendancesByEmployee(auth()->user(), $record->employee_id, $record->begin_date, $record->end_date);
                        $record->confirm_status = TimeRequest::CONFIRM_STATUS_CONFIRMED;
                        $record->save();
                    })
                    ->disabled(fn (TimeRequest $record): bool => $record->confirm_status != TimeRequest::CONFIRM_STATUS_SENT)
                    ->selectablePlaceholder(false)
            ])
            ->filters([
                //
            ])
            ->actions([
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTimeRequests::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->orderByDesc('begin_date');
    }
}
