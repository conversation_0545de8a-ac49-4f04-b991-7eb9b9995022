<?php

namespace App\Filament\Resources\Admin\BenefitGroupResource\Pages;

use App\Filament\Resources\Admin\BenefitGroupResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBenefitGroup extends EditRecord
{
    protected static string $resource = BenefitGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
