<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('employee_vouchers'))
            return;
        Schema::create('employee_vouchers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('employee_id');
            $table->unsignedBigInteger('voucher_tohirgoo_id');
            $table->string('code')->unique();
            $table->unsignedDecimal('amount', $precision = 24, $scale = 2);
            $table->unsignedDecimal('used_amount', $precision = 24, $scale = 2);
            $table->unsignedDecimal('unused_amount', $precision = 24, $scale = 2);
            $table->enum('status', ['buren', 'dutuu', 'ogt'])->default('ogt');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employee_vouchers');
    }
};
