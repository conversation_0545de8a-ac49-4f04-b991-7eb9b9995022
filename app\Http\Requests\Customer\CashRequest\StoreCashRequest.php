<?php

namespace App\Http\Requests\Customer\CashRequest;

use Illuminate\Foundation\Http\FormRequest;

class StoreCashRequest extends FormRequest
{   
    const IS_COMPANY              = 'is_company';
    const RECEIVER_NAME           = 'receiver_name';
    const TRANSACTION_DESCRIPTION = 'transaction_description';
    const BANK_ID                 = 'bank_id';
    const IS_IBAN                 = 'is_iban';
    const IBAN_NUMBER             = 'iban_number';
    const ACCOUNT_NUMBER          = 'account_number';
    const AMOUNT                  = 'amount';
    const ATTACHMENTS             = 'attachments';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [    
            'is_company'              => 'required|boolean',
            'receiver_name'           => 'required|string',
            'transaction_description' => 'required|string',
            'bank_id'                 => 'required|exists:banks,id',
            'is_iban'                 => 'required|boolean',
            'iban_number'             => 'required_if:is_iban,true|string',
            'account_number'          => 'required_if:is_iban,false|string',
            'amount'                  => 'required|numeric',
            'attachments'             => 'nullable|array',
            'attachments.*'           => 'required|file|max:10240',
        ];
    }
}
