<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class SalaryDayFrequencyDtl
 *
 * @mixin \App\Models\Customer\Time\SalaryDayFrequencyDtl
 */
class SalaryPeriodDtl extends JsonResource
{
    const ID                                         = 'id';
    const SALARY_PERIOD_ID                           = 'salary_period_id';
    const BEGIN_DATE                                 = 'begin_date';
    const END_DATE                                   = 'end_date';
    const SALARY_PERIOD_SUB_DTLS                     = 'salary_period_sub_dtls';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                           => $this->id,
            self::SALARY_PERIOD_ID             => $this->salary_period_id,
            self::BEGIN_DATE                   => $this->begin_date,
            self::END_DATE                     => $this->end_date,
            self::SALARY_PERIOD_SUB_DTLS       => $this->salary_period_sub_dtls,
        ];
    }
}
