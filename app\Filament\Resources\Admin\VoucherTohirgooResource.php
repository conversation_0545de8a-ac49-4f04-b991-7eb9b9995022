<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Models\Constant\ConstData;
use App\Models\Customer\Department;
use App\Models\Customer\Employee;
use App\Models\Customer\Voucher\VoucherTohirgoo;
use App\Services\ConnectionService;
use App\Jobs\CreateVouchersJob;
use App\Filament\Resources\Admin\VoucherTohirgooResource\Pages;
use App\Filament\Resources\Admin\VoucherTohirgooResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Support\RawJs;
use Illuminate\Database\Eloquent\Collection;
use Filament\Notifications\Notification;

class VoucherTohirgooResource extends Can
{
    protected static ?string $model = VoucherTohirgoo::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Тохиргоо';
    protected static ?string $modelLabel = 'Тохиргоо';
    protected static ?int $navigationSort = 110;
    protected static ?string $navigationGroup = 'Ваучер';
    protected static ?string $slug = 'voucher-tohirgoos';

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make(VoucherTohirgoo::NAME)
                            ->label('Нэр')
                            ->maxLength(50)
                            ->required(),
                        Forms\Components\DatePicker::make(VoucherTohirgoo::BEGIN_DATE)
                            ->label('Эхлэх огноо')
                            ->default(now())
                            ->displayFormat('d/m/Y')
                            ->required(),
                        Forms\Components\Select::make(VoucherTohirgoo::INTERVAL_MONTH)
                            ->label('Хэдэн сар тутамд вэ?')
                            ->options(function () {
                                $values = [];
                                for ($i = 1; $i <= 12; $i++)
                                    $values[$i] = $i . ' сар';;
                                return $values;
                            })
                            ->required(),
                        Forms\Components\TextInput::make(VoucherTohirgoo::AMOUNT)
                            ->label('Ваучерын дүн')
                            ->numeric()
                            ->inputMode('decimal')
                            ->mask(RawJs::make('$money($input)'))
                            ->stripCharacters(',')
                            ->required(),

                        Forms\Components\Select::make(VoucherTohirgoo::RELATION_DEPARTMENTS)
                            ->label('Хэлтэс')
                            ->relationship(name: VoucherTohirgoo::RELATION_DEPARTMENTS, titleAttribute: ConstData::NAME)
                            ->options(Department::all()->pluck(ConstData::NAME, ConstData::ID))
                            ->searchable()
                            ->distinct()
                            ->multiple()
                            ->searchingMessage('Хайж байна...')
                            ->loadingMessage('Ачаалж байна байна...')
                            ->noSearchResultsMessage('Хэлтэс олдсонгүй.'),
                        Forms\Components\Select::make(VoucherTohirgoo::RELATION_EMPLOYEES)
                            ->label('Ажилтан')
                            ->relationship(name: VoucherTohirgoo::RELATION_EMPLOYEES, titleAttribute: 'first_name')
                            ->options(Employee::all()->pluck(Employee::FULL_NAME, ConstData::ID))
                            ->searchable()
                            ->distinct()
                            ->multiple()
                            ->searchingMessage('Хайж байна...')
                            ->loadingMessage('Ачаалж байна байна...')
                            ->noSearchResultsMessage('Ажилтан олдсонгүй.'),
                    ])
                    ->columns(1)
                    ->columnSpan(['lg' => fn(?VoucherTohirgoo $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn(VoucherTohirgoo $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn(VoucherTohirgoo $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn(?VoucherTohirgoo $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(VoucherTohirgoo::NAME)->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(VoucherTohirgoo::BEGIN_DATE)->label('Эхлэх огноо')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(VoucherTohirgoo::INTERVAL_MONTH)->label('Хэдэн сар тутамд вэ?')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(VoucherTohirgoo::AMOUNT)->label('Ваучерын дүн')->money('MNT')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('Ажилуулах')
                        ->action(function (Collection $records, Tables\Actions\BulkAction $action) {
                            $cn = ConnectionService::connectionNameByUser(auth()->user());
                            // Job dispatch
                            dispatch(new CreateVouchersJob($cn, $records));

                            // UI-д амжилттай эхэлсэн notification
                            Notification::make()
                                ->title('Үйлдэл эхлүүллээ')
                                ->body('Ваучер үүсгэх процесс Queue-д илгээгдлээ. Та хэсэг хугацааны дараа үр дүнг хянах боломжтой.')
                                ->success()
                                ->send();

                        })
                        ->requiresConfirmation()
                        ->modalHeading('Ажилтнуудад ваучер үүсгэх үү?')
                        ->modalDescription('Сонгогдсон тохиргоонууд дээр үндэслэн ажилтнуудад ваучер үүсгэнэ. Та итгэлтэй байна уу?')
                        ->modalSubmitActionLabel('Үүсгэх')
                        ->icon('heroicon-m-arrow-path')
                        ->color('primary'),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVoucherTohirgoos::route('/'),
            'create' => Pages\CreateVoucherTohirgoo::route('/create'),
            'edit' => Pages\EditVoucherTohirgoo::route('/{record}/edit'),
        ];
    }
}
