<?php

namespace App\Models\Customer\Time\SuperSalaryPeriod;

use App\Models\Customer\Department;
use App\Http\Tools\DateTool;
use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SuperSalaryPeriodDtl extends Model
{
    use HasFactory;

    const SUPER_SALARY_PERIOD_ID = 'super_salary_period_id';
    const TYPE                   = 'type';
    const WD_COUNT               = 'wd_count';
    const WD_MIN                 = 'wd_min';

    const WD_TIME          = 'wd_time';
    const WD_TOTAL_TIME    = 'wd_total_time';

    const RELATION_SUPER_SALARY_PERIOD              = 'super_salary_period';
    const RELATION_SUPER_SALARY_PERIOD_SUB_ONE_DTLS = 'super_salary_period_sub_one_dtls';
    const RELATION_SUPER_SALARY_PERIOD_SUB_TWO_DTLS = 'super_salary_period_sub_two_dtls';

    public function __construct($attributes = array()) {
        parent::__construct($attributes);
        $this->connection = ConnectionService::connectionName();
    }

    protected $fillable = [
        self::SUPER_SALARY_PERIOD_ID, 
        self::TYPE,
        self::WD_COUNT,
        self::WD_MIN
    ];

    public function getDepartmentNamesAttribute()
    {
        return $this->super_salary_period_sub_one_dtls->pluck('short_name')->implode(', ');
    }

    public function super_salary_period()
    {
        return $this->belongsTo(SuperSalaryPeriod::class);
    }

    public function super_salary_period_sub_one_dtls()
    {
        return $this->belongsToMany(Department::class, 'super_salary_period_sub_one_dtls', 'super_salary_period_dtl_id', 'department_id');
    }

    public function super_salary_period_sub_two_dtls()
    {
        return $this->hasMany(SuperSalaryPeriodSubTwoDtl::class);
    }

    public function getYear() {
        return $this->super_salary_period->year;
    }

    public function getMonth() {
        return $this->super_salary_period->month;
    }

    public function getBeginDate() {
        $firstItem = $this->super_salary_period_sub_two_dtls->first();
        return $firstItem ? $firstItem->begin_date : null;
    }

    public function getEndDate() {
        $lastItem = $this->super_salary_period_sub_two_dtls->last();
        return $lastItem ? $lastItem->end_date : null;
    }
    
    public function getWdTimeAttribute() {  
        return isset($this->wd_min) ? DateTool::convertTime($this->wd_min) : null;
    }     

    public function getTotalWdMinute() {  
        return (int) ($this->wd_count ?? 0) * (int) ($this->wd_min ?? 0);
    }
    
    public function getWdTotalTimeAttribute() {  
        return ((int) $this->wd_count * (int) $this->wd_min) / 60;
    }    
}
