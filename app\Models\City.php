<?php

namespace App\Models;

use App\Models\Constant\ConstData;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @mixin IdeHelperCity
 */
class City extends Model
{
    use HasFactory;
    protected $connection = ConstData::ADMIN_DB;
    const TABLE           = 'cities';

    const ID   = 'id';
    const NAME = 'name';

    const RELATION_DISTRICTS = 'districts';

    public function districts(): HasMany {
        return $this->hasMany(District::class);
    }
}
