<?php

namespace App\Filament\Exports;

use App\Models\Customer\Voucher\EmployeeVoucherSpent;
use App\Models\Customer\Employee;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use Illuminate\Support\Collection;

class EmployeeVoucherSpentExporter extends Exporter
{
    protected static ?string $model = EmployeeVoucherSpent::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('id')->label('Код'),
            ExportColumn::make('last_name')->label('Овог'),
            ExportColumn::make('first_name')->label('Нэр'),
            ExportColumn::make('department.name')->label('Хэлтэс'),
            ExportColumn::make('position.name')->label('Албан тушаал'),
            ExportColumn::make('total_spent')
            ->label('Нийт дүн')
            ->state(fn ($record) => number_format($record->employee_voucher_spents->sum('amount')) . ' ₮'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Employee voucher export амжилттай дууслаа. ' . number_format($export->successful_rows) . ' ' . str('мөр')->plural($export->successful_rows) . ' экспортлогдсон.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('мөр')->plural($failedRowsCount) . ' экспортод амжилтгүй болсон.';
        }

        return $body;
    }
}
