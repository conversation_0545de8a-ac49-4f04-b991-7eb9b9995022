<?php

namespace App\Jobs;

use App\Models\Constant\ConstData;
use App\Services\Time\RawAttendanceService;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use Illuminate\Support\Facades\DB;
class ProcessRawAttendance implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout   = 1200;
    public $tries     = 1;

    protected $connectionName;
    protected $userId;
    protected $queueUniqueId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($connectionName, $userId)
    {
        $this->connectionName = $connectionName;
        $this->userId         = $userId;
        $this->queueUniqueId  = 'raw-attendance-'.$this->userId;
        $this->onConnection(ConstData::QC_DATABASE);
    }

    public function uniqueId()
    {
        return $this->queueUniqueId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(RawAttendanceService $rawAttendanceService)
    {
        try {
            $connectionName = $this->connectionName;
            $cn = DB::connection($connectionName);
            $cn->beginTransaction();
            $rawAttendanceService->downloadDeviceAttendances($connectionName);
            $cn->commit();
        } catch (\Throwable $th) {
            throw $th;
        } finally {
            DB::disconnect($connectionName);
        }
    }
}
