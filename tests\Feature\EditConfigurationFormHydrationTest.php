<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Filament\Resources\Admin\ConfigurationResource\Pages\EditConfiguration;
use App\Filament\Components\ChronoAnchorComponent;
use App\Filament\Components\ChronoRangeComponent;
use App\Filament\Components\ChronoCountComponent;

class EditConfigurationFormHydrationTest extends TestCase
{
    /** @test */
    public function it_can_create_edit_configuration_instance()
    {
        // Test that the EditConfiguration class can be instantiated
        $this->assertTrue(class_exists(EditConfiguration::class));
    }

    /** @test */
    public function it_has_mutate_form_data_before_fill_method()
    {
        // Test that the method exists
        $this->assertTrue(method_exists(EditConfiguration::class, 'mutateFormDataBeforeFill'));
    }

    /** @test */
    public function mutate_form_data_before_fill_handles_empty_data()
    {
        // Just test that the method exists and can be called
        $reflection = new \ReflectionClass(EditConfiguration::class);
        $method = $reflection->getMethod('mutateFormDataBeforeFill');

        $this->assertTrue($method->isProtected());
        $this->assertEquals('array', $method->getReturnType()->getName());
    }

    /** @test */
    public function parsing_components_are_available()
    {
        // Test that all required parsing components are available
        $this->assertTrue(method_exists(ChronoAnchorComponent::class, 'parseFormula'));
        $this->assertTrue(method_exists(ChronoRangeComponent::class, 'parseFormula'));
        $this->assertTrue(method_exists(ChronoCountComponent::class, 'parseFormula'));
    }
}
