<?php

namespace App\Filament\Resources\Admin\RawAttendanceResource\Pages;

use App\Jobs\ProcessRawAttendance;
use App\Services\ConnectionService;
use App\Filament\Resources\Admin\RawAttendanceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListRawAttendances extends ListRecords
{
    protected static string $resource = RawAttendanceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('download')
                    ->label('Шинэчлэх')
                    ->color('success')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->action(function () {
                        $user = auth()->user();
                        $cn = ConnectionService::connectionNameByUser($user);
                        ProcessRawAttendance::dispatch($cn, $user->id);
                    }),
        ];
    }
}
