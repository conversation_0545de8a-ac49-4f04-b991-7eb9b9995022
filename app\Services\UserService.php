<?php

namespace App\Services;

use App\Models\Constant\ConstData;
use App\Models\User;

class UserService
{
   public function getUsersWhoIsYourOrganization($orgnizationId)
   {    
       return User::whereHas('organizations', function ($query) use ($orgnizationId) {
           $query->where('organization_id', $orgnizationId);
       })->whereHas('roles', function ($query) {
           $query->where('name', ConstData::ROLE_VOUCHERSPENT);   
       })->get();
   }
}
