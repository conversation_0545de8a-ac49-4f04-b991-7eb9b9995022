<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasColumn('time_requests', 'is_used'))
            return;
        Schema::table('time_requests', function (Blueprint $table) {
            $table->boolean('is_used')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('time_requests', function (Blueprint $table) {
            $table->dropColumn('is_used');
        });
    }
};
