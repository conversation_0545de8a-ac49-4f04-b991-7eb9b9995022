<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTimeScheduleEmployeesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('time_schedule_employees'))
            return;
        Schema::create('time_schedule_employees', function (Blueprint $table) {
            $table->id();
            $table->integer('type');
            $table->unsignedBigInteger('time_schedule_id');
            $table->foreignId('employee_id')->constrained('employees')->onDelete('cascade');
            $table->date('begin_date');
            $table->date('end_date')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('time_schedule_employees');
    }
}
