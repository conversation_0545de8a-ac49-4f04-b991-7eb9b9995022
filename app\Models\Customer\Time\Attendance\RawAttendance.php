<?php

namespace App\Models\Customer\Time\Attendance;

use App\Models\Customer\EmployeeDtl;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Services\ConnectionService;

class RawAttendance extends Model
{
    use HasFactory;
    const TABLE = 'raw_attendances';

    const ID                   = 'id';
    const ATTENDANCE_CODE      = 'attendance_code';
    const REGISTER_DATE        = 'register_date';
    const TYPE                 = 'type';
    const FROM_NAME            = 'from_name';
    const LATITUDE             = 'latitude';
    const LONGITUDE            = 'longitude';

    const TYPE_DEFAULT                 = 0;
    const TYPE_CUSTOM                  = 1;
    const TYPE_LOCATION_ANYWHERE       = 2;
    const TYPE_LOCATION_AREA           = 3;

    const IO_STR          = 'io_str';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::ID,
        self::ATTENDANCE_CODE,
        self::REGISTER_DATE,
        self::TYPE,
        self::FROM_NAME,
        self::LATITUDE,
        self::LONGITUDE,
    ];

    public function employee_dtl () {
        return $this->belongsTo(EmployeeDtl::class, 'attendance_code', 'attendance_code');
    }
}
