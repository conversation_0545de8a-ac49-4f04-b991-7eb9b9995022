<?php

namespace App\Service;

use App\Models\Admin\Configuration;
use App\Models\Constant\ConstData;
use App\Exceptions\SystemException;

class AdminConfigurationService
{
    public function getConfigurationValueByConfigId($id) {
        $configuration = new Configuration();
        $configuration = $configuration->where(Configuration::CONFIG_ID, $id)->first();

        if (!$configuration)
            throw new SystemException(ConstData::CONFIG_EXCEPTION, 1);
        return $configuration->value;
    }
}
