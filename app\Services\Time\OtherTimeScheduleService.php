<?php

namespace App\Services\Time;

use App\Models\Customer\Time\Schedule\TimeScheduleEmployee;

class OtherTimeScheduleService
{
    public function getOtherTimeScheduleEmployeesByBetweenDates($cn, $beginDate, $endDate, $departmentIds = [])
    {
        $otherTimeScheduleEmployees = TimeScheduleEmployee::on($cn)
            ->where(TimeScheduleEmployee::TYPE, TimeScheduleEmployee::TYPE_OTHER)
            ->has(TimeScheduleEmployee::RELATION_OTHER_TIME_SCHEDULE);

        $otherTimeScheduleEmployees = $otherTimeScheduleEmployees->where(
            function ($query) use ($beginDate, $endDate) {
                return $query->where(
                    function ($query) use ($beginDate, $endDate) {
                        return $query->whereDate(TimeScheduleEmployee::BEGIN_DATE, '>=', $beginDate)
                            ->where(
                                function ($query) use ($endDate) {
                                    return $query->whereDate(TimeScheduleEmployee::END_DATE, '<=', $endDate)->orWhereNull(TimeScheduleEmployee::END_DATE);
                                }
                            );
                    }
                )->orWhere(
                    function ($query) use ($beginDate, $endDate) {
                        return $query->whereDate(TimeScheduleEmployee::BEGIN_DATE, '<', $beginDate)
                            ->where(
                                function ($query) use ($endDate) {
                                    return $query->whereDate(TimeScheduleEmployee::END_DATE, '>', $endDate)->orWhereNull(TimeScheduleEmployee::END_DATE);
                                }
                            );
                    }
                )->orWhere(
                    function ($query) use ($beginDate, $endDate) {
                        return $query->whereDate(TimeScheduleEmployee::BEGIN_DATE, '<', $beginDate)->where(
                            function ($query) use ($beginDate, $endDate) {
                                return $query->whereBetween(TimeScheduleEmployee::END_DATE, [$beginDate, $endDate])->orWhereNull(TimeScheduleEmployee::END_DATE);
                            }
                        );
                    }
                )->orWhere(
                    function ($query) use ($beginDate, $endDate) {
                        return $query->whereBetween(TimeScheduleEmployee::BEGIN_DATE, [$beginDate, $endDate])->where(
                            function ($query) use ($beginDate, $endDate) {
                                return $query->whereDate(TimeScheduleEmployee::END_DATE, '>', $endDate)->orWhereNull(TimeScheduleEmployee::END_DATE);
                            }
                        );
                    }
                );
            }
        )
            ->orderBy(TimeScheduleEmployee::BEGIN_DATE)
            ->get();
        return $otherTimeScheduleEmployees;
    }

    public function getOtherTimeScheduleEmployeeByDate($cn, $employeeId, $attDate)
    {
        $otherTimeScheduleEmployee = TimeScheduleEmployee::on($cn)
            ->where(TimeScheduleEmployee::TYPE, TimeScheduleEmployee::TYPE_OTHER)
            ->has(TimeScheduleEmployee::RELATION_OTHER_TIME_SCHEDULE)
            ->where(TimeScheduleEmployee::EMPLOYEE_ID, $employeeId)
            ->where(
                function ($query) use ($attDate) {
                    return $query->where(
                        function ($query) use ($attDate) {
                            return $query->whereDate(TimeScheduleEmployee::BEGIN_DATE, '<=', $attDate)
                                ->where(
                                    function ($query) use ($attDate) {
                                        return $query->whereDate(TimeScheduleEmployee::END_DATE, '>=', $attDate)->orWhereNull(TimeScheduleEmployee::END_DATE);
                                    }
                                );
                        }
                    );
                }
            )
            ->first();
        return $otherTimeScheduleEmployee;
    }
}
