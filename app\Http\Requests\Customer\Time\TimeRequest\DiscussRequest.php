<?php

namespace App\Http\Requests\Customer\Time\TimeRequest;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\Customer\Time\TimeRequest\TimeRequest;

class DiscussRequest extends FormRequest
{
    const PARAMETER_CONFIRM_STATUS = 'confirm_status';
    const PARAMETER_DESCRIPTION    = 'description';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'confirm_status' => ['required', Rule::in(TimeRequest::REQUEST_CONFIRM_STATUS)],
            'description'    => 'string|nullable|max:255',
        ];
    }
}
