<?php

namespace App\Filament\Resources\Admin\SuperSalaryPeriodResource\RelationManagers;

use App\Models\Customer\Time\SuperSalaryPeriod\SuperSalaryPeriodDtl;
use App\Models\Customer\Time\SuperSalaryPeriod\SuperSalaryPeriodSubTwoDtl;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use App\Http\Tools\DateTool;
use Google\ApiCore\Call;
use Illuminate\Database\Eloquent\Model;


class SuperMainSalaryPeriodDtlRelationManager extends RelationManager
{
    protected static string $relationship = 'super_main_salary_period_dtl';
    protected static ?string $title = 'Үндсэн үечлэл';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make(SuperSalaryPeriodDtl::WD_COUNT)
                    ->label('Нийт ажиллах өдрийн тоо')
                    ->default(21)
                    ->numeric()
                    ->live()
                    ->afterStateUpdated(fn (callable $get, callable $set, ?string $state) => $set(SuperSalaryPeriodDtl::WD_TOTAL_TIME, $state * DateTool::convertMin($get(SuperSalaryPeriodDtl::WD_TIME))/60))
                    ->inputMode('decimal'),
                
                Forms\Components\TimePicker::make(SuperSalaryPeriodDtl::WD_TIME)
                    ->label('Нэг өдрийн ажиллах цаг')
                    ->default('08:00')
                    ->seconds(false)
                    ->afterStateUpdated(fn (callable $get, callable $set, ?string $state) => $set(SuperSalaryPeriodDtl::WD_TOTAL_TIME, DateTool::convertMin($state)/60 * $get(SuperSalaryPeriodDtl::WD_COUNT)))
                    ->live(),

                Forms\Components\TextInput::make(SuperSalaryPeriodDtl::WD_TOTAL_TIME)
                    ->label('Нийт ажиллах цаг')
                    ->default(168)
                    ->disabled(),
                Forms\Components\Repeater::make(SuperSalaryPeriodDtl::RELATION_SUPER_SALARY_PERIOD_SUB_TWO_DTLS)
                ->relationship()
                ->schema([
                    Forms\Components\DatePicker::make(SuperSalaryPeriodSubTwoDtl::BEGIN_DATE)
                        ->label('Эхлэх огноо')
                        ->default(DateTool::createDateWithFormat(now()->year, now()->month, 1))
                        ->live()
                        ->displayFormat('d/m/Y')
                        // ->disabled(function (callable $get, Forms\Components\Component $component) {
                        //     $items = $get('../../'.SuperSalaryPeriodDtl::RELATION_SUPER_SALARY_PERIOD_SUB_TWO_DTLS);
                        //     if (count($items) <= 1) 
                        //         return false;
                        //     $statePath = $component->getStatePath();
                        //     // statePath-оос индексийг гаргаж авах
                        //     preg_match('/mountedTableActionsData\.\d+\.super_salary_period_sub_two_dtls\.(.*?)\.begin_date/', $statePath, $matches);
                        //     if (isset($matches[1])) {
                        //         $recordId = $matches[1];
                        //         $idx      = 0;
                        //         $nextBeginDate = null;
                        //         foreach ($items as $key => $value) {
                        //             if ($idx == 0)
                        //                 $nextBeginDate = $items[$key]['end_date'];
                        //             else 
                        //                 $items[$key]['begin_date'] = DateTool::addDays($nextBeginDate, 1)->format('Y-m-d');
                        //             $idx++;
                        //         }
                        //         dd($items);
                        //         $key = array_search($recordId, array_keys($items));
                        //         return $key !== 0;
                        //     }
                        // })
                        ,

                    Forms\Components\DatePicker::make(SuperSalaryPeriodSubTwoDtl::END_DATE)
                        ->label('Дуусах огноо')
                        ->default(DateTool::getLastDayOfMonth(now()))
                        ->displayFormat('d/m/Y')
                        ->live(),
                ])
                ->maxItems(2)
                ->grid(2)
                ->label('Үечлэлийн задаргаа')
                ->addActionLabel('Үечлэл нэмэх')
                ->live(),
            ])->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->heading('Үечлэлийн задаргаа')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(SuperSalaryPeriodDtl::WD_COUNT)->label('Нийт ажиллах өдрийн тоо')->sortable(),
                Tables\Columns\TextColumn::make(SuperSalaryPeriodDtl::WD_TIME)->label('Нэг өдрийн ажиллах цаг'),
                Tables\Columns\TextColumn::make(SuperSalaryPeriodDtl::WD_TOTAL_TIME)->label('Нийт ажиллах цаг'),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make()
                    ->label('Үечлэл нэмэх')
                    ->icon('heroicon-m-plus')
                    ->mutateFormDataUsing(function (array $data): array {
                        $data[SuperSalaryPeriodDtl::TYPE]   = 0;
                        $data[SuperSalaryPeriodDtl::WD_MIN] = DateTool::convertMin($data[SuperSalaryPeriodDtl::WD_TIME]);
                        return $data;
                    }),
            ])
            ->filters([
                //
            ])
            ->headerActions([
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->icon('heroicon-m-pencil')
                    ->mutateRecordDataUsing(function (Model $record,array $data): array {
                        $data[SuperSalaryPeriodDtl::WD_TIME]       = DateTool::convertTime($record->wd_min);
                        $data[SuperSalaryPeriodDtl::WD_TOTAL_TIME] = $record->wd_count * $record->wd_min / 60;
                        return $data;
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
