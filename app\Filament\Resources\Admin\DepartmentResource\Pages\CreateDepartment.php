<?php

namespace App\Filament\Resources\Admin\DepartmentResource\Pages;

use App\Models\Customer\Department;
use App\Filament\Resources\Admin\DepartmentResource;
use Filament\Resources\Pages\CreateRecord;

class CreateDepartment extends CreateRecord
{
    protected static string $resource = DepartmentResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data[Department::CREATED_BY] = auth()->user()->id;
        return $data;
    }
}
