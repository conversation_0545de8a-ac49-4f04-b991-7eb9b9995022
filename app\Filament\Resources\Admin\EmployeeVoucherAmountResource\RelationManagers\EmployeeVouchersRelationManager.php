<?php

namespace App\Filament\Resources\Admin\EmployeeVoucherAmountResource\RelationManagers;

use App\Models\Customer\Voucher\EmployeeVoucher;
use App\Services\EmployeeVoucherService;
use App\Services\ConnectionService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Support\RawJs;


class EmployeeVouchersRelationManager extends RelationManager
{
    protected static string $relationship = 'employee_vouchers';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('amount')
                    ->label('Ваучерын дүн')
                    ->numeric()
                    ->inputMode('decimal')
                    ->mask(RawJs::make('$money($input)'))
                    ->stripCharacters(',')
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('code')
            ->heading('Ваучерууд')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(EmployeeVoucher::CODE)->label('Код')->copyable(),
                Tables\Columns\TextColumn::make(EmployeeVoucher::AMOUNT)->label('Дүн')->money('MNT')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(EmployeeVoucher::USED_AMOUNT)->label('Ашигласан дүн')->money('MNT')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(EmployeeVoucher::UNUSED_AMOUNT)->label('Ашиглаагүй дүн')->money('MNT')->sortable()->searchable(),
                Tables\Columns\ToggleColumn::make(EmployeeVoucher::IS_ACTIVE)->label('Идэвхтэй эсэх')
                    ->afterStateUpdated(function (EmployeeVoucher $record) {
                        $this->dispatch('employee-voucher-amount-changed');
                    }),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->action(function (array $data) {
                        $cn = ConnectionService::connectionNameByUser(auth()->user());
                        app(EmployeeVoucherService::class)->createEmployeeVoucherNew($cn, $this->getOwnerRecord()->employee_id, $data['amount']);
                        $this->dispatch('employee-voucher-amount-changed');
                    })
            ])
            ->actions([
                Tables\Actions\DeleteAction::make()->hidden(function (EmployeeVoucher $record) {
                    return $record->used_amount > 0;
                })
                ->after(function (EmployeeVoucher $record) {
                    $this->dispatch('employee-voucher-amount-changed');
                }),
            ])
            ->bulkActions([
            ]);
    }
}
