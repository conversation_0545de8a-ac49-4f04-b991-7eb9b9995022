<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('employee_voucher_spent_dtls'))
            return;
        Schema::create('employee_voucher_spent_dtls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_voucher_spent_id')->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('employee_voucher_id');
            $table->unsignedDecimal('amount', $precision = 24, $scale = 2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employee_voucher_spent_dtls');
    }
};
