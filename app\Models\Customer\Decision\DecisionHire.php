<?php

namespace App\Models\Customer\Decision;

use App\Models\Customer\Decision\Decision;

use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DecisionHire extends Model
{
    use HasFactory;

    const TABLE = 'decision_hires';

    const ID                   = 'id';
    const DECISION_ID          = 'decision_id';
    const HIRE_DATE            = 'hire_date';
    const CREATED_BY           = 'created_by';
    const UPDATED_BY           = 'updated_by';

    const RELATION_DECISION    = 'decision';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::ID,
        self::DECISION_ID,
        self::HIRE_DATE,
        self::CREATED_BY,
    ];

    public function decision() {
        return $this->belongsTo(Decision::class);
    }
}
