<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSalaryPeriodSubDtlsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('salary_period_sub_dtls'))
            return;
        Schema::create('salary_period_sub_dtls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('salary_period_dtl_id')->constrained('salary_period_dtls')->onDelete('cascade');
            $table->date('salary_date');
            $table->unsignedBigInteger('status');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('salary_period_sub_dtls');
    }
}
