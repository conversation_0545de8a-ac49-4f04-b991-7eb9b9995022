<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePasswordRequest extends FormRequest
{
    const PARAMETER_PHONE        = 'phone';
    const PARAMETER_OLD_PASSWORD = 'old_password';
    const PARAMETER_PASSWORD     = 'password';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::PARAMETER_PHONE        => 'required|integer|digits:8',
            self::PARAMETER_OLD_PASSWORD => 'required',
            self::PARAMETER_PASSWORD     => 'required',
        ];
    }
}
