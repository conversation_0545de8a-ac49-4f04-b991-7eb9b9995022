<?php

namespace App\Models;

use App\Models\Constant\ConstData;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


/**
 * @mixin IdeHelperHoroo
 */
class Khoroo extends Model
{
    use HasFactory;
    protected $connection = ConstData::ADMIN_DB;
    const TABLE           = 'khoroos';

    const ID            = 'id';
    const CITY_ID       = 'city_id';
    const DISTRICT_ID   = 'district_id';
    const NAME          = 'name';

    public static function getKhoroosByDistrictId($cityId, $districtId)
    {
        if (!isset($districtId))
            return [];
        return Khoroo::where(Khoroo::CITY_ID, $cityId)->where(Khoroo::DISTRICT_ID, $districtId)->get()->pluck(self::NAME, self::ID);
    }
}
