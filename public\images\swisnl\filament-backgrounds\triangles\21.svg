<svg xmlns='http://www.w3.org/2000/svg' width='1920' height='1080'><path d='M930.9,465.5L710.3,432.8L959.1,664.9Z' fill='rgb(121,181,217)' shape-rendering='crispEdges'></path><path d='M710.3,432.8L609.9,636L959.1,664.9Z' fill='rgb(125,182,216)' shape-rendering='crispEdges'></path><path d='M609.9,636L589,1023.7L959.1,664.9Z' fill='rgb(108,161,205)' shape-rendering='crispEdges'></path><path d='M901.3,1194.6L1264.2,832.1L959.1,664.9Z' fill='rgb(64,127,187)' shape-rendering='crispEdges'></path><path d='M959.1,664.9L1322.1,462.8L930.9,465.5Z' fill='rgb(100,168,211)' shape-rendering='crispEdges'></path><path d='M1264.2,832.1L1322.1,462.8L959.1,664.9Z' fill='rgb(72,149,200)' shape-rendering='crispEdges'></path><path d='M930.9,465.5L677.1,2.7L710.3,432.8Z' fill='rgb(169,205,229)' shape-rendering='crispEdges'></path><path d='M98,394.4L280.8,817.5L609.9,636Z' fill='rgb(154,193,225)' shape-rendering='crispEdges'></path><path d='M609.9,636L280.8,817.5L589,1023.7Z' fill='rgb(125,162,208)' shape-rendering='crispEdges'></path><path d='M831.7,-89.7L677.1,2.7L930.9,465.5Z' fill='rgb(182,213,234)' shape-rendering='crispEdges'></path><path d='M710.3,432.8L98,394.4L609.9,636Z' fill='rgb(164,202,229)' shape-rendering='crispEdges'></path><path d='M589,1023.7L901.3,1194.6L959.1,664.9Z' fill='rgb(86,131,185)' shape-rendering='crispEdges'></path><path d='M1201.7,-48.3L831.7,-89.7L930.9,465.5Z' fill='rgb(168,204,230)' shape-rendering='crispEdges'></path><path d='M677.1,2.7L98,394.4L710.3,432.8Z' fill='rgb(196,218,238)' shape-rendering='crispEdges'></path><path d='M1322.1,462.8L1201.7,-48.3L930.9,465.5Z' fill='rgb(138,183,219)' shape-rendering='crispEdges'></path><path d='M901.3,1194.6L1441.1,1034L1264.2,832.1Z' fill='rgb(42,103,163)' shape-rendering='crispEdges'></path><path d='M1264.2,832.1L1687.4,464.2L1322.1,462.8Z' fill='rgb(69,139,195)' shape-rendering='crispEdges'></path><path d='M589,1023.7L878.5,1408.7L901.3,1194.6Z' fill='rgb(83,117,163)' shape-rendering='crispEdges'></path><path d='M901.3,1194.6L1469.8,1390.5L1441.1,1034Z' fill='rgb(36,90,149)' shape-rendering='crispEdges'></path><path d='M485.9,1581.9L878.5,1408.7L589,1023.7Z' fill='rgb(96,122,167)' shape-rendering='crispEdges'></path><path d='M1771.9,854.1L1687.4,464.2L1264.2,832.1Z' fill='rgb(42,115,180)' shape-rendering='crispEdges'></path><path d='M1322.1,462.8L1801.6,42.7L1201.7,-48.3Z' fill='rgb(139,171,213)' shape-rendering='crispEdges'></path><path d='M831.7,-89.7L584.7,-298L677.1,2.7Z' fill='rgb(205,227,241)' shape-rendering='crispEdges'></path><path d='M677.1,2.7L234.8,-176.9L98,394.4Z' fill='rgb(223,235,247)' shape-rendering='crispEdges'></path><path d='M1094.7,-281.5L584.7,-298L831.7,-89.7Z' fill='rgb(192,219,237)' shape-rendering='crispEdges'></path><path d='M1465.3,-407.8L1094.7,-281.5L1201.7,-48.3Z' fill='rgb(162,193,225)' shape-rendering='crispEdges'></path><path d='M1201.7,-48.3L1094.7,-281.5L831.7,-89.7Z' fill='rgb(175,207,232)' shape-rendering='crispEdges'></path><path d='M584.7,-298L234.8,-176.9L677.1,2.7Z' fill='rgb(221,234,246)' shape-rendering='crispEdges'></path><path d='M-159.3,686.5L87.3,1222.3L280.8,817.5Z' fill='rgb(144,164,208)' shape-rendering='crispEdges'></path><path d='M280.8,817.5L87.3,1222.3L589,1023.7Z' fill='rgb(123,141,185)' shape-rendering='crispEdges'></path><path d='M98,394.4L-159.3,686.5L280.8,817.5Z' fill='rgb(166,199,228)' shape-rendering='crispEdges'></path><path d='M-152.1,227.1L-159.3,686.5L98,394.4Z' fill='rgb(198,223,239)' shape-rendering='crispEdges'></path><path d='M1863,1075.1L1771.9,854.1L1441.1,1034Z' fill='rgb(8,75,147)' shape-rendering='crispEdges'></path><path d='M1441.1,1034L1771.9,854.1L1264.2,832.1Z' fill='rgb(24,98,169)' shape-rendering='crispEdges'></path><path d='M-157.4,61.5L-152.1,227.1L98,394.4Z' fill='rgb(226,237,248)' shape-rendering='crispEdges'></path><path d='M167.9,1479L485.9,1581.9L589,1023.7Z' fill='rgb(114,130,172)' shape-rendering='crispEdges'></path><path d='M1681.5,1552.3L1469.8,1390.5L878.5,1408.7Z' fill='rgb(30,86,146)' shape-rendering='crispEdges'></path><path d='M878.5,1408.7L1469.8,1390.5L901.3,1194.6Z' fill='rgb(52,101,155)' shape-rendering='crispEdges'></path><path d='M234.8,-176.9L-157.4,61.5L98,394.4Z' fill='rgb(235,244,251)' shape-rendering='crispEdges'></path><path d='M-491.7,1084.4L-248.6,1252.8L-159.3,686.5Z' fill='rgb(141,152,193)' shape-rendering='crispEdges'></path><path d='M87.3,1222.3L167.9,1479L589,1023.7Z' fill='rgb(120,134,175)' shape-rendering='crispEdges'></path><path d='M-270.7,1462.1L167.9,1479L87.3,1222.3Z' fill='rgb(135,142,179)' shape-rendering='crispEdges'></path><path d='M584.7,-298L212.1,-380.9L234.8,-176.9Z' fill='rgb(229,239,249)' shape-rendering='crispEdges'></path><path d='M234.8,-176.9L-72,-477.6L-157.4,61.5Z' fill='rgb(247,251,255)' shape-rendering='crispEdges'></path><path d='M1465.3,-407.8L212.1,-380.9L584.7,-298Z' fill='rgb(200,224,239)' shape-rendering='crispEdges'></path><path d='M2023.3,456.7L1801.6,42.7L1687.4,464.2Z' fill='rgb(106,131,178)' shape-rendering='crispEdges'></path><path d='M1687.4,464.2L1801.6,42.7L1322.1,462.8Z' fill='rgb(114,149,199)' shape-rendering='crispEdges'></path><path d='M1469.8,1390.5L1863,1075.1L1441.1,1034Z' fill='rgb(14,70,136)' shape-rendering='crispEdges'></path><path d='M1771.9,854.1L2023.3,456.7L1687.4,464.2Z' fill='rgb(57,110,166)' shape-rendering='crispEdges'></path><path d='M1801.6,42.7L1465.3,-407.8L1201.7,-48.3Z' fill='rgb(152,175,215)' shape-rendering='crispEdges'></path><path d='M1094.7,-281.5L1465.3,-407.8L584.7,-298Z' fill='rgb(175,206,232)' shape-rendering='crispEdges'></path><path d='M2175.4,821.8L2023.3,456.7L1771.9,854.1Z' fill='rgb(36,91,149)' shape-rendering='crispEdges'></path><path d='M-491.7,1084.4L-159.3,686.5L-653.1,649.2Z' fill='rgb(154,180,218)' shape-rendering='crispEdges'></path><path d='M-159.3,686.5L-248.6,1252.8L87.3,1222.3Z' fill='rgb(137,146,184)' shape-rendering='crispEdges'></path><path d='M485.9,1581.9L1681.5,1552.3L878.5,1408.7Z' fill='rgb(58,105,157)' shape-rendering='crispEdges'></path><path d='M1469.8,1390.5L1681.5,1552.3L1863,1075.1Z' fill='rgb(9,65,132)' shape-rendering='crispEdges'></path><path d='M2023.3,456.7L1980.8,-43.1L1801.6,42.7Z' fill='rgb(121,134,175)' shape-rendering='crispEdges'></path><path d='M1801.6,42.7L1771.7,-490.6L1465.3,-407.8Z' fill='rgb(146,161,205)' shape-rendering='crispEdges'></path><path d='M1465.3,-407.8L-72,-477.6L212.1,-380.9Z' fill='rgb(218,233,245)' shape-rendering='crispEdges'></path><path d='M212.1,-380.9L-72,-477.6L234.8,-176.9Z' fill='rgb(241,247,253)' shape-rendering='crispEdges'></path><path d='M-435.3,1376.8L-270.7,1462.1L-248.6,1252.8Z' fill='rgb(135,142,179)' shape-rendering='crispEdges'></path><path d='M-248.6,1252.8L-270.7,1462.1L87.3,1222.3Z' fill='rgb(135,142,179)' shape-rendering='crispEdges'></path><path d='M167.9,1479L-270.7,1462.1L485.9,1581.9Z' fill='rgb(128,138,177)' shape-rendering='crispEdges'></path><path d='M-587.4,389.5L-159.3,686.5L-152.1,227.1Z' fill='rgb(198,223,239)' shape-rendering='crispEdges'></path><path d='M-587.4,389.5L-152.1,227.1L-582.8,-81Z' fill='rgb(231,240,250)' shape-rendering='crispEdges'></path><path d='M2111.6,1226L2175.4,821.8L1863,1075.1Z' fill='rgb(8,53,114)' shape-rendering='crispEdges'></path><path d='M1863,1075.1L2175.4,821.8L1771.9,854.1Z' fill='rgb(12,67,134)' shape-rendering='crispEdges'></path><path d='M2023.3,456.7L2508.9,291.9L1980.8,-43.1Z' fill='rgb(114,130,172)' shape-rendering='crispEdges'></path><path d='M-582.8,-81L-152.1,227.1L-157.4,61.5Z' fill='rgb(241,247,253)' shape-rendering='crispEdges'></path><path d='M1980.8,-43.1L1771.7,-490.6L1801.6,42.7Z' fill='rgb(138,148,187)' shape-rendering='crispEdges'></path><path d='M1465.3,-407.8L1771.7,-490.6L-72,-477.6Z' fill='rgb(174,206,231)' shape-rendering='crispEdges'></path><path d='M-587.4,389.5L-653.1,649.2L-159.3,686.5Z' fill='rgb(176,208,232)' shape-rendering='crispEdges'></path><path d='M1681.5,1552.3L2111.6,1226L1863,1075.1Z' fill='rgb(8,50,110)' shape-rendering='crispEdges'></path><path d='M-653.1,649.2L-435.3,1376.8L-491.7,1084.4Z' fill='rgb(138,148,187)' shape-rendering='crispEdges'></path><path d='M-491.7,1084.4L-435.3,1376.8L-248.6,1252.8Z' fill='rgb(135,142,179)' shape-rendering='crispEdges'></path><path d='M-540.6,-400.2L-582.8,-81L-157.4,61.5Z' fill='rgb(247,251,255)' shape-rendering='crispEdges'></path><path d='M-587.4,389.5L-582.8,-81L-653.1,649.2Z' fill='rgb(216,232,244)' shape-rendering='crispEdges'></path><path d='M1681.5,1552.3L2149.2,1500L2111.6,1226Z' fill='rgb(8,48,107)' shape-rendering='crispEdges'></path><path d='M2399,1229.6L2448.7,706.2L2175.4,821.8Z' fill='rgb(12,67,133)' shape-rendering='crispEdges'></path><path d='M2399,1229.6L2175.4,821.8L2111.6,1226Z' fill='rgb(8,48,107)' shape-rendering='crispEdges'></path><path d='M2175.4,821.8L2448.7,706.2L2023.3,456.7Z' fill='rgb(43,96,152)' shape-rendering='crispEdges'></path><path d='M1980.8,-43.1L2246.8,-525.3L1771.7,-490.6Z' fill='rgb(135,142,179)' shape-rendering='crispEdges'></path><path d='M-72,-477.6L-540.6,-400.2L-157.4,61.5Z' fill='rgb(247,251,255)' shape-rendering='crispEdges'></path><path d='M2149.2,1500L2399,1229.6L2111.6,1226Z' fill='rgb(8,48,107)' shape-rendering='crispEdges'></path><path d='M2399,1229.6L2508.9,291.9L2448.7,706.2Z' fill='rgb(32,87,147)' shape-rendering='crispEdges'></path><path d='M2448.7,706.2L2508.9,291.9L2023.3,456.7Z' fill='rgb(75,113,161)' shape-rendering='crispEdges'></path><path d='M2508.9,291.9L2469.8,-64.1L1980.8,-43.1Z' fill='rgb(129,139,177)' shape-rendering='crispEdges'></path><path d='M2469.8,-64.1L2246.8,-525.3L1980.8,-43.1Z' fill='rgb(135,142,179)' shape-rendering='crispEdges'></path><path d='M1771.7,-490.6L2246.8,-525.3L-72,-477.6Z' fill='rgb(159,188,223)' shape-rendering='crispEdges'></path><path d='M2149.2,1500L2367.6,1589.6L2399,1229.6Z' fill='rgb(8,48,107)' shape-rendering='crispEdges'></path><path d='M2399,1229.6L2367.6,1589.6L2508.9,291.9Z' fill='rgb(8,53,115)' shape-rendering='crispEdges'></path><path d='M1681.5,1552.3L2367.6,1589.6L2149.2,1500Z' fill='rgb(8,48,107)' shape-rendering='crispEdges'></path><path d='M485.9,1581.9L2367.6,1589.6L1681.5,1552.3Z' fill='rgb(18,75,140)' shape-rendering='crispEdges'></path><path d='M2508.9,291.9L2562.2,-326.8L2469.8,-64.1Z' fill='rgb(135,142,179)' shape-rendering='crispEdges'></path><path d='M2469.8,-64.1L2562.2,-326.8L2246.8,-525.3Z' fill='rgb(135,142,179)' shape-rendering='crispEdges'></path></svg>