<?php

namespace App\Filament\Resources\Admin\OtherTimeScheduleResource\Pages;

use App\Filament\Resources\Admin\OtherTimeScheduleResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListOtherTimeSchedules extends ListRecords
{
    protected static string $resource = OtherTimeScheduleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх'),
        ];
    }
}
