<?php

namespace App\Models\Class;

use App\Models\Constant\ConstData;
use App\Services\RoleResourceService;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Resource;

class Can extends Resource
{
    public static function canViewAny(): bool
    {
        return resolve(RoleResourceService::class)->canView(static::class);
    }

    public static function canView(Model $record): bool
    {
        return auth()->user()?->can(ConstData::PERMISSION_VIEW);
    }

    public static function canCreate(): bool
    {
        return auth()->user()?->can(ConstData::PERMISSION_CREATE);
    }

    public static function canEdit($record): bool
    {
        return auth()->user()?->can(ConstData::PERMISSION_EDIT);
    }

    public static function canDelete($record): bool
    {
        return auth()->user()?->can(ConstData::PERMISSION_DELETE);
    }
}
