<?php

namespace App\Http\Resources;

use App\Models\Customer\Decision\Decision as DecisionModel;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class Decision
 *
 * @mixin \App\Models\Customer\Time\Decision\Decision
 */
class Decision extends JsonResource
{
    const ID                                    = 'id';
    const NUMBER                                = 'number';
    const TYPE                                  = 'type';
    const EMPLOYEE_ID                           = 'employee_id';
    const DECISION_DATE                         = 'decision_date';
    const DECISION_HIRE                         = 'hire';
    const DECISION_FIRE                         = 'fire';
    const DECISION_LONG_TERM_FURLOUGH           = 'long_term_furlough';
    const DECISION_MATERNITIES                  = 'maternities';
    const DECISION_VACATIONS                    = 'vacations';
    const EMPLOYEE                              = 'employee';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                            => $this->id,
            self::NUMBER                        => $this->number,
            self::TYPE                          => $this->type,
            self::DECISION_DATE                 => $this->decision_date,
            self::EMPLOYEE_ID                   => $this->employee_id,
            self::EMPLOYEE                      => new Employee($this->employee),
            self::DECISION_HIRE                 => new DecisionHire($this->hire),
            self::DECISION_FIRE                 => new DecisionFire($this->fire),
            self::DECISION_LONG_TERM_FURLOUGH   => new DecisionLongTermFurlough($this->long_term_furlough),
            self::DECISION_MATERNITIES          => DecisionMaternity::collection($this->maternities),
            self::DECISION_VACATIONS            => DecisionVacation::collection($this->vacations),
        ];
    }
}
