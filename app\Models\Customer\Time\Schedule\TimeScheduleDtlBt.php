<?php

namespace App\Models\Customer\Time\Schedule;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TimeScheduleDtlBt extends Model
{
    use HasFactory;
    const TABLE = 'time_schedule_dtl_bts';

    const ID                            = 'id';
    const TIME_SCHEDULE_DTL_ID          = 'time_schedule_dtl_id';
    const BEGIN_TIME_STR                = 'begin_time_str';
    const END_TIME_STR                  = 'end_time_str';
    const CHECK_IO                      = 'check_io';
    const CREATED_BY                    = 'created_by';
    const UPDATED_BY                    = 'updated_by';

    protected $casts = [
        self::CHECK_IO   => 'boolean',
    ];

    protected $fillable = [
        self::ID,
        self::TIME_SCHEDULE_DTL_ID,
        self::BEGIN_TIME_STR,
        self::END_TIME_STR,
        self::CHECK_IO,
        self::CREATED_BY,
        self::UPDATED_BY,
    ];

    public function getHashStr() {;
        return $this->begin_time_str.'-'.$this->end_time_str.'-'.($this->check_io?1:0);
    }
}
