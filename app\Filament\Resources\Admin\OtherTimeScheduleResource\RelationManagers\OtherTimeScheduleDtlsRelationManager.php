<?php

namespace App\Filament\Resources\Admin\OtherTimeScheduleResource\RelationManagers;

use App\Models\Customer\Time\Schedule\OtherTimeSchedule\OtherTimeScheduleDtl;
use App\Models\Customer\Time\Schedule\OtherTimeSchedule\OtherTimeScheduleDtlBt;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class OtherTimeScheduleDtlsRelationManager extends RelationManager
{
    protected static string $relationship = 'time_schedule_dtls';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('getDayOfWeekNameAttribute')
                            ->label('Гараг')
                            ->disabled()
                            ->formatStateUsing(fn (OtherTimeScheduleDtl $record): string =>  $record->getDayOfWeekNameAttribute()),

                        Forms\Components\ToggleButtons::make('is_holiday')
                            ->label('Ажиллах эсэх')
                            ->inline()
                            ->options([
                                false => 'Ажиллана',
                                 true => 'Амарна'
                            ])
                            ->icons([
                                false => 'heroicon-o-briefcase',
                                 true => 'heroicon-o-home',
                            ])->live(),

                        Forms\Components\TextInput::make('admit_late_min')
                            ->label('Өршөөлийн минут')
                            ->numeric()
                            ->inputMode('decimal')
                            ->hidden(function (callable $get) {
                                return $get('is_holiday');
                            }),

                        Forms\Components\TextInput::make('undurluguu_min')
                            ->label('Өндөрлөгөөний минут')
                            ->minValue(60)
                            ->numeric()
                            ->inputMode('decimal')
                            ->placeholder('60')
                            ->hidden(function (callable $get) {
                                return $get('is_holiday');
                            }),

                        Forms\Components\Toggle::make('is_dynamic')
                            ->label('Уян цаг эсэх')
                            ->default(false)
                            ->inline(false)
                            ->onIcon('heroicon-m-bolt')
                            ->offIcon('heroicon-m-bolt-slash')
                            ->live()
                            ->hidden(function (callable $get) {
                                return $get('is_holiday');
                            }),

                        Forms\Components\Placeholder::make('')->label(''),

                        Forms\Components\TimePicker::make('begin_time_str')
                            ->label('Эхлэх цаг')
                            ->seconds(false)
                            ->hidden(function (callable $get) {
                                return $get('is_dynamic') || $get('is_holiday');
                            })
                            ->live(),

                        Forms\Components\TimePicker::make('end_time_str')
                            ->label('Дуусах цаг')
                            ->seconds(false)
                            ->hidden(function (callable $get) {
                                return $get('is_dynamic') || $get('is_holiday');
                            })
                            ->minDate(function (callable $get) {
                                return $get('begin_time_str');
                            })
                            ->live(),

                        Forms\Components\TimePicker::make('rb_time_str')
                            ->label('Уян эхлэх цаг')
                            ->seconds(false)
                            ->hidden(function (callable $get) {
                                return !$get('is_dynamic') || $get('is_holiday');
                            })
                            ->live(),

                        Forms\Components\TimePicker::make('re_time_str')
                            ->label('Уян дуусах цаг')
                            ->seconds(false)
                            ->hidden(function (callable $get) {
                                return !$get('is_dynamic') || $get('is_holiday');
                            })
                            ->minDate(function (callable $get) {
                                return $get('rb_time_str');
                            })
                            ->live(),

                        Forms\Components\TextInput::make('work_min')
                            ->label('Ажиллах минут')
                            ->default(0)
                            ->minValue(60)
                            ->numeric()
                            ->inputMode('decimal')
                            ->hidden(function (callable $get) {
                                return !$get('is_dynamic') || $get('is_holiday');
                            }),

                        Forms\Components\Toggle::make('has_break_time')
                            ->label('Цайны цагтай эсэх')
                            ->inline(false)
                            ->onIcon('heroicon-m-bolt')
                            ->offIcon('heroicon-m-bolt-slash')
                            ->hidden(function (callable $get) {
                                return $get('is_holiday');
                            })
                            ->live(),
                    ]),
                Forms\Components\Grid::make(1)
                    ->schema([
                        Forms\Components\Repeater::make('time_schedule_dtl_bts')
                            ->label('Цайны цаг')
                            ->schema([
                                Forms\Components\TimePicker::make('begin_time_str')
                                    ->label('Эхлэх цаг')
                                    ->seconds(false)
                                    ->live(),

                                Forms\Components\TimePicker::make('end_time_str')
                                    ->label('Дуусах цаг')
                                    ->seconds(false)
                                    ->minDate(function (callable $get) {
                                        return $get('begin_time_str');
                                    })
                                    ->live(),

                                Forms\Components\Toggle::make('check_io')
                                    ->label('Цайны цагаар бүртгүүлэх эсэх')
                                    ->default(false)
                                    ->onIcon('heroicon-m-bolt')
                                    ->offIcon('heroicon-m-bolt-slash'),
                                    ])
                            ->defaultItems(1)
                            ->addActionLabel('Цайны цаг нэмэх')
                            ->maxItems(2)
                            ->grid(2)
                            ->columns(2)
                    ])
                    ->hidden(function (callable $get) {
                        return !$get('has_break_time') || $get('is_holiday');
                    }),
                ])->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('dayOfWeekName')->label('Гараг'),
                Tables\Columns\BooleanColumn::make('typeName')->label('Ажиллах эсэх')
                    ->icon(fn (OtherTimeScheduleDtl $record): string => $record->type == 1 || $record->type == 2 ? 'heroicon-o-briefcase' : 'heroicon-o-home')
                    ->color(fn (OtherTimeScheduleDtl $record): string => $record->type == 1 || $record->type == 2 ? 'success' : 'gray'),
                Tables\Columns\BooleanColumn::make('has_break_time')->label('Цайны цагтай эсэх'),
            ])
            ->heading('Гараг')
            ->description('7 хоногийн гарагуудын тохиргоо')
            ->filters([
                //
            ])
            ->headerActions([
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateRecordDataUsing(function (OtherTimeScheduleDtl $record, array $data): array {
                        $data[OtherTimeScheduleDtl::IS_DYNAMIC] = $data[OtherTimeScheduleDtl::TYPE] == OtherTimeScheduleDtl::TYPE_DYNAMIC;
                        $data[OtherTimeScheduleDtl::RELATION_TIME_SCHEDULE_DTL_BTS] = $record->time_schedule_dtl_bts;
                        $data[OtherTimeScheduleDtl::IS_HOLIDAY] = $data[OtherTimeScheduleDtl::TYPE] == OtherTimeScheduleDtl::TYPE_HOLIDAY;
                        $data[OtherTimeScheduleDtl::UPDATED_BY] = auth()->user()->id;
                        return $data;
                    })
                    ->after(function (OtherTimeScheduleDtl $record, array $data): void {
                        $isHoliday    = $data[OtherTimeScheduleDtl::IS_HOLIDAY];
                        $record->type = $isHoliday ? OtherTimeScheduleDtl::TYPE_HOLIDAY : ($data[OtherTimeScheduleDtl::IS_DYNAMIC] ? OtherTimeScheduleDtl::TYPE_DYNAMIC : OtherTimeScheduleDtl::TYPE_SIMPLE);
                        if ($record->has_break_time && $isHoliday)
                            $record->has_break_time = false;

                        $record->time_schedule_dtl_bts()->delete();
                        if ($record->has_break_time) {
                            foreach ($data[OtherTimeScheduleDtl::RELATION_TIME_SCHEDULE_DTL_BTS] as $key => $value) {
                                $record->time_schedule_dtl_bts()->save(new OtherTimeScheduleDtlBt([
                                    OtherTimeScheduleDtlBt::BEGIN_TIME_STR => $value[OtherTimeScheduleDtlBt::BEGIN_TIME_STR],
                                    OtherTimeScheduleDtlBt::END_TIME_STR   => $value[OtherTimeScheduleDtlBt::END_TIME_STR],
                                    OtherTimeScheduleDtlBt::CHECK_IO       => $value[OtherTimeScheduleDtlBt::CHECK_IO],
                                    OtherTimeScheduleDtlBt::CREATED_BY     => auth()->user()->id
                                ]));
                            }
                        }
                        $record->save();
                    })
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                ]),
            ]);
    }
}
