<?php

namespace App\Models;

use App\Models\Constant\ConstData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Report extends Model
{
    use HasFactory;

    protected $connection = ConstData::ADMIN_DB;
    const TABLE           = 'organizations';

    const ID              = 'id';
    const NAME            = 'name';

    const DEPARTMENTS     = 'departments';
    const IS_FULL_MONTH   = 'is_full_month';
    const HAS_DETAIL      = 'has_detail';

    const IS_MAIN                            = 'is_main';
    const SUPER_SALARY_PERIOD_ID             = 'super_salary_period_id';
    const SUPER_SALARY_PERIOD_DTL_ID         = 'super_salary_period_dtl_id';
    const SUPER_SALARY_PERIOD_SUB_TWO_DTL_ID = 'super_salary_period_sub_two_dtl_id';

    const RELATED_DEPARTMENTS = 'related_departments';

    const BEGIN_DATE = 'begin_date';
    const END_DATE   = 'end_date';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        self::NAME,
    ];
}
