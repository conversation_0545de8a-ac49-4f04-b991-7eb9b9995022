<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class EmployeeVoucherAmount extends JsonResource
{
    const ID                  = 'id';
    const UNUSED_TOTAL_AMOUNT = 'unused_total_amount';
    const USED_TOTAL_AMOUNT   = 'used_total_amount';
    const TOPIC               = 'topic';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'                  => $this->id,
            'unused_total_amount' => (float) $this->unused_total_amount,
            'used_total_amount'   => (float) $this->used_total_amount,
            'topic'               => $this->topic
        ];
    }
}
