<?php

namespace App\Models\Customer\Time;

use App\Models\Customer\Employee;
use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class DeviceConfig extends Model
{
    use HasFactory;
    const TABLE = 'device_configs';

    const ID                    = 'id';
    const NAME                  = 'name';
    const SERIAL_NUMBER         = 'serial_number';
    const LAST_DOWNLOAD_DATE    = 'last_download_date';
    const CREATED_BY            = 'created_by';
    const UPDATED_BY            = 'updated_by';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::ID,
        self::NAME,
        self::SERIAL_NUMBER,
        self::LAST_DOWNLOAD_DATE,
        self::CREATED_BY,
    ];

    public function employees(): BelongsToMany
    {
        return $this->belongsToMany(Employee::class, 'employee_device_config');
    }
}
