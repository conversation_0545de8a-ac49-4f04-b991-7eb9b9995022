<?php

namespace App\Filament\Resources\Admin\BenefitGroupResource\Pages;

use App\Filament\Resources\Admin\BenefitGroupResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBenefitGroups extends ListRecords
{
    protected static string $resource = BenefitGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
