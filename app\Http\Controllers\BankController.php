<?php

namespace App\Http\Controllers;

use App\Models\Bank;
use App\Http\Requests\GetBankRequest;
use App\Http\Resources\Bank as BankResource;
use Illuminate\Support\Arr;

class BankController extends Controller
{
    public function index(GetBankRequest $request)
    {
        $limit     = $request->input(GetBankRequest::LIMIT, 10);
        $filters   = $request->input(GetBankRequest::FILTER, []);
        $name      = Arr::get($filters, GetBankRequest::FILTER_NAME);
        $shortName = Arr::get($filters, GetBankRequest::FILTER_SHORT_NAME);

        $sort      = $request->input(GetBankRequest::SORT, []);
        $sortField = Arr::get($sort, GetBankRequest::SORT_FIELD);
        $sortType  = Arr::get($sort, GetBankRequest::SORT_TYPE, 'asc');

        $banks = Bank::query()
            ->when(!is_null($name), fn($q) => $q->where(Bank::NAME, $name))
            ->when(!is_null($shortName), fn($q) => $q->where(Bank::NAME, $shortName));

        if ($sortField && $sortType)
            $banks->orderBy($sortField, $sortType);
        return BankResource::collection($banks->paginate($limit));
    }
}
