<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class Apartment
 *
 * @mixin \App\Models\Apartment
 */
class Employee extends JsonResource
{
    const ID                            = 'id';
    const LAST_NAME                     = 'last_name';
    const FIRST_NAME                    = 'first_name';
    const CITIZEN_CODE                  = 'citizen_code';
    const PHONE                         = 'phone';
    const STATUS                        = 'status';
    const DEPARTMENT                    = 'department';
    const POSITION                      = 'position';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'                        => $this->id,
            'last_name'                 => $this->last_name,
            'first_name'                => $this->first_name,
            'citizen_code'              => $this->citizen_code,
            'phone'                     => $this->phone,
            'status'                    => $this->status,
            'department'                => new DepartmentMini($this->department),
            'position'                  => new PositionMini($this->position),

        ];
    }
}
