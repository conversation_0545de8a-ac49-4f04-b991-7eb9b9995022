<?php

namespace App\Models\Customer\CashRequest;

use App\Models\Customer\Department;
use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CashRequestConfig extends Model
{
    use HasFactory;

    const DEPARTMENT_ID = 'department_id';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::DEPARTMENT_ID,
    ];

    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    public function cash_request_config_dtls()
    {
        return $this->hasMany(CashRequestConfigDtl::class)->orderBy(CashRequestConfigDtl::LIMIT_AMOUNT);
    }
}
