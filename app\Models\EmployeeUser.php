<?php

namespace App\Models;

use App\Models\Customer\Employee;
use App\Models\Constant\ConstData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Laravel\Sanctum\HasApiTokens;

class EmployeeUser extends Model
{
    use HasFactory, HasApiTokens;
    protected $connection = ConstData::ADMIN_DB;

    const ID                        = 'id';
    const ORGANIZATION_ID           = 'organization_id';
    const PHONE                     = 'phone';
    const PASSWORD                  = 'password';
    const EMPLOYEE_ID               = 'employee_id';

    const RELATION_EMPLOYEE_ROLES   = 'employee_roles';

    protected $fillable = [
        self::ID,
        self::OR<PERSON><PERSON><PERSON>ATION_ID,
        self::PHONE,
        self::PASSWORD,
        self::EMPLOYEE_ID
    ];

    protected $hidden = [
        self::PASSWORD,
    ];

    protected $casts = [
        self::PASSWORD => 'hashed',
    ];

    public function organization() 
    {
        return $this->belongsTo(Organization::class);
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id');
    }

    public function employee_roles()
    {
        return $this->belongsToMany(EmployeeRole::class, 'employee_user_role');
    }

    public function getCN() 
    {
        return $this->organization->database_config->connection_name;
    }

    public function getEmployeeDepartmentName() 
    {
        return Employee::on($this->getCN())->find($this->employee_id)->department->name;
    }


    public function getEmployeePositionName() 
    {
        return Employee::on($this->getCN())->find($this->employee_id)->position->name;
    }

    public function getEmployeeDisplayName()
    {
        return Employee::on($this->getCN())->find($this->employee_id)->full_name;
    }
}
