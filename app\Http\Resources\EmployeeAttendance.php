<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class EmployeeAttendance
 *
 * @mixin \App\Models\EmployeeAttendance
 */
class EmployeeAttendance extends JsonResource
{
    const ID                                = 'id';
    const ATT_DATE                          = 'att_date';
    // const EMPLOYEE_ID                       = 'employee_id';
    // const TIME_STRING                       = 'time_string';
    // const SCHEDULE_TIME                     = 'schedule_time';
    // const DEVICE_ATTENDANCE_STRIDS          = 'device_attendance_strids';
    // const WORK_TIME                         = 'work_time';
    // const EMPLOYEE                          = 'employee';
    const EMPLOYEE_ATTENDANCE_DTLS          = 'employee_attendance_dtls';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                                => $this->id,
            self::ATT_DATE                          => $this->att_date,
            // self::EMPLOYEE                          => new Employee($this->employee),
            // self::TIME_STRING                       => $this->time_string,
            // self::SCHEDULE_TIME                     => $this->schedule_time,
            // self::DEVICE_ATTENDANCE_STRIDS          => $this->device_attendance_strids,
            // self::WORK_TIME                         => $this->getUnitWorkTime(),
            self::EMPLOYEE_ATTENDANCE_DTLS          => EmployeeAttendanceDtl::collection($this->employee_attendance_dtls),
        ];
    }
}
