<?php

namespace App\Http\Resources;

use App\Http\Tools\DateTool;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NotWorkTimeSubDetail extends JsonResource
{
   const ID                     = 'id';
   const BEGIN_DATE             = 'begin_date';
   const END_DATE               = 'end_date';
   const ATTENDANCE_STATUS_NAME = 'attendance_status_name';

   public function toArray(Request $request): array
   {
       return [
           'id'                     => $this->id,
           'begin_date'             => DateTool::getDateWithTime($this->begin_date),
           'end_date'               => DateTool::getDateWithTime($this->end_date),
           'attendance_status_name' => $this->getAttStatusName()
       ];
   }
}
