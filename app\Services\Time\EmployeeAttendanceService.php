<?php

namespace App\Services\Time;

use App\Models\Class\BusAttendance;
use App\Models\Customer\Employee;
use App\Models\Customer\Time\Attendance\EmployeeAttendance;
use App\Models\Customer\Time\SuperSalaryPeriod\SuperSalaryPeriodDtl;
use App\Models\Customer\Time\SuperSalaryPeriod\SuperSalaryPeriodSubTwoDtl;

use App\Services\AdminService;
use App\Services\EmployeeService;
use App\Services\Time\SuperSalaryPeriodService;
use App\Services\Time\RawAttendanceService;
use App\Services\Time\TimeRequestService;
use App\Services\Time\HolidayService;
use App\Services\Time\OtherTimeScheduleService;
use App\Jobs\ProcessEmployeeAttendance;
use App\Exports\TimeExport;
use App\Exports\TimeNoSPExport;
use App\Http\Tools\DateTool;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class EmployeeAttendanceService
{
    public function prepareEmployeeAttendancesForAllAdminUsers() {
        $service = resolve(AdminService::class);
        $users   = $service->getAdminUsers();
        foreach ($users as $key => $user) {
            $nowDateTime = DateTool::nowDateTime();
            ProcessEmployeeAttendance::dispatch($user, DateTool::getYear($nowDateTime), DateTool::getMonth($nowDateTime));
        }
    }

    public function prepareAllEmployeeAttendances($cn, $user, $year, $month) {
        $employeeService     = resolve(EmployeeService::class);
        $salaryPeriodService = resolve(SuperSalaryPeriodService::class);
        $superSalaryPeriod   = $salaryPeriodService->getSuperSalaryPeriodByYearAndMonth($cn, $year, $month);
        $employees           = $employeeService->getAllEmployees($cn);
        foreach ($superSalaryPeriod->super_other_salary_period_dtls as $key => $superSalaryPeriodDtl) {
            $departments = $superSalaryPeriodDtl->super_salary_period_sub_one_dtls;
            $filteredEmployees = $employees->filter(function ($employee) use ($departments) {
                return $departments->contains('id', $employee->department_id);
            });
            $employees = $employees->diff($filteredEmployees);
            $this->prepareAllEmployeeAttendances2($cn, $user, $filteredEmployees, $superSalaryPeriodDtl);
        }
        $superMainSalaryPeriodDtl = $superSalaryPeriod->super_main_salary_period_dtl;
        if (!isset($superMainSalaryPeriodDtl))
            return;
        $this->prepareAllEmployeeAttendances2($cn, $user, $employees, $superMainSalaryPeriodDtl);
    }

    public function prepareAllEmployeeAttendances2($connectionName, $user, $employees, $superSalaryPeriodDtl) 
    {
        $rawAttendanceService  = resolve(RawAttendanceService::class);
        $timeRequestService    = resolve(TimeRequestService::class);
        $holidayService        = resolve(HolidayService::class);
        $otherTSService        = resolve(OtherTimeScheduleService::class);

        $beginDate             = $superSalaryPeriodDtl->getBeginDate();
        $endDate               = $superSalaryPeriodDtl->getEndDate();
        $holidates             = $holidayService->getHolidatesByBetweenDates($connectionName, $beginDate, $endDate);
        $rawAttendances        = $rawAttendanceService->getRawAttendancesByBetweenDates($connectionName, $beginDate, $endDate);
        $confirmedTimeRequests = $timeRequestService->getConfirmedTimeRequestByBetweenDates($connectionName, $beginDate, $endDate, []);
        $employeeAttendances   = $this->getEmployeeAttendancesByBetweenDates($connectionName, $beginDate, $endDate);
        $otherTSEmployee       = $otherTSService->getOtherTimeScheduleEmployeesByBetweenDates($connectionName, $beginDate, $endDate);
        $nowDateTime           = DateTool::nowDateTime();
        $prepareEmployeeAttendancesService = new PrepareEmployeeAttendancesService(new BusAttendance([
            BusAttendance::USER                          => $user,
            BusAttendance::CONNECTION_NAME               => $connectionName,
            BusAttendance::EMPLOYEES                     => $employees,
            BusAttendance::SUPER_SALARY_PERIOD_DTL       => $superSalaryPeriodDtl,
            BusAttendance::HOLIDATES                     => $holidates,
            BusAttendance::OTHER_TIME_SCHEDULE_EMPLOYEES => $otherTSEmployee,
            BusAttendance::RAW_ATTENDANCES               => $rawAttendances,
            BusAttendance::CONFIRMED_TIME_REQUESTS       => $confirmedTimeRequests,
            BusAttendance::EMPLOYEE_ATTENDANCES          => $employeeAttendances,
            BusAttendance::NOW_DATE_TIME                 => $nowDateTime,
        ]));
        $prepareEmployeeAttendancesService->prepareAllEmployeeAttendances();
    }

    public function getEmployeeAttendancesByBetweenDates($connectionName, $beginDate, $endDate, $departmentIds = []) {
        $employeeAttendaces = EmployeeAttendance::on($connectionName)->with(EmployeeAttendance::RELATION_EMPLOYEE)->whereBetween(EmployeeAttendance::ATT_DATE, [$beginDate, $endDate]);
        if (count($departmentIds) > 0) {
            $employeeAttendaces = $employeeAttendaces->whereHas(EmployeeAttendance::RELATION_EMPLOYEE, function (Builder $query) use ($departmentIds) {
                $query->whereIn(Employee::DEPARTMENT_ID, $departmentIds);
            });
        }
        $employeeAttendaces = $employeeAttendaces->get();
        return $employeeAttendaces;
    }

    public function deleteEmployeeAttendance($connectionName, $employeeId, $attDate) {
        $employeeAttendace = new EmployeeAttendance();
        $employeeAttendace->setConnection($connectionName);
        $employeeAttendace = $employeeAttendace
                                ->where(EmployeeAttendance::EMPLOYEE_ID, $employeeId)
                                ->whereDate(EmployeeAttendance::ATT_DATE, $attDate)
                                ->first();
        if (isset($employeeAttendace)) {
            $employeeAttendace->employee_attendance_dtls()->delete();
            $employeeAttendace->delete();
        }
    }

    public function deleteEmployeeAttendanceByBetweenDates($connectionName, $employeeId, $beginDate, $endDate = null) {
        $employeeAttendaces = new EmployeeAttendance();
        $employeeAttendaces->setConnection($connectionName);

        if ($endDate) {
            $employeeAttendaces = $employeeAttendaces
                                ->where(EmployeeAttendance::EMPLOYEE_ID, $employeeId)
                                ->whereBetween(EmployeeAttendance::ATT_DATE, [$beginDate, $endDate])
                                ->get();
        } else {
            $employeeAttendaces = $employeeAttendaces
                                ->where(EmployeeAttendance::EMPLOYEE_ID, $employeeId)
                                ->whereDate(EmployeeAttendance::ATT_DATE, '>=', $beginDate)
                                ->get();
        }

        if ($employeeAttendaces) {
            foreach ($employeeAttendaces as $key => $employeeAttendace) {
                $employeeAttendace->employee_attendance_dtls()->delete();
                $employeeAttendace->delete();
            }
        }
    }

    public function exportMainReport($cn, $superSalaryPeriodDtlId, $superSalaryPeriodSubTwoDtlId, $departmentIds, $hasDetail) {
        $superSalaryPeriodDtl = SuperSalaryPeriodDtl::findOrFail($superSalaryPeriodDtlId);
        $year      = $superSalaryPeriodDtl->getYear();
        $month     = $superSalaryPeriodDtl->getMonth();

        $beginDate = $superSalaryPeriodDtl->getBeginDate();
        $endDate   = $superSalaryPeriodDtl->getEndDate();

        if (isset($superSalaryPeriodSubTwoDtlId)) {
            $superSalaryPeriodSubTwoDtl = SuperSalaryPeriodSubTwoDtl::find($superSalaryPeriodSubTwoDtlId);
            $beginDate = $superSalaryPeriodSubTwoDtl->begin_date;
            $endDate   = $superSalaryPeriodSubTwoDtl->end_date;
        }

        // ТУХАЙН ХУГАЦААНЫ НИЙТ ӨДРҮҮДИЙН НИЙЛБЭР ЦАГ
        $employeeAttendances = EmployeeAttendance::on($cn)->with('employee')
            ->select(
                 DB::raw(' SUM(' . EmployeeAttendance::WORK_TIME . '+' . EmployeeAttendance::OVER_TIME . '+' . EmployeeAttendance::NIGHT_TIME . '+' . EmployeeAttendance::REQUEST_WORK_TIME . ') as total_work_time'),
                 DB::raw(' SUM(CASE WHEN (' . EmployeeAttendance::WORK_TIME . '+' . EmployeeAttendance::OVER_TIME . '+' . EmployeeAttendance::NIGHT_TIME . '+' . EmployeeAttendance::REQUEST_WORK_TIME . ') > 0 THEN 1 ELSE 0 END) as total_work_count'),
                 DB::raw(' SUM(' . EmployeeAttendance::WORK_TIME . '+' . EmployeeAttendance::REQUEST_WORK_TIME . ') as work_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::NIGHT_TIME . ') as night_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::OVER_TIME . ') as over_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::ABSENT_TIME . '+' . EmployeeAttendance::LATE_TIME  . '+' . EmployeeAttendance::FURLOUGH_TIME  . '+' . EmployeeAttendance::SICK_TIME . '+' . EmployeeAttendance::COMPENSATORY_REST_TIME . ') as total_not_work_time'),
                 DB::raw(' SUM(CASE WHEN (' . EmployeeAttendance::VACATION_TIME . ') > 0 THEN 1 ELSE 0 END ' . ') as total_vacation_count'),
                 DB::raw(' SUM(' . EmployeeAttendance::LATE_TIME . ') as late_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::ABSENT_TIME . ') as absent_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::SALARY_FURLOUGH_TIME . ') as salary_furlough_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::FURLOUGH_TIME . ') as furlough_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::SICK_TIME . ') as sick_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::COMPENSATORY_REST_TIME . ') as compensatory_rest_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::VACATION_TIME . ') as vacation_time'), EmployeeAttendance::EMPLOYEE_ID)
        ->whereBetween(EmployeeAttendance::ATT_DATE, [$beginDate, $endDate])
        ->whereHas(EmployeeAttendance::RELATION_EMPLOYEE, function (Builder $query) use ($departmentIds) {
            if (!empty($departmentIds))
                $query->whereIn(Employee::DEPARTMENT_ID, $departmentIds);
        })
        ->groupBy(EmployeeAttendance::EMPLOYEE_ID);
        $employeeAttendances = $employeeAttendances->get();

        $loopBeginDate = $beginDate;
        $loopEndDate   = $endDate;
        while ($loopBeginDate <= $loopEndDate) {
            $salaryPeriodSubDtls[] = (object) ['salary_date' => $loopBeginDate];
            $loopBeginDate         = DateTool::getTomorrowFromDate($loopBeginDate)->format('Y-m-d');
        }

        if ($hasDetail) {
            // ӨДӨР БҮРИЙН ЦАГ
            $employeeAttendanceByDays = new EmployeeAttendance();
            $employeeAttendanceByDays->setConnection($cn);
            $employeeAttendanceByDays = $employeeAttendanceByDays
                        ->select(EmployeeAttendance::ATT_DATE, EmployeeAttendance::EMPLOYEE_ID, EmployeeAttendance::WORK_TIME, EmployeeAttendance::OVER_TIME, EmployeeAttendance::NIGHT_TIME, EmployeeAttendance::COMPENSATORY_REST_TIME, EmployeeAttendance::REQUEST_WORK_TIME, EmployeeAttendance::SALARY_FURLOUGH_TIME, EmployeeAttendance::FURLOUGH_TIME, EmployeeAttendance::SICK_TIME, EmployeeAttendance::LATE_TIME, EmployeeAttendance::ABSENT_TIME, EmployeeAttendance::VACATION_TIME)
                        ->whereBetween(EmployeeAttendance::ATT_DATE, [$beginDate, $endDate]);
            $employeeAttendanceByDays = $employeeAttendanceByDays->orderBy(EmployeeAttendance::EMPLOYEE_ID)->get();
        }

        $totalEmployeeAttendance = new EmployeeAttendance();
        $totalEmployeeAttendance->total_fund_time          = 0;
        $totalEmployeeAttendance->front_fund_time          = 0;
        $totalEmployeeAttendance->total_work_time_percent  = 0;
        $totalEmployeeAttendance->total_work_time          = 0;
        $totalEmployeeAttendance->total_work_count         = 0;
        $totalEmployeeAttendance->work_time                = 0;
        $totalEmployeeAttendance->night_time               = 0;
        $totalEmployeeAttendance->over_time                = 0;
        $totalEmployeeAttendance->total_not_work_time      = 0;
        $totalEmployeeAttendance->late_time                = 0;
        $totalEmployeeAttendance->absent_time              = 0;
        $totalEmployeeAttendance->salary_furlough_time     = 0;
        $totalEmployeeAttendance->furlough_time            = 0;
        $totalEmployeeAttendance->sick_time                = 0;
        $totalEmployeeAttendance->compensatory_rest_time   = 0;
        $totalEmployeeAttendance->vacation_time            = 0;
        $totalEmployeeAttendance->total_vacation_count     = 0;

        $wdCount      = $superSalaryPeriodDtl->wd_count;
        $wdTotalTime  = $superSalaryPeriodDtl->getWdTotalTimeAttribute();

        foreach ($employeeAttendances as $key => $employeeAttendance) {
            $totalWorkTimePercent = round((int) ($employeeAttendance->total_work_time)/60, 2);

            $totalEmployeeAttendance->total_work_time_percent      += $totalWorkTimePercent;
            $totalEmployeeAttendance->total_work_time              += $employeeAttendance->total_work_time;
            $totalEmployeeAttendance->total_work_count             += $employeeAttendance->total_work_count;
            $totalEmployeeAttendance->work_time                    += $employeeAttendance->work_time;
            $totalEmployeeAttendance->night_time                   += $employeeAttendance->night_time;
            $totalEmployeeAttendance->over_time                    += $employeeAttendance->over_time;
            $totalEmployeeAttendance->total_not_work_time          += $employeeAttendance->total_not_work_time;
            $totalEmployeeAttendance->late_time                    += $employeeAttendance->late_time;
            $totalEmployeeAttendance->absent_time                  += $employeeAttendance->absent_time;
            $totalEmployeeAttendance->salary_furlough_time         += $employeeAttendance->salary_furlough_time;
            $totalEmployeeAttendance->furlough_time                += $employeeAttendance->furlough_time;
            $totalEmployeeAttendance->sick_time                    += $employeeAttendance->sick_time;
            $totalEmployeeAttendance->compensatory_rest_time       += $employeeAttendance->compensatory_rest_time;
            $totalEmployeeAttendance->vacation_time                += $employeeAttendance->vacation_time;
            $totalEmployeeAttendance->total_vacation_count         += $employeeAttendance->total_vacation_count;

            $employeeAttendance->wd_count                   = $wdCount;
            $employeeAttendance->wd_total_time              = $wdTotalTime;
            $employeeAttendance->total_work_time_percent    = $totalWorkTimePercent;
            $employeeAttendance->total_work_time            = DateTool::showHourAndMinuteByMinute($employeeAttendance->total_work_time);
            $employeeAttendance->work_time                  = DateTool::showHourAndMinuteByMinute($employeeAttendance->work_time);
            $employeeAttendance->night_time                 = DateTool::showHourAndMinuteByMinute($employeeAttendance->night_time);
            $employeeAttendance->over_time                  = DateTool::showHourAndMinuteByMinute($employeeAttendance->over_time);
            $employeeAttendance->total_not_work_time        = DateTool::showHourAndMinuteByMinute($employeeAttendance->total_not_work_time);
            $employeeAttendance->late_time                  = DateTool::showHourAndMinuteByMinute($employeeAttendance->late_time);
            $employeeAttendance->absent_time                = DateTool::showHourAndMinuteByMinute($employeeAttendance->absent_time);
            $employeeAttendance->salary_furlough_time       = DateTool::showHourAndMinuteByMinute($employeeAttendance->salary_furlough_time);
            $employeeAttendance->furlough_time              = DateTool::showHourAndMinuteByMinute($employeeAttendance->furlough_time);
            $employeeAttendance->sick_time                  = DateTool::showHourAndMinuteByMinute($employeeAttendance->sick_time);
            $employeeAttendance->compensatory_rest_time     = DateTool::showHourAndMinuteByMinute($employeeAttendance->compensatory_rest_time);
            $employeeAttendance->vacation_time              = DateTool::showHourAndMinuteByMinute($employeeAttendance->vacation_time);

            if ($hasDetail) {
                $filteredEmployeeAttendanceByDays = [];
                foreach ($employeeAttendanceByDays as $key => $employeeAttendanceByDay) {
                    if ($employeeAttendance->employee_id !== $employeeAttendanceByDay->employee_id)
                        continue;
                    $filteredEmployeeAttendanceByDays[] = $employeeAttendanceByDay;
                }

                $dayData  = [];
                $lineTime = '--:--';
                $zeroTime = '00:00';
                foreach ($salaryPeriodSubDtls as $key => $salaryPeriodSubDtl) {
                    $hasFound = false;
                    $employeeAttendanceByDay = new EmployeeAttendance();
                    foreach ($filteredEmployeeAttendanceByDays as $key => $value) {
                        if ($value->att_date !== $salaryPeriodSubDtl->salary_date)
                            continue;
                        $hasFound = true;
                        $value->work_time                  = DateTool::showHourAndMinuteByMinute($value->work_time + $value->request_work_time);
                        $value->night_time                 = DateTool::showHourAndMinuteByMinute($value->night_time);
                        $value->over_time                  = DateTool::showHourAndMinuteByMinute($value->over_time);
                        $value->salary_furlough_time       = DateTool::showHourAndMinuteByMinute($value->salary_furlough_time);
                        $value->furlough_time              = DateTool::showHourAndMinuteByMinute($value->furlough_time);
                        $value->sick_time                  = DateTool::showHourAndMinuteByMinute($value->sick_time);
                        $value->late_time                  = DateTool::showHourAndMinuteByMinute($value->late_time);
                        $value->absent_time                = DateTool::showHourAndMinuteByMinute($value->absent_time);
                        $value->vacation_time              = DateTool::showHourAndMinuteByMinute($value->vacation_time);
                        $dayData[] = $value;

                        if ($value->work_time === $zeroTime) {
                            $value->work_time = $lineTime;
                        }
                        if ($value->night_time === $zeroTime) {
                            $value->night_time = $lineTime;
                        }
                        if ($value->over_time === $zeroTime) {
                            $value->over_time = $lineTime;
                        }
                        if ($value->salary_furlough_time === $zeroTime) {
                            $value->salary_furlough_time = $lineTime;
                        }
                        if ($value->furlough_time === $zeroTime) {
                            $value->furlough_time = $lineTime;
                        }
                        if ($value->sick_time === $zeroTime) {
                            $value->sick_time = $lineTime;
                        }
                        if ($value->late_time === $zeroTime) {
                            $value->late_time = $lineTime;
                        }
                        if ($value->absent_time === $zeroTime) {
                            $value->absent_time = $lineTime;
                        }
                        if ($value->vacation_time === $zeroTime) {
                            $value->vacation_time = $lineTime;
                        }
                        $employeeAttendanceByDay = $value;
                        break;
                    }

                    if (!$hasFound) {
                        $dayData[] = $employeeAttendanceByDay;
                    }
                }
                $employeeAttendance['day_data'] = $dayData;
            }
        }
        $totalEmployeeAttendance->total_work_time            = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->total_work_time);
        $totalEmployeeAttendance->total_work_time_percent    = $totalEmployeeAttendance->total_work_time_percent;
        $totalEmployeeAttendance->work_time                  = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->work_time);
        $totalEmployeeAttendance->night_time                 = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->night_time);
        $totalEmployeeAttendance->over_time                  = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->over_time);
        $totalEmployeeAttendance->total_not_work_time        = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->total_not_work_time);
        $totalEmployeeAttendance->late_time                  = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->late_time);
        $totalEmployeeAttendance->absent_time                = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->absent_time);
        $totalEmployeeAttendance->salary_furlough_time       = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->salary_furlough_time);
        $totalEmployeeAttendance->furlough_time              = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->furlough_time);
        $totalEmployeeAttendance->sick_time                  = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->sick_time);
        $totalEmployeeAttendance->compensatory_rest_time     = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->compensatory_rest_time);
        $totalEmployeeAttendance->vacation_time              = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->vacation_time);
        $fileName   = $year.'-'. $month . (!isset($salaryPeriodDtlId) ? 'buten-uechlel' : 'tuhain-uechlel') . '.xlsx';
        $timeExport = new TimeExport($salaryPeriodSubDtls, $employeeAttendances, $totalEmployeeAttendance, $hasDetail);
        Excel::store($timeExport, 'public/' . $fileName);
        return $fileName;
    }

    public function getEmployeeAttendanceGroupByDateAndEmployee($cn, $employeeId, $beginDate, $endDate) {
        $employeeAttendance = EmployeeAttendance::on($cn)
        ->select(
             DB::raw(' SUM(' . EmployeeAttendance::WORK_TIME . '+' . EmployeeAttendance::OVER_TIME . '+' . EmployeeAttendance::NIGHT_TIME . '+' . EmployeeAttendance::REQUEST_WORK_TIME . ') as total_work_time'),
             DB::raw(' SUM(CASE WHEN (' . EmployeeAttendance::WORK_TIME . '+' . EmployeeAttendance::OVER_TIME . '+' . EmployeeAttendance::NIGHT_TIME . '+' . EmployeeAttendance::REQUEST_WORK_TIME . ') > 0 THEN 1 ELSE 0 END) as total_work_count'),
             DB::raw(' SUM(' . EmployeeAttendance::WORK_TIME . '+' . EmployeeAttendance::REQUEST_WORK_TIME . ') as work_time'),
             DB::raw(' SUM(' . EmployeeAttendance::NIGHT_TIME . ') as night_time'),
             DB::raw(' SUM(' . EmployeeAttendance::OVER_TIME . ') as over_time'),
             DB::raw(' SUM(' . EmployeeAttendance::ABSENT_TIME . '+' . EmployeeAttendance::LATE_TIME  . '+' . EmployeeAttendance::FURLOUGH_TIME  . '+' . EmployeeAttendance::SICK_TIME . '+' . EmployeeAttendance::COMPENSATORY_REST_TIME . ') as total_not_work_time'),
             DB::raw(' SUM(CASE WHEN (' . EmployeeAttendance::VACATION_TIME . ') > 0 THEN 1 ELSE 0 END ' . ') as total_vacation_count'),
             DB::raw(' SUM(' . EmployeeAttendance::LATE_TIME . ') as late_time'),
             DB::raw(' SUM(' . EmployeeAttendance::ABSENT_TIME . ') as absent_time'),
             DB::raw(' SUM(' . EmployeeAttendance::SALARY_FURLOUGH_TIME . ') as salary_furlough_time'),
             DB::raw(' SUM(' . EmployeeAttendance::FURLOUGH_TIME . ') as furlough_time'),
             DB::raw(' SUM(' . EmployeeAttendance::SICK_TIME . ') as sick_time'),
             DB::raw(' SUM(' . EmployeeAttendance::COMPENSATORY_REST_TIME . ') as compensatory_rest_time'),
             DB::raw(' SUM(' . EmployeeAttendance::VACATION_TIME . ') as vacation_time'), EmployeeAttendance::EMPLOYEE_ID)
        ->whereBetween(EmployeeAttendance::ATT_DATE, [$beginDate, $endDate])
        ->where(EmployeeAttendance::EMPLOYEE_ID, $employeeId)
        ->groupBy(EmployeeAttendance::EMPLOYEE_ID);
        $employeeAttendance = $employeeAttendance->first();
        return $employeeAttendance;
    }

    public function getTimes($cn, $employeeId, $beginDate, $endDate) {
        $employeeAttendance = EmployeeAttendance::on($cn)
        ->select(
            EmployeeAttendance::ID,
             DB::raw(EmployeeAttendance::ABSENT_TIME . '+' . EmployeeAttendance::LATE_TIME  . '+' . EmployeeAttendance::FURLOUGH_TIME  . '+' . EmployeeAttendance::SICK_TIME . '+' . EmployeeAttendance::COMPENSATORY_REST_TIME . ' as total_not_work_time'),
                EmployeeAttendance::ATT_DATE
             )
        ->whereBetween(EmployeeAttendance::ATT_DATE, [$beginDate, $endDate])
        ->where(EmployeeAttendance::EMPLOYEE_ID, $employeeId);
        $employeeAttendance = $employeeAttendance->orderBy(EmployeeAttendance::ATT_DATE)->get();
        return $employeeAttendance;
    }

    public function exportAttendance($cn, $departmentIds, $beginDate, $endDate) {
        $endDate = $endDate ? $endDate : DateTool::nowDate();
        // ТУХАЙН ХУГАЦААНЫ НИЙТ ӨДРҮҮДИЙН НИЙЛБЭР ЦАГ
        $employeeAttendances = EmployeeAttendance::on($cn)->with('employee')
            ->select(
                 DB::raw(' SUM(' . EmployeeAttendance::WORK_TIME . '+' . EmployeeAttendance::OVER_TIME . '+' . EmployeeAttendance::NIGHT_TIME . '+' . EmployeeAttendance::REQUEST_WORK_TIME . ') as total_work_time'),
                 DB::raw(' SUM(CASE WHEN (' . EmployeeAttendance::WORK_TIME . '+' . EmployeeAttendance::OVER_TIME . '+' . EmployeeAttendance::NIGHT_TIME . '+' . EmployeeAttendance::REQUEST_WORK_TIME . ') > 0 THEN 1 ELSE 0 END) as total_work_count'),
                 DB::raw(' SUM(' . EmployeeAttendance::WORK_TIME . '+' . EmployeeAttendance::REQUEST_WORK_TIME . ') as work_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::NIGHT_TIME . ') as night_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::OVER_TIME . ') as over_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::ABSENT_TIME . '+' . EmployeeAttendance::LATE_TIME  . '+' . EmployeeAttendance::FURLOUGH_TIME  . '+' . EmployeeAttendance::SICK_TIME . '+' . EmployeeAttendance::COMPENSATORY_REST_TIME . ') as total_not_work_time'),
                 DB::raw(' SUM(CASE WHEN (' . EmployeeAttendance::VACATION_TIME . ') > 0 THEN 1 ELSE 0 END ' . ') as total_vacation_count'),
                 DB::raw(' SUM(' . EmployeeAttendance::LATE_TIME . ') as late_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::ABSENT_TIME . ') as absent_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::SALARY_FURLOUGH_TIME . ') as salary_furlough_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::FURLOUGH_TIME . ') as furlough_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::SICK_TIME . ') as sick_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::COMPENSATORY_REST_TIME . ') as compensatory_rest_time'),
                 DB::raw(' SUM(' . EmployeeAttendance::VACATION_TIME . ') as vacation_time'), EmployeeAttendance::EMPLOYEE_ID)
        ->whereBetween(EmployeeAttendance::ATT_DATE, [$beginDate, $endDate])
        ->whereHas(EmployeeAttendance::RELATION_EMPLOYEE, function (Builder $query) use ($departmentIds) {
            if (!empty($departmentIds))
                $query->whereIn(Employee::DEPARTMENT_ID, $departmentIds);
        })
        ->groupBy(EmployeeAttendance::EMPLOYEE_ID);
        $employeeAttendances = $employeeAttendances->get();

        $totalEmployeeAttendance = new EmployeeAttendance();
        $totalEmployeeAttendance->total_fund_time          = 0;
        $totalEmployeeAttendance->front_fund_time          = 0;
        $totalEmployeeAttendance->total_work_time_percent  = 0;
        $totalEmployeeAttendance->total_work_time          = 0;
        $totalEmployeeAttendance->total_work_count         = 0;
        $totalEmployeeAttendance->work_time                = 0;
        $totalEmployeeAttendance->night_time               = 0;
        $totalEmployeeAttendance->over_time                = 0;
        $totalEmployeeAttendance->total_not_work_time      = 0;
        $totalEmployeeAttendance->late_time                = 0;
        $totalEmployeeAttendance->absent_time              = 0;
        $totalEmployeeAttendance->salary_furlough_time     = 0;
        $totalEmployeeAttendance->furlough_time            = 0;
        $totalEmployeeAttendance->sick_time                = 0;
        $totalEmployeeAttendance->compensatory_rest_time   = 0;
        $totalEmployeeAttendance->vacation_time            = 0;
        $totalEmployeeAttendance->total_vacation_count     = 0;

        foreach ($employeeAttendances as $key => $employeeAttendance) {
            $totalWorkTimePercent = round((int) ($employeeAttendance->total_work_time)/60, 2);

            $totalEmployeeAttendance->total_work_time_percent      += $totalWorkTimePercent;
            $totalEmployeeAttendance->total_work_time              += $employeeAttendance->total_work_time;
            $totalEmployeeAttendance->total_work_count             += $employeeAttendance->total_work_count;
            $totalEmployeeAttendance->work_time                    += $employeeAttendance->work_time;
            $totalEmployeeAttendance->night_time                   += $employeeAttendance->night_time;
            $totalEmployeeAttendance->over_time                    += $employeeAttendance->over_time;
            $totalEmployeeAttendance->total_not_work_time          += $employeeAttendance->total_not_work_time;
            $totalEmployeeAttendance->late_time                    += $employeeAttendance->late_time;
            $totalEmployeeAttendance->absent_time                  += $employeeAttendance->absent_time;
            $totalEmployeeAttendance->salary_furlough_time         += $employeeAttendance->salary_furlough_time;
            $totalEmployeeAttendance->furlough_time                += $employeeAttendance->furlough_time;
            $totalEmployeeAttendance->sick_time                    += $employeeAttendance->sick_time;
            $totalEmployeeAttendance->compensatory_rest_time       += $employeeAttendance->compensatory_rest_time;
            $totalEmployeeAttendance->vacation_time                += $employeeAttendance->vacation_time;
            $totalEmployeeAttendance->total_vacation_count         += $employeeAttendance->total_vacation_count;

            $employeeAttendance->total_work_time_percent    = $totalWorkTimePercent;
            $employeeAttendance->total_work_time            = DateTool::showHourAndMinuteByMinute($employeeAttendance->total_work_time);
            $employeeAttendance->work_time                  = DateTool::showHourAndMinuteByMinute($employeeAttendance->work_time);
            $employeeAttendance->night_time                 = DateTool::showHourAndMinuteByMinute($employeeAttendance->night_time);
            $employeeAttendance->over_time                  = DateTool::showHourAndMinuteByMinute($employeeAttendance->over_time);
            $employeeAttendance->total_not_work_time        = DateTool::showHourAndMinuteByMinute($employeeAttendance->total_not_work_time);
            $employeeAttendance->late_time                  = DateTool::showHourAndMinuteByMinute($employeeAttendance->late_time);
            $employeeAttendance->absent_time                = DateTool::showHourAndMinuteByMinute($employeeAttendance->absent_time);
            $employeeAttendance->salary_furlough_time       = DateTool::showHourAndMinuteByMinute($employeeAttendance->salary_furlough_time);
            $employeeAttendance->furlough_time              = DateTool::showHourAndMinuteByMinute($employeeAttendance->furlough_time);
            $employeeAttendance->sick_time                  = DateTool::showHourAndMinuteByMinute($employeeAttendance->sick_time);
            $employeeAttendance->compensatory_rest_time     = DateTool::showHourAndMinuteByMinute($employeeAttendance->compensatory_rest_time);
            $employeeAttendance->vacation_time              = DateTool::showHourAndMinuteByMinute($employeeAttendance->vacation_time);
        }
        $totalEmployeeAttendance->total_work_time            = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->total_work_time);
        $totalEmployeeAttendance->total_work_time_percent    = $totalEmployeeAttendance->total_work_time_percent;
        $totalEmployeeAttendance->work_time                  = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->work_time);
        $totalEmployeeAttendance->night_time                 = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->night_time);
        $totalEmployeeAttendance->over_time                  = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->over_time);
        $totalEmployeeAttendance->total_not_work_time        = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->total_not_work_time);
        $totalEmployeeAttendance->late_time                  = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->late_time);
        $totalEmployeeAttendance->absent_time                = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->absent_time);
        $totalEmployeeAttendance->salary_furlough_time       = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->salary_furlough_time);
        $totalEmployeeAttendance->furlough_time              = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->furlough_time);
        $totalEmployeeAttendance->sick_time                  = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->sick_time);
        $totalEmployeeAttendance->compensatory_rest_time     = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->compensatory_rest_time);
        $totalEmployeeAttendance->vacation_time              = DateTool::showHourAndMinuteByMinute($totalEmployeeAttendance->vacation_time);
        $fileName   ='attendance_with_no_sp.xlsx';
        $timeExport = new TimeNoSPExport($employeeAttendances, $totalEmployeeAttendance);
        Excel::store($timeExport, 'public/' . $fileName);
        return $fileName;
    }
}
