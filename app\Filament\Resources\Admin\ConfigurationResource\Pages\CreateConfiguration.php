<?php

namespace App\Filament\Resources\Admin\ConfigurationResource\Pages;

use App\Filament\Resources\Admin\ConfigurationResource;
use App\Models\Customer\Configuration;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use Illuminate\Database\UniqueConstraintViolationException;

class CreateConfiguration extends CreateRecord
{
    protected static string $resource = ConfigurationResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data[Configuration::CREATED_BY] = auth()->user()->id;
        return $data;
    }

    protected function handleRecordCreation(array $data): \Illuminate\Database\Eloquent\Model
    {
        try {
            return parent::handleRecordCreation($data);
        } catch (UniqueConstraintViolationException $e) {
            Notification::make()
                ->title('Алдаа')
                ->body('Тохиргооны ID давхардаж байна. Өөр утга оруулна уу.')
                ->danger()
                ->send();
            
            $this->halt();
        }
    }
}