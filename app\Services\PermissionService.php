<?php

namespace App\Service;

use Spatie\Permission\Models\Permission;

class PermissionService
{
    /**
     * @param permissions    - Тухайн хэрэглэгчийн бүх permissions
     */
    public function getPermissionsByUser($permissions, $allPermissions = null) {
        if ($allPermissions == null) {
            $allPermissions = Permission::getPermissions()->pluck('name');
        }
        $userPermissions = collect([]);
        foreach ($allPermissions as $permission) {
            $permissionArr = explode(".", $permission);
            $menu = $permissionArr[0];

            if ($userPermissions->contains('menu', $menu))
                continue;

            $filteredPermissions = $allPermissions->filter(function ($value, $key) use($permission, $menu) {
                $menyArr = explode(".", $value);
                return $menyArr[0] === $menu;
            });

            $items = collect([]);

            foreach ($filteredPermissions as $filteredPermission) {
                $itemArr = explode(".", $filteredPermission);
                $window = $itemArr[1];
                if ($items->contains('name', $window))
                    continue;

                $filteredActions = $filteredPermissions->filter(function ($value, $key) use($window) {
                    $menyArr = explode(".", $value);
                    return $menyArr[1] === $window;
                });

                $actions = [];
                foreach ($filteredActions as $filteredAction) {
                    $actionArr = explode(".", $filteredAction);

                    $filteredUserPermissions = $permissions->filter(function ($value, $key) use($filteredAction) {
                        return $value === $filteredAction;
                    });
                    $actions[$actionArr[2]] = !$filteredUserPermissions->isEmpty();
                }

                $items->push(["name" => $window, "permission" => $actions ]);
            }

            $obj = [ "menu" => $menu, "item" => $items ];

            $userPermissions->push($obj);
        }

        return $userPermissions;
    }
}
