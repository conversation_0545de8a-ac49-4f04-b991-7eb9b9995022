<?php

namespace App\Models\Customer\CashRequest;

use App\Services\ConnectionService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Model;

class CashRequestAttachment extends Model
{
    const CASH_REQUEST_ID = 'cash_request_id';
    const FILE_PATH       = 'file_path';
    const ORIGINAL_NAME   = 'original_name';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::CASH_REQUEST_ID,
        self::FILE_PATH,
        self::ORIGINAL_NAME
    ];

    public function cash_request()
    {
        return $this->belongsTo(CashRequest::class);
    }

    public function getUrlAttribute(): string
    {
        return Storage::disk('minio')->url($this->file_path);
    }
}
