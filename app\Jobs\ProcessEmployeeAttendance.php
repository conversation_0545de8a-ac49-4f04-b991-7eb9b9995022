<?php

namespace App\Jobs;

use App\Models\Constant\ConstData;
use App\Services\ConnectionService;
use App\Services\Time\EmployeeAttendanceService;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Throwable;

class ProcessEmployeeAttendance implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout   = 1200;
    public $tries     = 1;

    protected $connectionName;
    protected $user;
    protected $year;
    protected $month;
    protected $queueUniqueId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user, $year, $month)
    {
        $this->connectionName = ConnectionService::connectionNameByUser($user);
        $this->user           = $user;
        $this->year           = $year;
        $this->month          = $month;
        $this->queueUniqueId  = 'employee-attendance-' . $user->id;
        $this->onConnection(ConstData::QC_DATABASE);
    }

    public function uniqueId()
    {
        return $this->queueUniqueId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(EmployeeAttendanceService $employeeAttendanceService)
    {
        try {
            $connectionName = $this->connectionName;
            $user           = $this->user;
            $year           = $this->year;
            $month          = $this->month;

            $cn = DB::connection($connectionName);
            $cn->beginTransaction();
            $employeeAttendanceService->prepareAllEmployeeAttendances($connectionName, $user, $year, $month);
            $cn->commit();
        } catch (\Throwable $th) {
            throw $th;
        } finally {
            DB::disconnect($connectionName);
        }
    }

     /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(Throwable $exception)
    {
        echo $exception;
    }
}
