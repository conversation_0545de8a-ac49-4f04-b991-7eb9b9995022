<?php

namespace App\Filament\Resources\Admin\ApprovalConfigHdrResource\Pages;

use App\Filament\Resources\Admin\ApprovalConfigHdrResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListApprovalConfigHdrs extends ListRecords
{
    protected static string $resource = ApprovalConfigHdrResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-o-plus'),
        ];
    }
}
