<?php

namespace App\Exceptions;

use App\Models\Constant\Message;
use App\Models\Constant\ConstData;
use Exception;

class SystemException extends Exception
{
    private $exceptionType;
    private $errorCode;
    private $errors;
    private $additionalMsg;

    public function __construct($exceptionType, $errorCode, $errors = [], $additionalMsg = null) {
        $this->exceptionType = $exceptionType;
        $this->errorCode     = $errorCode;
        $this->errors        = $errors;
        $this->additionalMsg = $additionalMsg;
    }

    /**
     * Report the exception.
     *
     * @return bool|null
     */
    public function report()
    {
        //
    }

    public function prepareMessage() {
        $errorCode = $this->errorCode;
        switch ($this->exceptionType) {
            case ConstData::AUTH_EXCEPTION:
                $message = Message::getAuthMessage($errorCode);
                break;
            case ConstData::SYSTEM_EXCEPTION:
                $message = Message::getSystemMessage($errorCode);
                break;
            case ConstData::CONFIG_EXCEPTION:
                $message = Message::getConfigMessage($errorCode);
                break;
            case ConstData::TIME_EXCEPTION:
                $message = Message::getTimeMessage($errorCode);
                break;
            case ConstData::EMPLOYEE_EXCEPTION:
                $message = Message::getEmployeeMessage($errorCode);
                break;
            case ConstData::DECISION_EXCEPTION:
                $message = Message::getDecisionMessage($errorCode);
                break;
            default:
                $message   = '';
                break;
        }
        return $message;
    }

    /**
     * Render the exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function render($request) {
        $message        = $this->prepareMessage();
        $additionalMsg  = $this->additionalMsg ? $this->additionalMsg : '';
        return response()->json([
                ConstData::STATUS  => ConstData::STATUS_ERROR,
                ConstData::MESSAGE => ($this->exceptionType . $message . ' ' . $additionalMsg),
                ConstData::ERRORS  => $this->errors
            ], 422);
    }
}
