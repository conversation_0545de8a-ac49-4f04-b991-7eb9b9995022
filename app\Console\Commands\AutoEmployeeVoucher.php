<?php

namespace App\Console\Commands;

use App\Services\EmployeeVoucherService;

use Illuminate\Console\Command;

class AutoEmployeeVoucher extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auto:employee-voucher';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $service = resolve(EmployeeVoucherService::class);
        $service->createEmployeeVouchersByAllOrganizations();
    }
}
