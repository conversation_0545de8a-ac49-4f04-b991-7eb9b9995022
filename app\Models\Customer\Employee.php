<?php

namespace App\Models\Customer;

use App\Models\Constant\ConstData;
use App\Models\Customer\Time\DeviceConfig;
use App\Models\EmployeeUser;
use App\Models\Customer\Time\Schedule\TimeScheduleEmployee;
use App\Models\Customer\Voucher\EmployeeVoucherAmount;
use App\Models\Customer\Voucher\EmployeeVoucherSpent;
use App\Services\ConnectionService;
use App\Services\Time\BioTimeService;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Laravel\Sanctum\HasApiTokens;

class Employee extends Model
{
    use HasFactory, HasApiTokens;

    const TABLE           = 'employees';

    const ID                        = 'id';
    const LAST_NAME                 = 'last_name';
    const FIRST_NAME                = 'first_name';
    const CITIZEN_CODE              = 'citizen_code';
    const DEPARTMENT_ID             = 'department_id';
    const POSITION_ID               = 'position_id';
    const PHONE                     = 'phone';
    const STATUS                    = 'status';
    const CREATED_BY                = 'created_by';
    const UPDATED_BY                = 'updated_by';

    const RELATION_DEPARTMENT                   = 'department';
    const RELATION_POSITION                     = 'position';
    const RELATION_EMPLOYEE_DTL                 = 'employee_dtl';
    const RELATION_USER                         = 'user';
    const RELATION_DEVICE_CONFIGS               = 'device_configs';
    const RELATION_OTHER_TIME_SHEDULE_EMPLOYEES = 'other_time_shedule_employees';

    const FULL_NAME = "full_name";

    const STATUS_ACTIVE      = "A";
    const STATUS_RESOURCE    = "R";
    const STATUS_NOT_ACTIVE  = "N";

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::ID,
        self::LAST_NAME,
        self::FIRST_NAME,
        self::CITIZEN_CODE,
        self::PHONE,
        self::DEPARTMENT_ID,
        self::POSITION_ID,
        self::STATUS,
        self::CREATED_BY,
        self::UPDATED_BY,
    ];

    public function getFullNameAttribute()
    {
        return "$this->last_name $this->first_name";
    }

    public function getFullNameWithDPAttribute()
    {
        return $this->department->short_name . ', '. $this->position->short_name . ", $this->last_name $this->first_name";
    }

    public function isStatusActive() {
        return $this->status === self::STATUS_ACTIVE;
    }

    public function isStatusNotActive() {
        return $this->status === self::STATUS_NOT_ACTIVE;
    }

    public function department() {
        return $this->belongsTo(Department::class);
    }

    public function position() {
        return $this->belongsTo(Position::class);
    }

    public function employee_dtl(): HasOne {
        return $this->hasOne(EmployeeDtl::class);
    }

    public function employee_user() {
        return $this->hasOne(EmployeeUser::class, self::PHONE, EmployeeUser::PHONE);
    }

    public function device_configs(): BelongsToMany
    {
        return $this->belongsToMany(DeviceConfig::class, 'employee_device_config');
    }

    public function createUserInDevice() {
        $biotimeService = resolve(BioTimeService::class);
        foreach ($this->device_configs as $key => $deviceConfig) {
            $response = $biotimeService->getUser($deviceConfig->serial_number, ConstData::DEFAULT_BIOTIME_PORT, $this->employee_dtl->attendance_code);
            if (!isset($response) || $response->status() != 200)
                continue;
            $user = $response->json();                
            if (isset($user))
                continue;
            $biotimeService->createUser(
                $deviceConfig->serial_number,
                ConstData::DEFAULT_BIOTIME_PORT,
                $this->employee_dtl->attendance_code,
                $this->full_name,
                $this->employee_dtl->attendance_code
            );
        }
    }

    public function removeUserFromDevice($newDeviceConfigIds = []) {
        $biotimeService = resolve(BioTimeService::class);
        foreach ($this->device_configs as $key => $deviceConfig) {
            $hasFound = collect($newDeviceConfigIds)->contains(function ($value) use ($deviceConfig) {
                return $value === $deviceConfig->id;
            });
            if ($hasFound)
                continue;
            $biotimeService->deleteUser($deviceConfig->serial_number, ConstData::DEFAULT_BIOTIME_PORT, $this->id);
        }
    }

    public function other_time_shedule_employees() {
        return $this->hasMany(TimeScheduleEmployee::class)->where(TimeScheduleEmployee::TYPE, TimeScheduleEmployee::TYPE_OTHER)->orderBy(TimeScheduleEmployee::BEGIN_DATE, 'desc');
    }

    public function employee_voucher_amounts() {
        return $this->hasMany(EmployeeVoucherAmount::class);
    }

    public function employee_voucher_spents() {
        return $this->hasMany(EmployeeVoucherSpent::class);
    }
}
