<?php

namespace App\Models\Customer\Time\Approval;

use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ApprovalConfigSubDtl extends Model
{
    use HasFactory;
    const TABLE                     = 'approval_config_sub_dtls';

    const ID                        = 'id';
    const APPROVAL_CONFIG_HDR_ID    = 'approval_config_hdr_id';
    const EMPLOYEE_USER_ID          = 'employee_user_id';
    const CREATED_BY                = 'created_by';

    public function __construct($attributes = array()) {
        $this->connection             = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        self::ID,
        self::APPROVAL_CONFIG_HDR_ID,
        self::EMPLOYEE_USER_ID,
        self::CREATED_BY,
    ];

    const RELATION_APPROVAL_CONFIG = 'approval_config_hdr';

    public function approval_config_hdr() {
        return $this->belongsTo(ApprovalConfigHdr::class);
    }
}
