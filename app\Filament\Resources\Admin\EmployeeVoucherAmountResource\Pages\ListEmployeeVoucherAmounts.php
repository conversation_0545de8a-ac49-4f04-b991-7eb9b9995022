<?php

namespace App\Filament\Resources\Admin\EmployeeVoucherAmountResource\Pages;

use App\Filament\Resources\Admin\EmployeeVoucherAmountResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListEmployeeVoucherAmounts extends ListRecords
{
    protected static string $resource = EmployeeVoucherAmountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-o-plus'),
        ];
    }
}
