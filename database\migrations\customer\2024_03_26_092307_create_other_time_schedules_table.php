<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOtherTimeSchedulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('other_time_schedules'))
            return;
        Schema::create('other_time_schedules', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('short_name');
            $table->date('begin_date');
            $table->longText('hash_str');
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->unique(['name', 'begin_date']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('other_time_schedules');
    }
}
