<?php

namespace App\Models;

use App\Models\Constant\ConstData;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Organization extends Model
{
    use HasFactory;
    protected $connection = ConstData::ADMIN_DB;
    const TABLE           = 'organizations';

    const ID                  = 'id';
    const NAME                = 'name';
    const REGISTRATION_NUMBER = 'registration_number';
    const DATABASE_CONFIG_ID  = 'database_config_id';
    const CITY_ID             = 'city_id';
    const DISTRICT_ID         = 'district_id';
    const KHOROO_ID           = 'khoroo_id';
    const ADDRESS             = 'address';
    const CREATED_BY          = 'created_by';
    const UPDATED_BY          = 'updated_by';

    const RELATION_USERS           = 'users';
    const RELATION_DATABASE_CONFIG = 'database_config';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        self::NAME,
        self::REGISTRATION_NUMBER,
        self::DATABASE_CONFIG_ID,
        self::CITY_ID,
        self::DISTRICT_ID,
        self::KHOROO_ID,
        self::ADDRESS,
        self::CREATED_BY,
        self::UPDATED_BY,
    ];

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_organization');
    }

    public function database_config(): BelongsTo
    {
        return $this->belongsTo(DatabaseConfig::class);
    }

}
