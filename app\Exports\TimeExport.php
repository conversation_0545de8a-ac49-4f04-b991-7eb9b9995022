<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class TimeExport implements FromView
{
    public $salaryDayFrequencyDtls;
    public $unitData;
    public $totalData;
    public $hasDetail;

    /**
     * TimeExport constructor.
     *
     * @param $dayData
     * @param $unitData
     */
    public function __construct($salaryDayFrequencyDtls, $unitData, $totalData, $hasDetail)
    {
        $this->salaryDayFrequencyDtls   = $salaryDayFrequencyDtls;
        $this->unitData                 = $unitData;
        $this->totalData                = $totalData;
        $this->hasDetail                = $hasDetail;
    }

    /**
     * @inheritDoc
     */
    public function view(): View
    {
        return view('exports.time', [
            'salaryDayFrequencyDtls'    => $this->salaryDayFrequencyDtls,
            'unitData'                  => $this->unitData,
            'totalData'                 => $this->totalData,
            'hasDetail'                 => $this->hasDetail,
        ]);
    }
}
