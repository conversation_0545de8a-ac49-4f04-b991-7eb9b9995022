<?php

namespace App\Http\Requests\Customer\Device\DeviceAttendance;

use Illuminate\Foundation\Http\FormRequest;

class DownloadAttendanceRequest extends FormRequest
{
    const PARAMETER_YEAR                   = 'year';
    const PARAMETER_MONTH                  = 'month';
    const FILTER                           = 'filter';
    const FILTER_DEVICE_CONFIG_IDS         = 'device_config_ids';
    const FILTER_DEP_IDS                   = 'dep_ids';
    const FILTER_EMP_CODES                 = 'emp_codes';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::PARAMETER_YEAR                        => 'required|integer|between:2000,3000',
            self::PARAMETER_MONTH                       => 'required|integer|between:1,12',
            self::FILTER_DEVICE_CONFIG_IDS              => 'nullable|array|integer',
            self::FILTER_DEP_IDS                        => 'nullable|array|integer',
            self::FILTER_EMP_CODES                      => 'nullable|array|integer',
        ];
    }
}
