<?php

namespace App\Console\Commands;

use App\Services\Time\RawAttendanceService;

use Illuminate\Console\Command;

class AutoRawAttendance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auto:raw-attendance';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $service = resolve(RawAttendanceService::class);
        $service->prepareRawAttendancesForAllAdminUsers();
    }
}
