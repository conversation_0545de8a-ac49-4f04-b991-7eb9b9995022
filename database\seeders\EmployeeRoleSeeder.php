<?php

namespace Database\Seeders;

use App\Models\Constant\ConstData;
use App\Models\EmployeeRole;
use Illuminate\Database\Seeder;

class EmployeeRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        if(EmployeeRole::count() > 0){
            return;
        }

        $employeeRoles = array (
                    'employee_roles' =>
                        array (
                            0 =>
                                array (
                                    'id' => 1,
                                    'name' => ConstData::EMPLOYEE_ROLE_EMPLOYEE,
                                    'description' => 'Ажилтан',
                                ),
                            1 =>
                                array (
                                    'id' => 2,
                                    'name' => ConstData::EMPLOYEE_ROLE_MERCHANT,
                                    'description' => 'Урамшуулал олгох ажилтан',
                                ),
                            2 =>
                                array (
                                    'id' => 3,
                                    'name' => ConstData::EMPLOYEE_ROLE_APPROVER,
                                    'description' => 'Хүсэлт батлах ажилтан',
                                ),
                        ),
                );

        foreach($employeeRoles['employee_roles'] as $employeeRole){
            EmployeeRole::insert([
                EmployeeRole::ID          => $employeeRole[EmployeeRole::ID],
                EmployeeRole::NAME        => $employeeRole[EmployeeRole::NAME],
                EmployeeRole::DESCRIPTION => $employeeRole[EmployeeRole::DESCRIPTION],
            ]);
        }
    }
}
