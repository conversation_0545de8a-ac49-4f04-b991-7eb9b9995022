# Time Request Valid Dates Endpoint

## Overview

The `/api/time-requests/valid-dates/{att_status}` endpoint provides date constraints for time request form date selection. It shares the same validation logic as the time request validation system but returns date constraints instead of validation results.

## Endpoint

```
GET /api/time-requests/valid-dates/{att_status}
```

### Parameters

- `att_status` (integer): The attendance status ID from `EmployeeAttendanceDtl` constants

### Authentication

Requires authentication via San<PERSON><PERSON> token.

### Response Format

```json
{
    "success": true,
    "data": {
        "type": "min_date|none",
        "min_date": "2024-02-15|null",
        "max_date": "2024-12-31|null", 
        "message": "Human readable constraint description"
    }
}
```

### Response Fields

- `type`: Type of constraint (`min_date`, `max_date`, `range`, or `none`)
- `min_date`: Minimum allowed date in YYYY-MM-DD format (null if no minimum)
- `max_date`: Maximum allowed date in YYYY-MM-DD format (null if no maximum)
- `message`: Human-readable description of the constraint

## Attendance Status Handling

### ATTENDANCE_STATUS_VACATION (12)

**Logic**: Max date of (employee company_work_date + shift) and (value1 now date + shift)

**Configurations Used**:
- `ATTENDANCE_STATUS_VACATION` (value field) - Company work date anchor
- `TIME_REQUEST_ATTENDANCE_STATUS_VACATION_MIN_DATE` (value1 field) - Now date anchor

**Example Response**:
```json
{
    "success": true,
    "data": {
        "type": "min_date",
        "min_date": "2024-06-01",
        "max_date": null,
        "message": "Vacation requests must be after 2024-06-01"
    }
}
```

### ATTENDANCE_STATUS_OUT_WORK (17)

**Logic**: Only value1 configuration

**Configurations Used**:
- `TIME_REQUEST_ATTENDANCE_STATUS_OUT_WORK_MIN_DATE` (value1 field)

**Example Response**:
```json
{
    "success": true,
    "data": {
        "type": "min_date",
        "min_date": "2024-02-18",
        "max_date": null,
        "message": "Requests must be after 2024-02-18"
    }
}
```

### ATTENDANCE_STATUS_FURLOUGH_LONG (83)

**Logic**: Only value1 configuration

**Configurations Used**:
- `TIME_REQUEST_ATTENDANCE_STATUS_FURLOUGH_LONG_MIN_DATE` (value1 field)

**Example Response**:
```json
{
    "success": true,
    "data": {
        "type": "min_date",
        "min_date": "2024-02-22",
        "max_date": null,
        "message": "Requests must be after 2024-02-22"
    }
}
```

### Salary Furlough Types (91, 92, 93, 94)

**Statuses**:
- `ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY` (91)
- `ATTENDANCE_STATUS_SALARY_FURLOUGH_KID_BIRTHDAY` (92)
- `ATTENDANCE_STATUS_SALARY_FURLOUGH_HALFDAY` (93)
- `ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE` (94)

**Logic**: Max of value and value1 valid dates

**Configurations Used**:
- `ATTENDANCE_STATUS_SALARY_FURLOUGH.{STATUS_NAME}` (value field) - Count-based constraints
- `TIME_REQUEST_{STATUS_NAME}_MIN_DATE` (value1 field) - Anchor-based constraints

**Example Response**:
```json
{
    "success": true,
    "data": {
        "type": "min_date",
        "min_date": "2024-04-01",
        "max_date": null,
        "message": "Salary furlough requests must be after 2024-04-01"
    }
}
```

## Shared Logic

The endpoint uses the `TimeRequestDateConstraintService` which shares core logic with `TimeRequestValidationService`:

### Shared Components

1. **ChronoAnchorComponent**: For parsing anchor-based configurations
2. **ChronoCountComponent**: For parsing count-based configurations  
3. **Date calculation methods**: Same shift application and session calculation logic
4. **Configuration resolution**: Same configuration ID mapping

### Benefits

- **Consistency**: Frontend date constraints match backend validation
- **Maintainability**: Single source of truth for date logic
- **Accuracy**: No discrepancy between what's allowed and what's validated

## Error Handling

### Invalid Attendance Status

```json
{
    "success": true,
    "data": {
        "type": "none",
        "min_date": null,
        "max_date": null,
        "message": ""
    }
}
```

### Server Error

```json
{
    "success": false,
    "message": "Огнооны мэдээлэл авахад алдаа гарлаа.",
    "data": {
        "type": "none",
        "min_date": null,
        "max_date": null,
        "message": ""
    }
}
```

## Usage Examples

### Frontend Date Picker Integration

```javascript
// Fetch date constraints when attendance status changes
async function updateDateConstraints(attStatus) {
    try {
        const response = await fetch(`/api/time-requests/valid-dates/${attStatus}`);
        const data = await response.json();
        
        if (data.success && data.data.type === 'min_date') {
            // Set minimum date on date picker
            datePicker.setMinDate(data.data.min_date);
            
            // Show constraint message to user
            showMessage(data.data.message);
        } else {
            // No constraints
            datePicker.clearConstraints();
        }
    } catch (error) {
        console.error('Failed to fetch date constraints:', error);
    }
}
```

### Form Validation

```javascript
// Validate selected date against constraints
function validateSelectedDate(selectedDate, attStatus) {
    return fetch(`/api/time-requests/valid-dates/${attStatus}`)
        .then(response => response.json())
        .then(data => {
            if (data.data.type === 'min_date' && data.data.min_date) {
                return selectedDate >= data.data.min_date;
            }
            return true; // No constraints
        });
}
```

## Testing

The endpoint includes comprehensive test coverage:

- Authentication requirements
- Response structure validation
- Different attendance status handling
- Error condition handling
- Integration with shared validation logic
