<?php

namespace App\Services;

use App\Exceptions\SystemException;
use App\Models\Constant\ConstData;

use Illuminate\Support\Facades\DB;

class ConnectionService
{
    static public function connectionName() {
        $user = auth()->user();
        return self::connectionNameByUser($user);
    }

    static public function connectionNameByUser($user) {
        if (!isset($user) || !$user->organizations)
            return null;
        $organization = $user->organizations->first();
        if (!$organization)
            throw new SystemException(ConstData::SYSTEM_EXCEPTION, 1);
        $connectionName = $organization->database_config->connection_name;
        if (!$connectionName)
            throw new SystemException(ConstData::SYSTEM_EXCEPTION, 1);
        return $connectionName;
    }

    static public function getConnection() {
        $connectionName = self::connectionName();
        return DB::connection($connectionName);
    }

    static public function getCNForEmployeeUser() {
        return auth()->user()->organization->database_config->connection_name;
    }

    static public function getOrganizationIdForEmployeeUser() {
        return auth()->user()->organization->id;
    }
}
