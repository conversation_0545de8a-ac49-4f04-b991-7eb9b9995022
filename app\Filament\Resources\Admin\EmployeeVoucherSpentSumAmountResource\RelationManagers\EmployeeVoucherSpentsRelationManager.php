<?php

namespace App\Filament\Resources\Admin\EmployeeVoucherSpentSumAmountResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class EmployeeVoucherSpentsRelationManager extends RelationManager
{
    protected static string $relationship = 'employee_voucher_spents';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->heading('Задаргаа')
            ->columns([
                Tables\Columns\TextColumn::make('merchant_company_name')->label('Мерчант нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('merchant_location_name')->label('Мерчант хаяг')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('merchant_employee_display_name')->label('Мерчант ажилтаны нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('created_at')->label('Огноо')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('amount')->label('Дүн')->sortable()->searchable()->money('MNT'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
