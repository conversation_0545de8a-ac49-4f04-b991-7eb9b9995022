<?php

namespace App\Http\Controllers\Customer;

use App\Models\Customer\Employee;
use App\Models\Customer\Voucher\EmployeeVoucher;
use App\Models\Customer\Voucher\EmployeeVoucherAmount;
use App\Http\Resources\EmployeeVoucher as EmployeeVoucherResource;
use App\Http\Resources\EmployeeVoucherAmount as EmployeeVoucherAmountResource;
use App\Services\ConnectionService;
use App\Http\Controllers\Controller;

class EmployeeVoucherController extends Controller
{
    public function getCN() {
        $service = resolve(ConnectionService::class);
        return $service->getCNForEmployeeUser();
    }

    public function getAUEmployee() 
    {
        $cn           = $this->getCN();
        $employeeUser = auth()->user();
        $employee     = Employee::on($cn)->where(Employee::PHONE, $employeeUser->phone)->first();
        return $employee;
    }

    public function index()
    {   
        $cn           = $this->getCN();
        $employee     = $this->getAUEmployee();
        $emplVouchers = EmployeeVoucher::on($cn)->where(EmployeeVoucher::EMPLOYEE_ID, $employee->id)
                        ->where(EmployeeVoucher::IS_ACTIVE, true)
                        ->orderByDesc('created_at')->get();
        return EmployeeVoucherResource::collection($emplVouchers);
    }

    public function me()
    {   
        $cn           = $this->getCN();
        $employee     = $this->getAUEmployee();
        $empVoucheAmt = EmployeeVoucherAmount::on($cn)->where(EmployeeVoucherAmount::EMPLOYEE_ID, $employee->id)->first();
        return new EmployeeVoucherAmountResource($empVoucheAmt);
    }
}
