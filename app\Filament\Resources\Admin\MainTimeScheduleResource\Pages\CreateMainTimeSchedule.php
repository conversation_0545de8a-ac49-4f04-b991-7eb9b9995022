<?php

namespace App\Filament\Resources\Admin\MainTimeScheduleResource\Pages;

use App\Models\Customer\Time\Schedule\MainTimeSchedule\MainTimeSchedule;

use App\Filament\Resources\Admin\MainTimeScheduleResource;
use Filament\Resources\Pages\CreateRecord;

class CreateMainTimeSchedule extends CreateRecord
{
    protected static string $resource = MainTimeScheduleResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data[MainTimeSchedule::CREATED_BY] = auth()->user()->id;
        return $data;
    }

    protected function afterCreate(): void
    {
        $mainTimeSchdule = $this->record;
        $mainTimeSchdule->updated_at = null;
        $mainTimeSchdule->getHashStr();
        $mainTimeSchdule->save();

        $mainTimeScheduleDtls = $mainTimeSchdule->getDefaultMainTimeScheduleDtls();
        foreach ($mainTimeScheduleDtls as $newTimeScheduleDtl) {
            $newTimeScheduleDtlBts = $newTimeScheduleDtl->getDefaultMainTimeScheduleDtlBts();
            $newTimeScheduleDtl->created_by = auth()->user()->id;
            $mainTimeSchdule->time_schedule_dtls()->save($newTimeScheduleDtl);
            $newTimeScheduleDtl->time_schedule_dtl_bts()->saveMany($newTimeScheduleDtlBts);
        }
    }
}
