<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        if(Role::count() > 0){
            return;
        }

        $roles = array (
                    'roles' =>
                        array (
                            0 =>
                                array (
                                    'id' => 1,
                                    'name' => 'superadmin',
                                    'description' => 'sa',
                                ),
                            1 =>
                                array (
                                    'id' => 2,
                                    'name' => 'admin',
                                    'description' => 'a',
                                ),
                        ),
                );

        foreach($roles['roles'] as $role){
            Role::insert([
                'id' => $role['id'],
                'name' => $role['name'],
                'description' => $role['description']
            ]);
        }
    }
}
