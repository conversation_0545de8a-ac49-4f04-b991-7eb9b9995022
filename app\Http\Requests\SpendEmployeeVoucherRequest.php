<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SpendEmployeeVoucherRequest extends FormRequest
{   
    const PARAMETER_UNIQ_ID = 'uniq_id';
    const PARAMETER_AMOUNT  = 'amount';
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'uniq_id' => 'required|string',
            'amount'  => 'required|integer|min:1',
        ];
    }
}
