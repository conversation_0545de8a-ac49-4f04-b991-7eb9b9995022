<?php

namespace App\Service;

use App\Models\Customer\Configuration;
use App\Models\Constant\ConstData;
use App\Exceptions\SystemException;
use App\Http\Tools\DateTool;

class ConfigurationService
{
    public function getConfigurationValueByConfigId($connectionName, $id) {
        $configuration = new Configuration();
        $configuration->setConnection($connectionName);
        $configuration = $configuration->where(Configuration::CONFIG_ID, $id)->first();

        if (!$configuration)
            throw new SystemException(ConstData::CONFIG_EXCEPTION, 1);
        return $configuration->value;
    }

    public function getConfigurationByConfigId($connectionName, $id) {
        $configuration = new Configuration();
        return $configuration->on($connectionName)->where(Configuration::CONFIG_ID, $id)->first();
    }

    public function getConfigurationsByConfigId($connectionName, $id) {
        $configuration = new Configuration();
        return $configuration->on($connectionName)->where(Configuration::CONFIG_ID, $id)->get();
    }
}
