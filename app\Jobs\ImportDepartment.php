<?php

namespace App\Jobs;

use App\Models\Constant\ConstData;
use App\Imports\DepartmentImport;
use App\Service\DepartmentService;
use App\Exceptions\SystemException;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Storage;

use Illuminate\Support\Facades\DB;
class ImportDepartment implements ShouldQueue, ShouldBeUnique {
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 1200;
    public $tries   = 1;

    protected $connectionName;
    protected $organizationId;
    protected $user;
    protected $filePath;
    protected $fileName;
    protected $queueUniqueId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($connectionName, $organizationId, $user, $file) {
        $this->connectionName   = $connectionName;
        $this->organizationId   = $organizationId;
        $this->user             = $user;
        $baseName               = basename(strval($file));
        $this->fileName         = $baseName;
        $this->filePath         = Storage::put($baseName, $file);
        $this->queueUniqueId    = 'import-department-'. $this->organizationId . '-' . $this->user->id;
        $this->onConnection(ConstData::QC_REDIS);
    }

    public function uniqueId() {
        return $this->queueUniqueId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(DepartmentService $departmentService) {
        try {
            $connectionName = $this->connectionName;
            $filePath       = $this->filePath;
            $user           = $this->user;
            $userId         = $user->id;

            $cn = DB::connection($connectionName);
            $cn->beginTransaction();
            $allDepartments = $departmentService->getDepartmentsByUser($connectionName, $user);
            Excel::import(new DepartmentImport($connectionName, $allDepartments, $userId), $filePath);
            $cn->commit();
        } catch (SystemException $se) {
            $cn->rollBack();
            return $se->getCustomMessage();
        } finally {
            DB::disconnect($connectionName);
        }
    }
}
