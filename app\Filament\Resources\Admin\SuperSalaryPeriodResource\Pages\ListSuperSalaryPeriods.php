<?php

namespace App\Filament\Resources\Admin\SuperSalaryPeriodResource\Pages;

use App\Filament\Resources\Admin\SuperSalaryPeriodResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSuperSalaryPeriods extends ListRecords
{
    protected static string $resource = SuperSalaryPeriodResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-o-plus'),
        ];
    }
}
