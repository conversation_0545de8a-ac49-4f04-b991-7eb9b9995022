<?php

namespace App\Models;

use App\Models\Constant\ConstData;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class DatabaseConfig extends Model
{
    use HasFactory;
    protected $connection = ConstData::ADMIN_DB;
    const TABLE           = 'database_configs';

    const ID                  = 'id';
    const CONNECTION_NAME     = 'connection_name';
    const DRIVER              = 'driver';
    const HOST                = 'host';
    const PORT                = 'port';
    const DATABASE            = 'database';
    const USER_NAME           = 'user_name';
    const PASSWORD            = 'password';
    const CREATED_BY          = 'created_by';
    const UPDATED_BY          = 'updated_by';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        self::CONNECTION_NAME,
        self::DRIVER,
        self::HOST,
        self::PORT,
        self::DATABASE,
        self::USER_NAME,
        self::PASSWORD,
        self::CREATED_BY,
        self::UPDATED_BY,
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        self::PASSWORD,
        'remember_token',
    ];

    public static function getCNByOrganizationName($organizationName)
    {
        return 'tm_' . Str::camel(preg_replace('/\s+/', '_', $organizationName));
    }

}
