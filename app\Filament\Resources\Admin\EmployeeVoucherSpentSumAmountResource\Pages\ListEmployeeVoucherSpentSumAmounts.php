<?php

namespace App\Filament\Resources\Admin\EmployeeVoucherSpentSumAmountResource\Pages;

use App\Filament\Exports\EmployeeVoucherSpentExporter;
use App\Filament\Resources\Admin\EmployeeVoucherSpentSumAmountResource;
use Filament\Tables\Actions\ExportAction;
use Filament\Resources\Pages\ListRecords;


class ListEmployeeVoucherSpentSumAmounts extends ListRecords
{
    protected static string $resource = EmployeeVoucherSpentSumAmountResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }
}
