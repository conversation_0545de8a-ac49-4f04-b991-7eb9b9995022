<?php

namespace App\Service\Time;

use App\Models\Customer\Time\TimeScheduleShiftEmployee;
use App\Models\Customer\Employee;
use App\Models\Constant\ConstData;
use App\Http\Tools\DateTool;
use App\Exceptions\SystemException;

use Illuminate\Database\Eloquent\Builder;

class TimeScheduleShiftService
{
    /**
     * Ажилтан дээр ээлжийн цагийн хуваарь тохируулах
     */
    public function setTimeScheduleShiftEmployees($connectionName, $employeeIds, $beginDate, $endDate, $lateMin, $afrMin, $hasAuto, $gapTime, $autoEndDate, $userId) {
        $timeScheduleShiftEmployees = collect([]);
        $errorData = [];
        foreach ($employeeIds as $key => $employeeId) {
            $dupTimeScheduleShiftEmployee = $this->checkDuplicatedTimeScheduleShiftEmployee($connectionName, null, $employeeId, $beginDate, $endDate);

            if ($dupTimeScheduleShiftEmployee) {
                $errorData[] = (object) [ TimeScheduleShiftEmployee::RELATION_EMPLOYEE => $dupTimeScheduleShiftEmployee->employee, TimeScheduleShiftEmployee::BEGIN_DATE => $dupTimeScheduleShiftEmployee->begin_date, TimeScheduleShiftEmployee::END_DATE => $dupTimeScheduleShiftEmployee->end_date];
                continue;
            }

            if ($hasAuto) {
                $diffMin = DateTool::getMinuteBetweenDates($beginDate, $endDate);
                $shiftBeginDate = $beginDate;
                while ($shiftBeginDate <= $autoEndDate) {
                    $shiftEndDate = DateTool::addMinute($shiftBeginDate, $diffMin);
                    $timeScheduleShiftEmployee = new TimeScheduleShiftEmployee();
                    $timeScheduleShiftEmployee->setConnection($connectionName);
                    $timeScheduleShiftEmployee->employee_id = $employeeId;
                    $timeScheduleShiftEmployee->begin_date  = $shiftBeginDate;
                    $timeScheduleShiftEmployee->end_date    = DateTool::addMinute($shiftBeginDate, $diffMin);
                    $timeScheduleShiftEmployee->late_min    = $lateMin;
                    $timeScheduleShiftEmployee->afr_min     = $afrMin;
                    $timeScheduleShiftEmployee->created_by  = $userId;
                    $timeScheduleShiftEmployee->save();
                    $timeScheduleShiftEmployees->push($timeScheduleShiftEmployee);
                    $shiftBeginDate = $shiftEndDate;
                    $shiftBeginDate = DateTool::addMinute($shiftBeginDate, $gapTime);
                }
            } else {
                $timeScheduleShiftEmployee = new TimeScheduleShiftEmployee();
                $timeScheduleShiftEmployee->setConnection($connectionName);
                $timeScheduleShiftEmployee->employee_id = $employeeId;
                $timeScheduleShiftEmployee->begin_date  = $beginDate;
                $timeScheduleShiftEmployee->end_date    = $endDate;
                $timeScheduleShiftEmployee->late_min    = $lateMin;
                $timeScheduleShiftEmployee->afr_min     = $afrMin;
                $timeScheduleShiftEmployee->created_by  = $userId;
                $timeScheduleShiftEmployee->save();
                $timeScheduleShiftEmployees->push($timeScheduleShiftEmployee);
            }
        }
        if (count($errorData) > 0)
            throw new SystemException(ConstData::TIME_EXCEPTION, 317, $errorData);
        return $timeScheduleShiftEmployees;
    }

    public function checkDuplicatedTimeScheduleShiftEmployee($connectionName, $timeScheduleShiftEmployeeId, $employeeId, $beginDate, $endDate) {
        $timeScheduleShiftEmployee = new TimeScheduleShiftEmployee();
        $timeScheduleShiftEmployee = $timeScheduleShiftEmployee->on($connectionName)->with(TimeScheduleShiftEmployee::RELATION_EMPLOYEE)->where(TimeScheduleShiftEmployee::EMPLOYEE_ID, $employeeId)
                                    ->where(TimeScheduleShiftEmployee::ID, '!=', $timeScheduleShiftEmployeeId)
                                    ->where(
                                        function($query) use($beginDate, $endDate) {
                                            return $query->where(
                                                function($query) use($beginDate, $endDate) {
                                                    return $query->whereDate(TimeScheduleShiftEmployee::BEGIN_DATE, '>=', $beginDate)->whereDate(TimeScheduleShiftEmployee::END_DATE, '<=', $endDate);
                                                }
                                              )->orWhere(
                                                function($query) use($beginDate, $endDate) {
                                                    return $query->whereDate(TimeScheduleShiftEmployee::BEGIN_DATE, '<', $beginDate)->whereDate(TimeScheduleShiftEmployee::END_DATE, '>', $endDate);
                                                }
                                              )->orWhere(
                                                function($query) use($beginDate, $endDate) {
                                                    return $query->whereDate(TimeScheduleShiftEmployee::BEGIN_DATE, '<', $beginDate)->whereBetween(TimeScheduleShiftEmployee::END_DATE, [$beginDate, $endDate]);
                                                }
                                              )->orWhere(
                                                function($query) use($beginDate, $endDate) {
                                                    return $query->whereBetween(TimeScheduleShiftEmployee::BEGIN_DATE, [$beginDate, $endDate])->whereDate(TimeScheduleShiftEmployee::END_DATE, '>', $endDate);
                                                }
                                              );
                                        }
                                      )
                                    ->first();
        return $timeScheduleShiftEmployee;
    }

    /**
     * ЭЭЛЖИЙН ЦАГИЙН ХУВААРЬ
     */
    public function getTimeScheduleShiftEmployeesByBetweenDates($connectionName, $beginDate, $endDate, $departmentIds) {
        $timeScheduleShiftEmployees = new TimeScheduleShiftEmployee();
        $timeScheduleShiftEmployees = $timeScheduleShiftEmployees->on($connectionName);

        if (count($departmentIds) > 0) {
            $timeScheduleShiftEmployees = $timeScheduleShiftEmployees->whereHas(TimeScheduleShiftEmployee::RELATION_EMPLOYEE, function (Builder $query) use($departmentIds) {
                $query->whereIn(Employee::DEPARTMENT_ID, $departmentIds);
            });
        }
        $timeScheduleShiftEmployees = $timeScheduleShiftEmployees->where(
                                        function($query) use($beginDate, $endDate) {
                                            return $query->where(
                                                function($query) use($beginDate, $endDate) {
                                                    return $query->whereDate(TimeScheduleShiftEmployee::BEGIN_DATE, '>=', $beginDate)->whereDate(TimeScheduleShiftEmployee::END_DATE, '<=', $endDate);
                                                }
                                              )->orWhere(
                                                function($query) use($beginDate, $endDate) {
                                                    return $query->whereDate(TimeScheduleShiftEmployee::BEGIN_DATE, '<', $beginDate)->whereDate(TimeScheduleShiftEmployee::END_DATE, '>', $endDate);
                                                }
                                              )->orWhere(
                                                function($query) use($beginDate, $endDate) {
                                                    return $query->whereDate(TimeScheduleShiftEmployee::BEGIN_DATE, '<', $beginDate)->whereBetween(TimeScheduleShiftEmployee::END_DATE, [$beginDate, $endDate]);
                                                }
                                              )->orWhere(
                                                function($query) use($beginDate, $endDate) {
                                                    return $query->whereBetween(TimeScheduleShiftEmployee::BEGIN_DATE, [$beginDate, $endDate])->whereDate(TimeScheduleShiftEmployee::END_DATE, '>', $endDate);
                                                }
                                              );
                                        }
                                      )
                                    ->get();
        return $timeScheduleShiftEmployees;
   }

   public function getTimeScheduleShiftEmployeesByDate($connectionName, $employeeId, $attDate) {
        $timeScheduleShiftEmployee = new TimeScheduleShiftEmployee();
        $timeScheduleShiftEmployee->setConnection($connectionName);
        $timeScheduleShiftEmployee = $timeScheduleShiftEmployee->on($connectionName)
                                    ->where(TimeScheduleShiftEmployee::EMPLOYEE_ID, $employeeId)
                                    ->whereDate(TimeScheduleShiftEmployee::BEGIN_DATE, '<=', $attDate)
                                    ->whereDate(TimeScheduleShiftEmployee::END_DATE, '>=', $attDate)->get();
        return $timeScheduleShiftEmployee;
    }
}
