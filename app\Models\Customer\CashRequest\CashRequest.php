<?php

namespace App\Models\Customer\CashRequest;

use App\Services\ConnectionService;
use App\Enums\CashRequestStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CashRequest extends Model
{
    use HasFactory;

    const DEPARTMENT_NAME         = 'department_name';
    const EMPLOYEE_ID             = 'employee_id';
    const EMPLOYEE_LAST_NAME      = 'employee_last_name';
    const EMPLOYEE_FIRST_NAME     = 'employee_first_name';
    const IS_COMPANY              = 'is_company';
    const RECEIVER_NAME           = 'receiver_name';
    const TRANSACTION_DESCRIPTION = 'transaction_description';
    const BANK_ID                 = 'bank_id';
    const BANK_NAME               = 'bank_name';
    const IBAN_NUMBER             = 'iban_number';
    const ACCOUNT_NUMBER          = 'account_number';
    const AMOUNT                  = 'amount';
    const STATUS                  = 'status';

    const RELATION_REQUEST_DTLS        = 'cash_request_dtls';
    const RELATION_REQUEST_ATTACHMENTS = 'cash_request_attachments';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::DEPARTMENT_NAME,
        self::EMPLOYEE_ID,
        self::EMPLOYEE_LAST_NAME,
        self::EMPLOYEE_FIRST_NAME,
        self::IS_COMPANY,
        self::RECEIVER_NAME,
        self::TRANSACTION_DESCRIPTION,
        self::BANK_NAME,
        self::IBAN_NUMBER,
        self::ACCOUNT_NUMBER,
        self::AMOUNT,
        self::STATUS,
    ];

    protected $casts = [
        self::IS_COMPANY => 'boolean',
        self::STATUS     => CashRequestStatusEnum::class
    ];

    public function cash_request_dtls() {
        return $this->hasMany(CashRequestDtl::class);
    }

    public function cash_request_attachments()
    {
        return $this->hasMany(CashRequestAttachment::class);
    }
}
