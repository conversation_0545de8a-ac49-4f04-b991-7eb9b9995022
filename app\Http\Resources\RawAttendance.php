<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class Decision
 *
 * @mixin \App\Models\Customer\Time\Decision\Decision
 */
class RawAttendance extends JsonResource
{
    const ID                   = 'id';
    const REGISTER_DATE        = 'register_date';
    const TYPE                 = 'type';
    const FROM_NAME            = 'from_name';
    const LATITUDE             = 'latitude';
    const LONGITUDE            = 'longitude';


    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                   => $this->id,
            self::REGISTER_DATE        => $this->register_date,
            self::TYPE                 => $this->type,
            self::FROM_NAME            => $this->from_name,
            self::LATITUDE             => $this->latitude,
            self::LONGITUDE            => $this->longitude,
        ];
    }
}
