<?php

namespace App\Filament\Resources\Admin\EmployeeAttendanceResource\RelationManagers;

use App\Http\Tools\DateTool;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\SelectColumn;

class EmployeeAttendanceDtlsRelationManager extends RelationManager
{
    protected static string $relationship = 'employee_attendance_dtls';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('begin_date')
                    ->label('Эхлэх цаг')
                    ->formatStateUsing(fn (string $state): string => DateTool::getTime($state)),
                Tables\Columns\TextColumn::make('end_date')
                    ->label('Дуусах цаг')
                    ->formatStateUsing(fn (string $state): string => DateTool::getTime($state)),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Үүсгэсэн огноо')
                    ->formatStateUsing(fn (string $state): string => DateTool::getDateWithTime($state))
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Засагдсан огноо')
                    ->formatStateUsing(fn (string $state): string => DateTool::getDateWithTime($state))
                    ->toggleable(isToggledHiddenByDefault: true),
                SelectColumn::make('attendance_status')
                    ->options(fn (EmployeeAttendanceDtl $record): array => $record->isDisabledAttStatus() ? $record->getDisabledAttStatusNames() : $record->getEnabledAttStatusNames())
                    ->disabled(function (EmployeeAttendanceDtl $record2): bool {
                        return $record2->isDisabledAttStatus();
                    })
                    ->afterStateUpdated(function ($record) {
                        $employeeAttendance = $record->employee_attendance;
                        $employeeAttendance->clearTime();
                        foreach ($employeeAttendance->employee_attendance_dtls as $key => $employeeAttendanceDtl) {
                            $employeeAttendance->setTimeColumn($employeeAttendanceDtl);
                        }
                        $employeeAttendance->save();
                        $this->dispatch('employee-status-changed');
                    })
                    ->selectablePlaceholder(false),
                ]);
    }
}
