<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class DecisionHire
 *
 * @mixin \App\Models\Customer\Time\Decision\DecisionHire
 */
class DecisionHire extends JsonResource
{
    const ID                         = 'id';
    const DECISION_ID                = 'decision_id';
    const HIRE_DATE                  = 'hire_date';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                            => $this->id,
            self::DECISION_ID                   => $this->decision_id,
            self::HIRE_DATE                     => $this->hire_date,
        ];
    }
}
