<?php

namespace App\Services;

use App\Models\Organization;
use App\Models\Customer\Employee;
use App\Models\Customer\Voucher\VoucherTohirgoo;
use App\Models\Customer\Voucher\EmployeeVoucher;
use App\Models\Customer\Voucher\EmployeeVoucherAmount;
use App\Enums\EmployeeVoucherStatusEnum;
use App\Http\Tools\DateTool;
use Illuminate\Support\Str;

class EmployeeVoucherService
{
    public function createEmployeeVouchersByAllOrganizations() {
        $organizations = Organization::get();
        foreach ($organizations as $key => $organization) {
            $this->createEmployeeVouchersByOrganization($organization);
        };
    }

    public function createEmployeeVouchersByOrganization($organization) 
    {
        $cn               = $organization->database_config->connection_name;
        $voucherTohirgoos = VoucherTohirgoo::on($cn)->where(VoucherTohirgoo::IS_USED, false)
                                                    ->where(VoucherTohirgoo::BEGIN_DATE, DateTool::nowDate())
                                                    ->get();
        $this->createEmployeeVouchersByVoucherTohirgoos($cn, $voucherTohirgoos);
    }

    public function createEmployeeVouchersByVoucherTohirgoos($cn, $voucherTohirgoos) 
    {
        foreach ($voucherTohirgoos as $key => $voucherTohirgoo) {
            if ($voucherTohirgoo->begin_date > DateTool::nowDate()) 
                continue;
            $this->createEmployeeVouchersByVoucherTohirgoo($cn, $voucherTohirgoo);
        }
    }

    public function createEmployeeVouchersByVoucherTohirgoo($cn, $voucherTohirgoo) 
    {
        $isUsed = false;
        if ($voucherTohirgoo->departments()->count() > 0) {
            $departments = $voucherTohirgoo->departments()->get();
            foreach ($departments as $key => $department) {
                $employees = Employee::on($cn)->where(Employee::DEPARTMENT_ID, $department->id)->get();
                foreach ($employees as $key => $employee)
                    $this->createEmployeeVoucher($cn, $employee->id, $voucherTohirgoo);
            }
            $isUsed = true;
        }
        if ($voucherTohirgoo->employees()->count() > 0) {
            $employees = $voucherTohirgoo->employees()->get();
            foreach ($employees as $key => $employee) {
                $this->createEmployeeVoucher($cn, $employee->id, $voucherTohirgoo);
            }
            $isUsed = true;
        }
        $voucherTohirgoo->update([
            VoucherTohirgoo::IS_USED => $isUsed
        ]);
    }

    public function createEmployeeVoucher($cn, $employeeId, $voucherTohirgoo) {
        $employeeVoucher = EmployeeVoucher::on($cn)
                                          ->where(EmployeeVoucher::EMPLOYEE_ID, $employeeId)
                                          ->where(EmployeeVoucher::VOUCHER_TOHIRGOO_ID, $voucherTohirgoo->id)
                                          ->first();
        if (isset($employeeVoucher))
            return;
        $employeeVoucher = new EmployeeVoucher();
        $employeeVoucher->setConnection($cn);
        $employeeVoucher->employee_id         = $employeeId;
        $employeeVoucher->voucher_tohirgoo_id = $voucherTohirgoo->id;
        $employeeVoucher->code                = $this->createVoucherCode($employeeId, $voucherTohirgoo->id);
        $employeeVoucher->amount              = $voucherTohirgoo->amount;
        $employeeVoucher->used_amount         = 0;
        $employeeVoucher->unused_amount       = $voucherTohirgoo->amount;
        $employeeVoucher->status              = EmployeeVoucherStatusEnum::OGT;
        $employeeVoucher->save();

        $employeeVoucherAmount = EmployeeVoucherAmount::on($cn)->where(EmployeeVoucherAmount::EMPLOYEE_ID, $employeeId)->first();
        if (!isset($employeeVoucherAmount)) {
            $employeeVoucherAmount = new EmployeeVoucherAmount();
            $employeeVoucherAmount->setConnection($cn);
            $employeeVoucherAmount->employee_id         = $employeeId;
            $employeeVoucherAmount->unused_total_amount = $voucherTohirgoo->amount;
            $employeeVoucherAmount->used_total_amount   = 0;
            $employeeVoucherAmount->save();
        } else {
            $employeeVoucherAmount->unused_total_amount += $voucherTohirgoo->amount;
            $employeeVoucherAmount->save();
        }
    }

    public function createVoucherCode($employeeId, $voucherTohirgooId) {
        $newEmployeeId        = str_pad($employeeId, 4, '0', STR_PAD_LEFT);
        $newVoucherTohirgooId = str_pad($voucherTohirgooId, 5, '0', STR_PAD_LEFT);
        return 'EV'.DateTool::nowYearMonth().$newVoucherTohirgooId.$newEmployeeId;
    }

    public function createEmployeeVoucherNew($cn, $employeeId, $amount) {
        $employeeVoucher = new EmployeeVoucher();
        $employeeVoucher->setConnection($cn);
        $employeeVoucher->employee_id         = $employeeId;
        $employeeVoucher->code                = Str::uuid()->toString();
        $employeeVoucher->amount              = $amount;
        $employeeVoucher->used_amount         = 0;
        $employeeVoucher->unused_amount       = $amount;
        $employeeVoucher->status              = EmployeeVoucherStatusEnum::OGT;
        $employeeVoucher->save();

        $employeeVoucherAmount = EmployeeVoucherAmount::on($cn)->firstOrNew([EmployeeVoucherAmount::EMPLOYEE_ID => $employeeId]);
        $employeeVoucherAmount->setConnection($cn);
        $employeeVoucherAmount->employee_id = $employeeId;
        $employeeVoucherAmount->save();
    }
}
