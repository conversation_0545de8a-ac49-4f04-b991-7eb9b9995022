<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CashRequest extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                       => $this->id,
            'department_name'          => $this->department_name,
            'employee_last_name'       => $this->employee_last_name,
            'employee_first_name'      => $this->employee_first_name,
            'is_company'               => $this->is_company,
            'transaction_description'  => $this->transaction_description,
            'bank_name'                => $this->bank_name,
            'iban_number'              => $this->iban_number,
            'account_number'           => $this->account_number,
            'amount'                   => $this->amount,
            'status'                   => $this->status,
            'cash_request_attachments' => CashRequestAttachment::collection($this->cash_request_attachments),
        ];
    }
}
