<?php

use App\Models\Organization;
use App\Models\Constant\ConstData;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOrganizationsTable extends Migration
{
    public function up()
    {
        Schema::connection(ConstData::ADMIN_DB)->create(Organization::TABLE, function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('registration_number')->unique();
            $table->string('address')->nullable();
            $table->foreignId('database_config_id')->constrained();
            $table->foreignId('city_id')->nullable();
            $table->foreignId('district_id')->nullable();
            $table->foreignId('khoroo_id')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists(Organization::TABLE);
    }
}
