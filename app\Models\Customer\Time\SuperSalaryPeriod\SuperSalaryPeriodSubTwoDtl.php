<?php

namespace App\Models\Customer\Time\SuperSalaryPeriod;

use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SuperSalaryPeriodSubTwoDtl extends Model
{
    use HasFactory;

    const ID                         = 'id';
    const SUPER_SALARY_PERIOD_DTL_ID = 'super_salary_period_dtl_id';
    const BEGIN_DATE                 = 'begin_date';
    const END_DATE                   = 'end_date';

    const FULL_DATE                        = 'full_date';
    const RELATION_SUPER_SALARY_PERIOD_DTL = 'super_salary_period_dtl';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::SUPER_SALARY_PERIOD_DTL_ID, 
        self::BEGIN_DATE,
        self::END_DATE,
    ];

    public function getFullDateAttribute()
    {
        return "$this->begin_date - $this->end_date";
    }

    public function super_salary_period_dtl()
    {
        return $this->belongsTo(SuperSalaryPeriodDtl::class);
    }
}
