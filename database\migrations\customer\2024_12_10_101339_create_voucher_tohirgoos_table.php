<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('voucher_tohirgoos'))
            return;
        Schema::create('voucher_tohirgoos', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->date('begin_date');
            $table->integer('interval_month');
            $table->unsignedDecimal('amount', $precision = 24, $scale = 2);
            $table->boolean('is_used')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('voucher_tohirgoos');
    }
};
