<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class Apartment
 *
 * @mixin \App\Models\Apartment
 */
class EmployeeDtl extends JsonResource
{
    const ID             = 'id';
    const EMPLOYEE_ID    = 'employee_id';
    
    const UNIQ_ID        = 'uniq_id';
    const EXPIRED_AT     = 'expired_at';
    
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'         => $this->id,
            'uniq_id'    => $this->uniq_id,  
            'expired_at' => $this->expired_at
        ];
    }
}
