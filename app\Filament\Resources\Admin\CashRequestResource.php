<?php

namespace App\Filament\Resources\Admin;

use App\Models\Customer\CashRequest\CashRequest;
use App\Filament\Resources\Admin\CashRequestResource\Pages;
use App\Filament\Resources\Admin\CashRequestResource\RelationManagers;
use App\Models\Class\Can;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class CashRequestResource extends Can
{
    protected static ?string $model = CashRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'хүсэлт';
    protected static ?string $modelLabel = 'хүсэлт';
    protected static ?int $navigationSort = 12;
    protected static ?string $navigationGroup = 'Бэлэн мөнгөний хүсэлт';
    protected static ?string $slug = 'cash-requests';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(CashRequest::DEPARTMENT_NAME)->label('Хэлтэс')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(CashRequest::EMPLOYEE_LAST_NAME)->label('Ажилтаны овог')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(CashRequest::EMPLOYEE_FIRST_NAME)->label('Ажилтаны нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(CashRequest::RECEIVER_NAME)->label('Хүлээн авагч')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(CashRequest::BANK_NAME)->label('Банкны нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(CashRequest::TRANSACTION_DESCRIPTION)->label('Гүйлгээний утга')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(CashRequest::STATUS)->label('Төлөв')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(CashRequest::AMOUNT)->label('Мөнгөн дүн')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('created_at')->label('Үүсгэсэн огноо')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCashRequests::route('/'),
        ];
    }
}
