<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class Apartment
 *
 * @mixin \App\Models\Apartment
 */
class ApprovalConfigDtl extends JsonResource
{
    const ID          = 'id';
    const EMPLOYEE_ID = 'employee_id';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID          => $this->id,
            self::EMPLOYEE_ID => $this->employee_id,
        ];
    }
}
