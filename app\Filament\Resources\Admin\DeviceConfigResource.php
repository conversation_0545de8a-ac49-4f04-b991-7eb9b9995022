<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Models\Customer\Time\DeviceConfig;
use App\Filament\Resources\Admin\DeviceConfigResource\Pages;
use App\Filament\Resources\Admin\DeviceConfigResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;

class DeviceConfigResource extends Can
{
    protected static ?string $model = DeviceConfig::class;

    protected static ?string $navigationIcon = 'heroicon-o-finger-print';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Төхөөрөмж';
    protected static ?string $modelLabel = 'төхөөрөмж';
    protected static ?int $navigationSort = 7;
    protected static ?string $navigationGroup = 'Цаг';
    protected static ?string $slug = 'DeviceConfigs';

    public static function form(Form $form): Form
    {
        return $form
                ->schema([
                    Forms\Components\Section::make()
                        ->schema([
                            Forms\Components\TextInput::make(DeviceConfig::NAME)
                                ->label('Нэр')
                                ->maxLength(50)
                                ->required(),

                            Forms\Components\TextInput::make(DeviceConfig::SERIAL_NUMBER)
                                ->label('IP хаяг')
                                ->ipv4()
                                ->default('***********')
                                ->placeholder('***********')
                                ->required(),

                            Forms\Components\Hidden::make(DeviceConfig::CREATED_BY)
                                ->default(auth()->user()->id)
                                ->disabled(fn (?DeviceConfig $record) => $record != null),
                        ])
                        ->columns(1)
                        ->columnSpan(['lg' => fn (?DeviceConfig $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (DeviceConfig $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (DeviceConfig $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?DeviceConfig $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(DeviceConfig::NAME)->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(DeviceConfig::SERIAL_NUMBER)->label('IP хаяг')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(DeviceConfig::LAST_DOWNLOAD_DATE)->label('Сүүлд татсан огноо')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDeviceConfigs::route('/'),
            'create' => Pages\CreateDeviceConfig::route('/create'),
            'edit' => Pages\EditDeviceConfig::route('/{record}/edit'),
        ];
    }
}
