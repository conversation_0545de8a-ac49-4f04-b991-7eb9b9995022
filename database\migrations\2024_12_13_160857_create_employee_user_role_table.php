<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employee_user_role', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('employee_user_id');
            $table->unsignedBigInteger('employee_role_id');
            $table->timestamps();

            $table->foreign('employee_user_id')->references('id')->on('employee_users')->onDelete('cascade');
            $table->foreign('employee_role_id')->references('id')->on('employee_roles')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employee_user_role');
    }
};
