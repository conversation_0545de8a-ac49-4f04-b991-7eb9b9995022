<?php

namespace App\Services\Time;

use App\Models\Customer\Time\RequestConfig\RequestConfig;
use App\Models\Customer\Time\RequestConfig\RequestConfigDtl;
use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Builder;

class RequestConfigService
{
    public function getRequestConfigs() {
        $user = auth()->user();
        $cn   = ConnectionService::getCNForEmployeeUser();
        $requestConfigs = RequestConfig::on($cn)->whereHas(RequestConfig::RELATION_REQUEST_CONFIG_DTLS, function (Builder $query) use ($user) { 
            $query->where(RequestConfigDtl::EMPLOYEE_USER_ID, $user->id); 
        })->get();
        return $requestConfigs;
    }
}
