<?php

namespace App\Models\Customer\Voucher;

use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeVoucherSpentDtl extends Model
{
    use HasFactory;

    const ID                        = 'id';
    const EMPLOYEE_VOUCHER_SPENT_ID = 'employee_voucher_spent_id';
    const EMPLOYEE_VOUCHER_ID       = 'employee_voucher_id';
    const AMOUNT                    = 'amount';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::EMPLOYEE_VOUCHER_SPENT_ID,
        self::EMPLOYEE_VOUCHER_ID,
        self::AMOUNT,
    ];

    public function employee_voucher_spent()
    {
        return $this->belongsTo(EmployeeVoucherSpent::class);
    }

    public function employee_Voucher()
    {
        return $this->belongsTo(EmployeeVoucher::class);
    }
}
