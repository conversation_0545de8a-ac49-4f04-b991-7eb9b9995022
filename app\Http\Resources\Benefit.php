<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class Benefit extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'               => $this->id,
            'benefit_group_id' => $this->benefit_group_id,
            'name'             => $this->name,
            'description'      => $this->description,
            'emoji'            => $this->emoji,
            'image_url'        => $this->image_url ?Storage::disk('minio')->temporaryUrl(

                                    $this->image_url, // зурагны path
                                    now()->addMinutes(10) // Хугацааг тохируулж болно,
                                ):null,
        ];
    }
}
