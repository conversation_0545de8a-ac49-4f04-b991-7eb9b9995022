<?php

namespace App\Service;

use App\Models\Customer\GroupEmployee;
use App\Models\Customer\GroupEmployeeDtl;

use Illuminate\Database\Eloquent\Builder;

class GroupEmployeeService
{
    /**
     * БҮЛЭГИЙН НЭРЭЭР БАГЦЛАГДСАН АЖИЛТАНУУДЫН КОДУУУДЫГ АВАХ
     */
    public function getGroupEmployeeIds($connectionName, $id) {
        $groupEmployeeDtl = new GroupEmployeeDtl();
        $groupEmployeeDtl->setConnection($connectionName);
        $employeeIds = $groupEmployeeDtl->select(GroupEmployeeDtl::EMPLOYEE_ID)
                                        ->whereHas(GroupEmployeeDtl::RELATION_GROUP_EMPLOYEE, function (Builder $query) use ($id) {
                                            $query->where(GroupEmployee::ID, $id);
                                        })->get();

        return $employeeIds;
    }
}
