<?php

namespace App\Filament\Resources\Admin\CashRequestConfigResource\Pages;

use App\Filament\Resources\Admin\CashRequestConfigResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCashRequestConfig extends EditRecord
{
    protected static string $resource = CashRequestConfigResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
