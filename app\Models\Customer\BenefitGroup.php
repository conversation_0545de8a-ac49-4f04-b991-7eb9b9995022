<?php

namespace App\Models\Customer;

use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class BenefitGroup extends Model
{
    use HasFactory;

    const NAME        = 'name';
    const DESCRIPTION = 'description';
    const EMOJI       = 'emoji';
    const IMAGE_URL   = 'image_url';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::NAME,
        self::DESCRIPTION,
        self::EMOJI,
        self::IMAGE_URL
    ];

    public function benefits()
    {
        return $this->hasMany(Benefit::class);
    }

    public function getImagePreviewUrlAttribute()
    {
        if (!$this->image_url) return null;
        return Storage::disk('minio')->temporaryUrl(
            $this->image_url,
            now()->addMinutes(10)
        );
    }
}
