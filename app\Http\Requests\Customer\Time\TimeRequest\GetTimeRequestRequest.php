<?php

namespace App\Http\Requests\Customer\Time\TimeRequest;

use App\Models\Customer\Time\TimeRequest\TimeRequest;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetTimeRequestRequest extends FormRequest
{
    const LIMIT                  = 'limit';
    const CONFIRM_STATUS         = 'confirm_status';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'limit'          => 'nullable|integer',
            /**
             * 1: Илгээсэн, 2: Цуцлагдсан, 3: Батлагдсан
             */
            'confirm_status' => ['nullable', Rule::in(TimeRequest::ALL_CONFIRM_STATUS)],
        ];
    }
}
