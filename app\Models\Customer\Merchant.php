<?php

namespace App\Models\Customer;

use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Merchant extends Model
{
    use HasFactory;
    
    const ID               = 'id';
    const NAME             = 'name';
    const LOCATION_NAME    = 'location_name';

    const EMPLOYEE_USERS    = 'employee_users';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::ID,
        self::NAME,
        self::LOCATION_NAME,
    ];

    protected $casts = [
        'employee_users' => 'array',
    ];
}
