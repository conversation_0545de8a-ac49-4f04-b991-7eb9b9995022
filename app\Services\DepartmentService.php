<?php

namespace App\Service;

use App\Models\Customer\Department;

use Illuminate\Database\Eloquent\Builder;

class DepartmentService
{
    public $userDepartmentService;
    public function __construct(UserDepartmentService $userDepartmentService) {
        $this->userDepartmentService = $userDepartmentService;
    }

    public function getDepartmentsByUser($connectionName, $user) {
        $isOrganizationRole = $this->userDepartmentService->isOrganizationRole($user);

        $departments = new Department();
        $departments = $departments->on($connectionName);
        if ($isOrganizationRole) {
            $departmentIds = $this->userDepartmentService->getDepartmentIdsByUserId($user->id);
            if (!empty($departmentIds)) {
                $departments = $departments->whereIn(Department::ID, $departmentIds);
            }
        }
        $departments = $departments->get();
        return $departments;
    }

    public function getDepartmentsHasPositionByUser($connectionName, $user) {
        $isOrganizationRole = $this->userDepartmentService->isOrganizationRole($user);

        $departments = new Department();
        $departments = $departments->on($connectionName)->select('departments.id', 'departments.name');
        // ->join('positions', 'departments.id', '=', 'positions.department_id');

        if (!$isOrganizationRole) {
            $departmentIds = $this->userDepartmentService->getDepartmentIdsByUserId($user->id);
            if (!empty($departmentIds)) {
                $departments = $departments->whereIn('departments.id', $departmentIds);
            }
        }
        $departments = $departments->groupBy('departments.id', 'departments.name')->get();
        return $departments;
    }
}
