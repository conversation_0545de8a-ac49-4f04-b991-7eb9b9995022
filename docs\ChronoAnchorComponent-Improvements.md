# ChronoAnchorComponent Improvements

## Overview
The ChronoAnchorComponent has been enhanced to support selecting anchor dates from any datetime field from Customer models plus a custom date picker option.

## New Features

### 1. Date Source Selection
- Added a new `Date Source` dropdown that allows users to choose between:
  - **Custom Date**: Manual date picker
  - **Customer Model DateTime Fields**: Pre-defined datetime fields from various Customer models
  - **Now**: Current date/time

### 2. Standardized Formula Format
- Uses the new `anchor,date,type,shift` format exclusively
- Removed old format parsing for cleaner, more maintainable code

### 2. Available Customer Model DateTime Fields

#### Employee Model
- Employee Created Date (`employee.created_at`)
- Employee Updated Date (`employee.updated_at`)

#### Employee Detail Model
- Employee Company Work Date (`employee_dtl.company_work_date`)
- Employee Expired Date (`employee_dtl.expired_at`)
- Employee Detail Created Date (`employee_dtl.created_at`)
- Employee Detail Updated Date (`employee_dtl.updated_at`)

#### Time Request Model
- Time Request Begin Date (`time_request.begin_date`)
- Time Request End Date (`time_request.end_date`)
- Time Request Created Date (`time_request.created_at`)
- Time Request Updated Date (`time_request.updated_at`)

#### Time Schedule Model
- Time Schedule Begin Date (`time_schedule.begin_date`)
- Time Schedule End Date (`time_schedule.end_date`)
- Time Schedule Created Date (`time_schedule.created_at`)
- Time Schedule Updated Date (`time_schedule.updated_at`)

#### Holiday Model
- Holiday Begin Date (`holiday.begin_date`)
- Holiday End Date (`holiday.end_date`)
- Holiday Created Date (`holiday.created_at`)
- Holiday Updated Date (`holiday.updated_at`)

#### Employee Attendance Model
- Employee Attendance Date (`employee_attendance.att_date`)
- Employee Attendance Created Date (`employee_attendance.created_at`)
- Employee Attendance Updated Date (`employee_attendance.updated_at`)

#### Raw Attendance Model
- Raw Attendance Register Date (`raw_attendance.register_date`)
- Raw Attendance Created Date (`raw_attendance.created_at`)
- Raw Attendance Updated Date (`raw_attendance.updated_at`)

### 3. Dynamic UI Behavior
- **Custom Date Picker**: Only visible when "Custom Date" is selected as the date source
- **Automatic Field Clearing**: When switching from custom date to a model field, the custom date field is automatically cleared
- **Reactive Updates**: Formula is automatically updated when any field changes

## Technical Changes

### Component Structure
- **Before**: 3 columns (Type, Shift Count, Date)
- **After**: 4 columns (Type, Shift Count, Date Source, Custom Date)

### New Fields
1. `{name}_date_source`: Select dropdown for choosing date source
2. `{name}_custom_date`: Date picker (only visible for custom dates)

### Formula Generation
- **Custom Date**: Uses the selected date value directly
- **Model Fields**: Uses the field path (e.g., `employee.created_at`) in the formula

## Usage Example

```php
// In a Filament form
ChronoAnchorComponent::make('vacation_anchor')
```

This will create a component with:
- Chrono Type dropdown
- Anchor Shift Count input
- Date Source dropdown (defaults to "Custom Date")
- Custom Anchor Date picker (visible only when "Custom Date" is selected)
- Hidden formula field that gets automatically updated

## Backward Compatibility
The component maintains backward compatibility with existing implementations. The default behavior is to show the custom date picker, which matches the original functionality.

## Testing
Unit tests have been added to verify:
- Component creation with default and custom names
- Correct component structure
- Presence of all required form fields

Run tests with:
```bash
php artisan test tests/Feature/ChronoAnchorComponentTest.php
```
