<?php

namespace App\Console\Commands;

use App\Services\Time\EmployeeAttendanceService;

use Illuminate\Console\Command;

class AutoEmployeeAttendance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auto:employee-attendance';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $service = resolve(EmployeeAttendanceService::class);
        $service->prepareEmployeeAttendancesForAllAdminUsers();
    }
}
