<?php

namespace App\Models\Customer\Voucher;

use App\Enums\EmployeeVoucherStatusEnum;
use App\Models\Customer\Employee;
use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeVoucher extends Model
{
    use HasFactory;

    const ID                  = 'id';
    const EMPLOYEE_ID         = 'employee_id';
    const CODE                = 'code';
    const AMOUNT              = 'amount';
    const USED_AMOUNT         = 'used_amount';
    const UNUSED_AMOUNT       = 'unused_amount';
    const STATUS              = 'status';    
    const CREATED_AT          = 'created_at';
    const IS_ACTIVE           = 'is_active';

    const VOUCHER_TOHIRGOO_ID = 'voucher_tohirgoo_id'; // hereggui bolson

    const RELATION_EMPLOYEE         = 'employee';
    const RELATION_VOUCHER_TOHIRGOO = 'voucher_tohirgoo';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::EMPLOYEE_ID,
        // self::VOUCHER_TOHIRGOO_ID,
        self::CODE,
        self::AMOUNT,
        self::USED_AMOUNT,
        self::UNUSED_AMOUNT,
        self::STATUS,
        self::IS_ACTIVE
    ];

    protected $casts = [
        self::STATUS        => EmployeeVoucherStatusEnum::class,
        self::IS_ACTIVE     => 'boolean',
        self::USED_AMOUNT   => 'float',
        self::UNUSED_AMOUNT => 'float'
    ];

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    // public function voucher_tohirgoo()
    // {
    //     return $this->belongsTo(VoucherTohirgoo::class);
    // }
}
