<?php

namespace App\Filament\Resources\Admin\OtherTimeScheduleResource\Pages;

use App\Filament\Resources\Admin\OtherTimeScheduleResource;
use App\Models\Customer\Time\Schedule\OtherTimeSchedule\OtherTimeSchedule;
use App\Models\Customer\Time\Schedule\TimeScheduleEmployee;
use Filament\Resources\Pages\CreateRecord;

class CreateOtherTimeSchedule extends CreateRecord
{
    protected static string $resource = OtherTimeScheduleResource::class;

    protected function afterCreate(): void
    {
        $otherTimeSchedule = $this->record;
        $otherTimeSchedule->updated_at = null;
        $otherTimeSchedule->getHashStr();
        $otherTimeSchedule->save();
        $newTimeScheduleEmployees = [];
        foreach ($this->data[OtherTimeSchedule::RELATION_TIME_SCHEDULE_EMPLOYEES] as $key => $value) {
            $value[TimeScheduleEmployee::END_DATE] = $value[TimeScheduleEmployee::HAS_END_DATE] ? $value[TimeScheduleEmployee::END_DATE] : null;
            $newTimeScheduleEmployees[] = new TimeScheduleEmployee($value);
        }
        $otherTimeSchedule->time_schedule_employees()->saveMany($newTimeScheduleEmployees);

        $otherTimeScheduleDtls = $otherTimeSchedule->getDefaultOtherTimeScheduleDtls();
        foreach ($otherTimeScheduleDtls as $newTimeScheduleDtl) {
            $newTimeScheduleDtlBts = $newTimeScheduleDtl->getDefaultOtherTimeScheduleDtlBts();
            $newTimeScheduleDtl->created_by = auth()->user()->id;
            $otherTimeSchedule->time_schedule_dtls()->save($newTimeScheduleDtl);
            $newTimeScheduleDtl->time_schedule_dtl_bts()->saveMany($newTimeScheduleDtlBts);
        }
    }
}
