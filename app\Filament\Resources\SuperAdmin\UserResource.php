<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Models\Constant\ConstData;
use App\Models\User;
use App\Models\Role;

use App\Filament\Resources\SuperAdmin\UserResource\Pages;
use App\Filament\Resources\SuperAdmin\UserResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Хэрэглэгч';
    protected static ?string $modelLabel = 'хэрэглэгч';
    protected static ?int $navigationSort = 2;
    protected static ?string $slug = 'users';

    public static function form(Form $form): Form
    {
        return $form
                ->schema([
                    Forms\Components\Section::make()
                        ->schema([
                            Forms\Components\TextInput::make(User::NAME)
                                ->label('Нэр')
                                ->maxLength(50)
                                ->unique(ignoreRecord: true)
                                ->required(),
                            Forms\Components\TextInput::make(User::PHONE)
                                ->label('Утас')
                                ->maxLength(50)
                                ->unique(ignoreRecord: true)
                                ->required(),
                            Forms\Components\TextInput::make(User::EMAIL)
                                ->label('Имэйл')
                                ->maxLength(50)
                                ->unique(ignoreRecord: true)
                                ->required(),
                            Forms\Components\CheckboxList::make('roles')
                                ->relationship('roles', 'name') // Spatie-ийн roles харилцааг холбоно
                                ->options(function () {
                                    return Role::where('name', '!=', ConstData::ROLE_SUPER_ADMIN)->pluck('name', 'id');
                                })
                                ->columns(2)
                                ->bulkToggleable()
                                ->required()
                        ])
                        ->columns(1)
                        ->columnSpan(['lg' => fn (?User $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (User $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (User $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?User $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('phone')->label('Утас')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('email')->label('Имэйл')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                // Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->where('name', '!=', ConstData::ROLE_SUPER_ADMIN);
    }   
}
