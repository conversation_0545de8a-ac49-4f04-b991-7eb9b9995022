<?php

namespace App\Filament\Resources\Admin\EmployeeVoucherAmountResource\Pages;

use App\Filament\Resources\Admin\EmployeeVoucherAmountResource;
use App\Models\Customer\Voucher\EmployeeVoucherAmount;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateEmployeeVoucherAmount extends CreateRecord
{
    protected static string $resource = EmployeeVoucherAmountResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data[EmployeeVoucherAmount::UNUSED_TOTAL_AMOUNT] = 0;
        $data[EmployeeVoucherAmount::USED_TOTAL_AMOUNT]   = 0;
        return $data;
    }
}
