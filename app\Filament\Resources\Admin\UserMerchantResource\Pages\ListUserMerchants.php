<?php

namespace App\Filament\Resources\Admin\UserMerchantResource\Pages;

use App\Filament\Resources\Admin\UserMerchantResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListUserMerchants extends ListRecords
{
    protected static string $resource = UserMerchantResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-o-plus'),
        ];
    }
}
