<?php

namespace App\Filament\Resources\Admin;

use App\Models\Constant\ConstData;
use App\Models\Customer\Department;
use App\Models\Customer\CashRequest\CashRequestConfig;
use App\Filament\Resources\Admin\CashRequestConfigResource\Pages;
use App\Filament\Resources\Admin\CashRequestConfigResource\RelationManagers;
use App\Models\Class\Can;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class CashRequestConfigResource extends Can
{
    protected static ?string $model = CashRequestConfig::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog';
    protected static ?string $navigationLabel, $pluralModelLabel = 'хүсэлтийн тохиргоо';
    protected static ?string $modelLabel = 'хүсэлтийн тохиргоо';
    protected static ?int $navigationSort = 11;
    protected static ?string $navigationGroup = 'Бэлэн мөнгөний хүсэлт';
    protected static ?string $slug = 'cash-request-configs';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make('department_id')
                            ->label('Хэлтэс')
                            ->unique(ignoreRecord: true)
                            ->options(Department::all()->pluck(ConstData::NAME, ConstData::ID))
                            ->searchable()
                            ->required()
                            ->live()
                    ])
                    ->columns(1)
                    ->columnSpan(['lg' => fn (?CashRequestConfig $record) => $record === null ? 3 : 2]),

            Forms\Components\Section::make()
                ->schema([
                    Forms\Components\Placeholder::make('created_at')
                        ->label('Created at')
                        ->content(fn (CashRequestConfig $record): ?string => $record->created_at?->diffForHumans()),

                    Forms\Components\Placeholder::make('updated_at')
                        ->label('Last modified at')
                        ->content(fn (CashRequestConfig $record): ?string => $record->updated_at?->diffForHumans()),
                ])
                ->columnSpan(['lg' => 1])
                ->hidden(fn (?CashRequestConfig $record) => $record === null),
        ])
        ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('department.name')->label('Хэлтсийн нэр')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\CashRequestConfigDtlsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCashRequestConfigs::route('/'),
            'create' => Pages\CreateCashRequestConfig::route('/create'),
            'edit' => Pages\EditCashRequestConfig::route('/{record}/edit'),
        ];
    }
}
