<?php

namespace App\Filament\Resources\Admin\PositionResource\Pages;

use App\Filament\Resources\Admin\PositionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPositions extends ListRecords
{
    protected static string $resource = PositionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-o-plus'),
        ];
    }
}
