<?php

namespace App\Models\Constant;

class ConstData
{
    const STATUS                      = 'status';
    const ERRORS                      = 'errors';
    const MESSAGE                     = 'message';
    const VALUE                       = 'value';
    const TOKEN                       = 'token';
    const ATTACHMENT                  = 'attachment';

    const STATUS_SUCCESS              = 'success';
    const STATUS_PROCESSING           = 'processing';
    const STATUS_ERROR                = 'error';

    const ADMIN_DB                    = 'astratime';
    const AUTH_EXCEPTION              = 'Auth_Exception: ';
    const INFO_EXCEPTION              = 'Info_Exception: ';
    const TIME_EXCEPTION              = 'Time_Exception: ';
    const CONFIG_EXCEPTION            = 'Config_Exception: ';
    const DECISION_EXCEPTION          = 'Decision_Exception: ';
    const EMPLOYEE_EXCEPTION          = 'Employee_Exception: ';
    const SYSTEM_EXCEPTION            = 'System_Exception: ';

    const ROLE_SUPER_ADMIN            = 'superadmin';
    const ROLE_ADMIN                  = 'admin';
    const ROLE_VOUCHERSPENT           = 'voucherspent';

    const EMPLOYEE_ROLE_EMPLOYEE      = 'employee';
    const EMPLOYEE_ROLE_MERCHANT      = 'merchant';
    const EMPLOYEE_ROLE_APPROVER      = 'approver';

    const QC_SYNC                     = 'sync';
    const QC_DATABASE_LONG_RUNNING    = 'database-long-running';
    const QC_DATABASE                 = 'database';
    const QC_REDIS                    = 'redis';

    const ID                          = 'id';
    const NAME                        = 'name';

    const NIGHT_TIME = 'night_time';

    const BAIHGUI = 'Байхгүй';

    const PRODUCTION                   = 'production';
    const LOCAL                        = 'local';

    const BIOTIME_HOST                 = 'biotime_host';
    const BIOTIME_PORT                 = 'biotime_port';
    const DEFAULT_BIOTIME_PORT         = 4370;

    const FIREBASE_CREDENTIALS = 'FIREBASE_CREDENTIALS';

    const PERMISSION_VIEW   = 'view';
    const PERMISSION_CREATE = 'create';
    const PERMISSION_EDIT   = 'edit';
    const PERMISSION_DELETE = 'delete';

    const MINIO_ACCESS_KEY_ID           = 'MINIO_ACCESS_KEY_ID';
    const MINIO_SECRET_ACCESS_KEY       = 'MINIO_SECRET_ACCESS_KEY';
    const MINIO_DEFAULT_REGION          = 'MINIO_DEFAULT_REGION';
    const MINIO_BUCKET                  = 'MINIO_BUCKET';
    const MINIO_URL                     = 'MINIO_URL';
    const MINIO_ENDPOINT                = 'MINIO_ENDPOINT';
    const MINIO_USE_PATH_STYLE_ENDPOINT = 'MINIO_USE_PATH_STYLE_ENDPOINT';

    static public function successMSG($value = null) {
        return response()->json([
            self::STATUS  => self::STATUS_SUCCESS,
            self::MESSAGE => 'Амжилттай',
            self::VALUE   => $value,
        ], 200);
    }
}
