<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ApprovalConfigHdr extends JsonResource
{
    const ID                          = 'id';
    const NAME                        = 'name';
    const APRROVAL_CONFIG_DTLS        = 'approval_config_dtls';
    const APRROVAL_CONFIG_SUB_DTLS    = 'approval_config_sub_dtls';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                          => $this->id,
            self::NAME                        => $this->name,
            self::APRROVAL_CONFIG_DTLS        => ApprovalConfigDtl::collection($this->approval_config_dtls),
            self::APRROVAL_CONFIG_SUB_DTLS    => ApprovalConfigSubDtl::collection($this->approval_config_sub_dtls),
        ];
    }
}
