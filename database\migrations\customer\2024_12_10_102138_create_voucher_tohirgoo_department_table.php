<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('voucher_tohirgoo_department'))
            return;
        Schema::create('voucher_tohirgoo_department', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('voucher_tohirgoo_id');
            $table->unsignedBigInteger('department_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('voucher_tohirgoo_department');
    }
};
