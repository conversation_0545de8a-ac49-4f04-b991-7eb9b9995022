<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Models\Role;
use App\Models\RoleResource as RoleResourceModel;
use App\Helpers\FilamentHelper;
use App\Filament\Resources\SuperAdmin\RoleResource\Pages;
use App\Filament\Resources\SuperAdmin\RoleResource\RelationManagers;
use App\Models\Constant\ConstData;
use Filament\Resources\Resource;
use Filament\Forms;
use Filament\Tables;

class RoleResource extends Resource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon  = 'heroicon-o-shield-check';
    protected static ?string $navigationGroup = 'Access Control';
    protected static ?string $slug            = 'roles';
    protected static ?int $navigationSort     = 1;

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->unique(ignoreRecord: true),

                Forms\Components\Select::make('permissions')
                    ->relationship('permissions', 'name')
                    ->multiple()
                    ->preload()
                    ->searchable(),

                Forms\Components\CheckboxList::make('resources')
                    ->options(fn () => FilamentHelper::getFilamentResources())
                    ->afterStateHydrated(fn ($component, $state, $record) => 
                        $component->state($record?->resources->pluck('resource')->toArray() ?? []) // Хуучин resource-уудыг ачаалах
                    )
                    ->afterStateUpdated(fn ($state, $record) => $record?->resources()->delete()) // Шинэчлэлт хийх үед хуучин өгөгдлийг устгах
                    ->dehydrated(false)
                    ->bulkToggleable()
                    ->saveRelationshipsUsing(function ($state, $record) {
                        if ($record) {
                            // Хуучин тохиргоог устгах
                            $record->resources()->delete();
                
                            // Шинэ сонголтуудыг хадгалах
                            foreach ($state as $resource) {
                                RoleResourceModel::create([
                                    'role_id'  => $record->id,
                                    'resource' => $resource,
                                ]);
                            }
                        }
                    })
                    ->required(),
            ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('permissions.name')->label('Permissions')->badge(),
            ])
            ->filters([])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }


    public static function getEloquentQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return parent::getEloquentQuery()->where('name', '!=', ConstData::ROLE_SUPER_ADMIN);
    }
}

