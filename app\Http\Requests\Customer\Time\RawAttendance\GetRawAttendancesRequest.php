<?php

namespace App\Http\Requests\Customer\Time\RawAttendance;

use Illuminate\Foundation\Http\FormRequest;

class GetRawAttendancesRequest extends FormRequest
{
    const LIMIT                  = 'limit';
    const FILTER                 = 'filter';
    const FILTER_BEGIN_DATE      = 'begin_date';
    const FILTER_END_DATE        = 'end_date';
    const SORT                   = 'sort';
    const SORT_FIELD             = 'field';
    const SORT_TYPE              = 'type';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::FILTER_BEGIN_DATE => 'nullable|date',
            self::FILTER_END_DATE   => 'nullable|date|after_or_equal:begin_date',
        ];
    }
}
