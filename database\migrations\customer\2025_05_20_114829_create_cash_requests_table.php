<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cash_requests', function (Blueprint $table) {
            $table->id();
            $table->string('department_name');
            $table->foreignId('employee_id')->constrained();
            $table->string('employee_last_name');
            $table->string('employee_first_name');
            $table->boolean('is_company')->default(0);
            $table->string('receiver_name');
            $table->longText('transaction_description');
            $table->string('bank_name');
            $table->string('iban_number')->nullable();
            $table->string('account_number')->nullable();
            $table->unsignedDecimal('amount', $precision = 24, $scale = 2);
            $table->enum('status', ['pending', 'temp_approved', 'approved', 'rejected'])->default('pending');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cash_requests');
    }
};
