<?php

namespace App\Filament\Resources\SuperAdmin\OrganizationResource\Pages;

use App\Models\Organization;
use App\Filament\Resources\SuperAdmin\OrganizationResource;
use App\Services\OrganizationService;
use Filament\Resources\Pages\CreateRecord;

class CreateOrganization extends CreateRecord
{
    protected static string $resource = OrganizationResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $user           = auth()->user();
        $service        = resolve(OrganizationService::class);
        $dataBaseConfig = $service->createDataBaseConfig($data[Organization::NAME], $user->id);
        $data[Organization::DATABASE_CONFIG_ID] = $dataBaseConfig->id;
        $data[Organization::CREATED_BY]         = $user->id;
        return $data;
    }
}
