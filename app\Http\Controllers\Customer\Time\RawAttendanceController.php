<?php

namespace App\Http\Controllers\Customer\Time;

use App\Models\Customer\Employee;
use App\Models\Customer\Time\Attendance\RawAttendance;
use App\Http\Requests\Customer\Time\RawAttendance\GetRawAttendancesRequest;
use App\Http\Resources\RawAttendance as RawAttendanceResource;
use App\Http\Controllers\Controller;
use App\Services\ConnectionService;

class RawAttendanceController extends Controller
{   
    public function getCN() {
        $service = resolve(ConnectionService::class);
        return $service->getCNForEmployeeUser();
    }

    /**
     * Системд бүртгэгдсэн ирцийн мэдээлэлүүдийг жагсаалтаар авах.
     */
    public function index(GetRawAttendancesRequest $request)
    {
        $limit         = $request->input(GetRawAttendancesRequest::LIMIT, 10);
        $filters       = $request->input(GetRawAttendancesRequest::FILTER);

        $cn            = $this->getCN();
        $employee = Employee::on($cn)->where(Employee::PHONE, auth()->user()->phone)->first();
        $attendanceCode = $employee->employee_dtl->attendance_code;

        if (!isset($attendanceCode))
            return RawAttendanceResource::collection([]);

        $rawAttendances = RawAttendance::on($cn)->where(RawAttendance::ATTENDANCE_CODE, $attendanceCode);
        if (isset($filters[GetRawAttendancesRequest::FILTER_BEGIN_DATE])) {
            $beginDate     = $filters[GetRawAttendancesRequest::FILTER_BEGIN_DATE];
            $rawAttendances = $rawAttendances->where(RawAttendance::REGISTER_DATE, '>=', $beginDate);
        }
        if (isset($filters[GetRawAttendancesRequest::FILTER_END_DATE])) {
            $endDate       = $filters[GetRawAttendancesRequest::FILTER_END_DATE];
            $rawAttendances = $rawAttendances->where(RawAttendance::REGISTER_DATE, '<=', $endDate);
        }
        $rawAttendances = $rawAttendances->paginate($limit);
        return RawAttendanceResource::collection($rawAttendances);
    }
}
