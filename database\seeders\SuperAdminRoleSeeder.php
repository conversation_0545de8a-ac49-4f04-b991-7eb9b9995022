<?php

namespace Database\Seeders;

use App\Models\Constant\ConstData;
use App\Models\User;
use App\Models\Role;
use Illuminate\Database\Seeder;

class SuperAdminRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if (Role::where('name', ConstData::ROLE_SUPER_ADMIN)->exists())
            return;
        Role::create([
            'name'       => ConstData::ROLE_SUPER_ADMIN,
            'guard_name' => 'web'
        ]);
        $sa = User::where('name', ConstData::ROLE_SUPER_ADMIN)->first();
        $sa->assignRole(ConstData::ROLE_SUPER_ADMIN);
    }
}
