<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('super_salary_period_sub_one_dtls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('super_salary_period_dtl_id')->constrained('super_salary_period_dtls')->onDelete('cascade')->name('fk_ss_period_dtl_id');
            $table->unsignedBigInteger('department_id');
            $table->timestamps();

            $table->index('super_salary_period_dtl_id')->name('index_ss_period_dtl_id');
            $table->index('department_id')->name('index_department_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('super_salary_period_sub_one_dtls');
    }
};
