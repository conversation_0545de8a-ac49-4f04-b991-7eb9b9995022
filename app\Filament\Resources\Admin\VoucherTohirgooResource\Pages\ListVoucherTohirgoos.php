<?php

namespace App\Filament\Resources\Admin\VoucherTohirgooResource\Pages;

use App\Filament\Resources\Admin\VoucherTohirgooResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListVoucherTohirgoos extends ListRecords
{
    protected static string $resource = VoucherTohirgooResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-o-plus'),
        ];
    }
}
