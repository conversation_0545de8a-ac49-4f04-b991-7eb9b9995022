<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmployeeAttendanceDtlsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('employee_attendance_dtls'))
            return;
        Schema::create('employee_attendance_dtls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_attendance_id')->constrained();
            $table->dateTime('begin_date');
            $table->dateTime('end_date');
            $table->string('begin_sign');
            $table->string('end_sign');
            $table->integer('type');
            $table->integer('attendance_status')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employee_attendance_dtls');
    }
}
