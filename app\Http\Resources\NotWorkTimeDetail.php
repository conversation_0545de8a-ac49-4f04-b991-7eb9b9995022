<?php

namespace App\Http\Resources;

use App\Http\Tools\DateTool;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NotWorkTimeDetail extends JsonResource
{
    const ID                  = 'id';
    const TOTAL_NOT_WORK_TIME = 'total_not_work_time';
    const ATT_DATE            = 'att_date';
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                  => $this->id,
            'total_not_work_time' => DateTool::convertHourAndMin($this->total_not_work_time),
            'att_date'            => $this->att_date
        ];
    }
}
