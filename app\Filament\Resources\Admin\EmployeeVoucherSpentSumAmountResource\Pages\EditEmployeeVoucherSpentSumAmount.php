<?php

namespace App\Filament\Resources\Admin\EmployeeVoucherSpentSumAmountResource\Pages;

use App\Filament\Resources\Admin\EmployeeVoucherSpentSumAmountResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditEmployeeVoucherSpentSumAmount extends EditRecord
{
    protected static string $resource = EmployeeVoucherSpentSumAmountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
