<?php

namespace App\Filament\Components;

use Filament\Forms;

class ChronoRangeComponent
{
    public static function make(string $name = 'chrono_range'): Forms\Components\Group
    {
        return Forms\Components\Group::make([
            Forms\Components\Select::make("{$name}_chrono_unit")
                ->label('Chrono Unit')
                ->options([
                    'minute' => 'Minute',
                    'hour' => 'Hour',
                    'halfday' => 'Half Day',
                    'day' => 'Day',
                    'month' => 'Month',
                    'quarter' => 'Quarter',
                    'halfyear' => 'Half Year',
                    'year' => 'Year'
                ])
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\TextInput::make("{$name}_from")
                ->label('From')
                ->numeric()
                ->minValue(0)
                ->rules(['integer', 'min:0'])
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\TextInput::make("{$name}_to")
                ->label('To')
                ->numeric()
                ->minValue(0)
                ->rules(['integer', 'min:0'])
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\Hidden::make("{$name}_formula")
        ])->columns(3);
    }

    private static function updateFormula(string $name, callable $set, callable $get): void
    {
        $chronoUnit = $get("{$name}_chrono_unit");
        $from = $get("{$name}_from");
        $to = $get("{$name}_to");

        if ($chronoUnit && $from !== null && $from !== '' && $to !== null && $to !== '') {
            // New format: range,chrono_unit,from,to
            $formula = "range,{$chronoUnit},{$from},{$to}";
            $set("{$name}_formula", $formula);
        }
    }

    /**
     * Parse a formula string back into component values
     * Formula format: "range,chrono_unit,from,to"
     */
    public static function parseFormula(string $formula): array
    {
        $result = [
            'chrono_unit' => null,
            'from' => null,
            'to' => null,
        ];

        // Split by comma and check if it's the new format
        $parts = explode(',', $formula);

        if (count($parts) === 4 && $parts[0] === 'range') {
            $chronoUnit = $parts[1];
            $from = (int)$parts[2];
            $to = (int)$parts[3];

            $result['chrono_unit'] = $chronoUnit;
            $result['from'] = $from;
            $result['to'] = $to;
        }

        return $result;
    }
}