<?php

use App\Models\Customer\Approval\ApprovalConfigDtl;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateApprovalConfigDtlsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('approval_config_dtls'))
            return;
        Schema::create('approval_config_dtls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('approval_config_hdr_id')->constrained('approval_config_hdrs')->onDelete('cascade');
            $table->unsignedBigInteger('employee_id')->unique();
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('approval_config_dtls');
    }
}
