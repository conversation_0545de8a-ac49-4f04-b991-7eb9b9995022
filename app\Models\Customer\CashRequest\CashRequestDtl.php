<?php

namespace App\Models\Customer\CashRequest;

use App\Enums\CashRequestDtlStatusEnum;
use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CashRequestDtl extends Model
{
    use HasFactory;

    const CASH_REQUEST_ID  = 'cash_request_id';
    const EMPLOYEE_USER_ID = 'employee_user_id';
    const DESCRIPTION      = 'description';
    const STATUS           = 'status';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::CASH_REQUEST_ID,
        self::EMPLOYEE_USER_ID,
        self::DESCRIPTION,
        self::STATUS,
    ];

    protected $casts = [
        self::STATUS => CashRequestDtlStatusEnum::class
    ];

    public function cash_request()
    {
        return $this->belongsTo(CashRequest::class);
    }
}
