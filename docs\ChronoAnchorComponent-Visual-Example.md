# ChronoAnchorComponent Visual Layout

## Before (Original Component)
```
┌─────────────────┬─────────────────┬─────────────────┐
│   Chrono Type   │ Anchor Shift    │  Anchor Date    │
│                 │     Count       │                 │
│ [Dropdown]      │ [Number Input]  │ [Date Picker]   │
│ - Minute        │                 │                 │
│ - Hour          │                 │                 │
│ - Day           │                 │                 │
│ - Month         │                 │                 │
│ - etc...        │                 │                 │
└─────────────────┴─────────────────┴─────────────────┘
```

## After (Improved Component)
```
┌─────────────────┬─────────────────┬─────────────────┬─────────────────┐
│   Chrono Type   │ Anchor Shift    │   Date Source   │ Custom Anchor   │
│                 │     Count       │                 │     Date        │
│ [Dropdown]      │ [Number Input]  │ [Dropdown]      │ [Date Picker]   │
│ - Minute        │                 │ - Custom Date   │ (Only visible   │
│ - Hour          │                 │ - Employee      │  when Custom    │
│ - Day           │                 │   Created Date  │  Date selected) │
│ - Month         │                 │ - Employee      │                 │
│ - etc...        │                 │   Work Date     │                 │
│                 │                 │ - Time Request  │                 │
│                 │                 │   Begin Date    │                 │
│                 │                 │ - Holiday Date  │                 │
│                 │                 │ - etc...        │                 │
└─────────────────┴─────────────────┴─────────────────┴─────────────────┘
```

## User Experience Flow

### Scenario 1: Using Custom Date (Default)
1. User selects "Custom Date" from Date Source dropdown (default selection)
2. Custom Anchor Date picker becomes visible
3. User selects a specific date
4. Formula is generated: `2024-01-15+day*5` (example)

### Scenario 2: Using Employee Model Field
1. User selects "Employee Created Date" from Date Source dropdown
2. Custom Anchor Date picker becomes hidden
3. Formula is generated: `employee.created_at+month*2` (example)

### Scenario 3: Using Time Request Field
1. User selects "Time Request Begin Date" from Date Source dropdown
2. Custom Anchor Date picker becomes hidden
3. Formula is generated: `time_request.begin_date+day*-7` (example)

## Formula Examples

### Custom Date Examples
- `2024-01-15+day*5` - 5 days after January 15, 2024
- `2024-03-01+month*-2` - 2 months before March 1, 2024
- `2024-12-31+year*1` - 1 year after December 31, 2024

### Model Field Examples
- `employee.created_at+day*30` - 30 days after employee creation
- `employee_dtl.company_work_date+month*6` - 6 months after company work date
- `time_request.begin_date+day*-1` - 1 day before time request begins
- `holiday.begin_date+day*0` - Same day as holiday begins
- `raw_attendance.register_date+hour*24` - 24 hours after attendance registration

## Benefits

1. **Flexibility**: Users can anchor dates to any relevant datetime field in the system
2. **Dynamic**: Dates automatically adjust based on the referenced model field values
3. **User-Friendly**: Clear labels make it easy to understand what each option represents
4. **Backward Compatible**: Existing implementations continue to work without changes
5. **Extensible**: Easy to add more datetime fields from other models in the future
