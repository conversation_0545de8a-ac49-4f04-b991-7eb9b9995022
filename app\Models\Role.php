<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Permission\Models\Role as SpatieRole;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\Permission\Models\Permission;

/**
 * @mixin IdeHelperRole
 */
class Role extends SpatieRole
{
    use HasFactory;

    protected $fillable = ['name', 'guard_name'];

    // Role нь Permission-уудтай холбогдох харилцаа
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'role_has_permissions');
    }

    // Role нь Resource-уудтай холбогдох харилцаа
    // public function resources(): BelongsToMany
    // {
    //     return $this->belongsToMany(RoleResource::class, 'role_resource', 'role_id', 'resource_id');
    // }

    public function resources()
    {
        return $this->hasMany(RoleResource::class, 'role_id');
    }

    // public function resources(): BelongsToMany
    // {
    //     return $this->belongsToMany(
    //         RoleResource::class,       // Холбогдож буй загвар
    //         'role_resource',       // Дундын хүснэгтийн нэр
    //         'role_id',             // `role_resource` дэх Role-н ID
    //         'resource'          // `role_resource` дэх Resource-н ID
    //     );
    // }
}

