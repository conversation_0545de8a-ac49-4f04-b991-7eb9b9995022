<?php

namespace App\Http\Requests\Customer\Time\SalaryPeriod;

use Illuminate\Foundation\Http\FormRequest;

class SalaryPeriodRequest extends FormRequest
{
    const PARAMETER_YEAR                                                = 'year';
    const PARAMETER_MONTH                                               = 'month';
    const PARAMETER_DATES                                               = 'dates';
    const PARAMETER_DATES_BEGIN_DATE                                    = 'dates.*.begin_date';
    const PARAMETER_DATES_END_DATE                                      = 'dates.*.end_date';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::PARAMETER_YEAR                                        => 'required|integer|between:2000,3000',
            self::PARAMETER_MONTH                                       => 'required|integer|between:1,12',
            self::PARAMETER_DATES                                       => 'required|array',
            self::PARAMETER_DATES_BEGIN_DATE                            => 'required|date',
            self::PARAMETER_DATES_END_DATE                              => 'required|date|after_or_equal:dates.*.begin_date',
        ];
    }
}
