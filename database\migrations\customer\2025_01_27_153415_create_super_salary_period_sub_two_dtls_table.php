<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('super_salary_period_sub_two_dtls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('super_salary_period_dtl_id')->constrained('super_salary_period_dtls')->onDelete('cascade')->name('fk_ss_period_dtl_id');
            $table->date('begin_date');
            $table->date('end_date');
            $table->timestamps();

            $table->index(['begin_date', 'end_date'], 'index_begin_end_date')->name('index_begin_end_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('super_salary_period_sub_two_dtls');
    }
};
