<?php

namespace App\Filament\Resources\Admin;

use App\Models\Customer\Benefit;
use App\Models\Customer\BenefitGroup;
use App\Filament\Resources\Admin\BenefitResource\Pages;
use App\Models\Class\Can;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Builder;

class BenefitResource extends Can
{
    protected static ?string $model = Benefit::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Урамшуулал';
    protected static ?string $modelLabel = 'Урамшуулал';
    protected static ?int $navigationSort = 11;
    protected static ?string $navigationGroup = 'Урамшуулал';
    protected static ?string $slug = 'benefits';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make(Benefit::BENEFIT_GROUP_ID)
                            ->label('Бүлэг')
                            ->options(BenefitGroup::all()->pluck(BenefitGroup::NAME, 'id')),
                        Forms\Components\TextInput::make(Benefit::NAME)
                            ->label('Нэр')
                            ->maxLength(255)
                            ->required(),
                        Forms\Components\Textarea::make(Benefit::DESCRIPTION)
                            ->label('Тайлбар')
                            ->maxLength(65535),
                        Forms\Components\TextInput::make(Benefit::EMOJI)
                            ->label('Emoji')
                            ->maxLength(255),
                        Forms\Components\FileUpload::make(BenefitGroup::IMAGE_URL)
                            ->label("Зураг оруулна уу!")
                            ->image()
                            ->imageResizeMode('cover')
                            ->acceptedFileTypes(['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml', 'image/webp'])
                            ->preserveFilenames()
                            ->directory(strtolower(config('app.name')) . '/benefits/' . ($record?->id ?? Str::uuid()))
                            ->disk('minio')
                            ->visibility('private')
                            ->maxSize(100000),
                    ])
                    ->columns(1)
                    ->columnSpan(['lg' => fn (?Benefit $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Benefit $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Benefit $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Benefit $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->groups([
                'benefit_group.name',
            ])
            ->defaultGroup('benefit_group.name')
            ->columns([
                Tables\Columns\TextColumn::make('benefit_group.name')->label('Бүлэг')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Benefit::NAME)->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Benefit::DESCRIPTION)->label('Тайлбар')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Benefit::EMOJI)->label('Emoji')->sortable(),
                Tables\Columns\ImageColumn::make('image_preview_url')->label('Зураг')->size(50),

            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBenefits::route('/'),
            'create' => Pages\CreateBenefit::route('/create'),
            'edit' => Pages\EditBenefit::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with('benefit_group');
    }
}
