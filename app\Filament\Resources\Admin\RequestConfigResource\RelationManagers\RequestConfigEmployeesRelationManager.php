<?php

namespace App\Filament\Resources\Admin\RequestConfigResource\RelationManagers;

use App\Models\Customer\Employee;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Livewire\Attributes\On;

class RequestConfigEmployeesRelationManager extends RelationManager
{
    protected static string $relationship = 'request_config_employees';

    protected static ?string $title = 'Хамрагдах ажилтан';

    // // Reactive байдлаар хадгалах public property зарлах
    // public ?int $department_id = null;

    // public function mount(): void
    // {
    //     $this->department_id = $this->getOwnerRecord()?->department_id;
    // }

    // #[On('department_id_updated')]
    // public function refreshRelation($department_id)
    // {
    //     // Reactive property-г шинэчлээд refresh хийнэ.
    //     $this->department_id = $department_id;
    //     $this->dispatch('$refresh');
    // }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('employee_id')
                    ->required()
                    ->options(function (callable $get) {;
                        return Employee::query()
                            ->where(Employee::DEPARTMENT_ID, $this->getOwnerRecord()->department_id)
                            ->get()
                            ->pluck('full_name_with_dp', Employee::ID);
                    })
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->heading('Ажилтанууд')
            ->columns([
                Tables\Columns\TextColumn::make('employee.full_name_with_dp')->label('Хэлтэс, Албан тушаал, Овог, Нэр'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-s-plus'),
            ])
            ->actions([
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
