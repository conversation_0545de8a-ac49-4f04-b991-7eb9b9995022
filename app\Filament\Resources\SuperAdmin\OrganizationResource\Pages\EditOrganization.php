<?php

namespace App\Filament\Resources\SuperAdmin\OrganizationResource\Pages;

use App\Filament\Resources\SuperAdmin\OrganizationResource;
use Filament\Actions;
use Filament\Actions\Modal\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Artisan;

class EditOrganization extends EditRecord
{
    protected static string $resource = OrganizationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\Action::make('Update')->label('Бааз update хийх')
                ->requiresConfirmation()
                ->modalDescription('Та бааз шинэчлэхдээ итгэлтэй байна уу?')
                ->action(function () {
                    try {
                        $db = $this->record->database_config->database;

                        // Run the migration command
                        $exitCode = Artisan::call('migrate', [
                            '--path' => 'database/migrations/customer',
                            '--database' => $db,
                            '--force' => true
                        ]);

                        // Get the command output
                        $output = Artisan::output();

                        if ($exitCode === 0) {
                            // Success notification with migration details
                            Notification::make()
                                ->title('Бааз update амжилттай дууслаа')
                                ->body($output ?: 'Migration амжилттай гүйцэтгэгдлээ.')
                                ->success()
                                ->duration(10000) // Show for 10 seconds
                                ->send();
                        } else {
                            // Error notification
                            Notification::make()
                                ->title('Бааз update амжилтгүй боллоо')
                                ->body($output ?: 'Migration-д алдаа гарлаа.')
                                ->danger()
                                ->duration(15000) // Show for 15 seconds
                                ->send();
                        }
                    } catch (\Exception $e) {
                        // Exception notification
                        Notification::make()
                            ->title('Алдаа гарлаа')
                            ->body('Migration явцад алдаа гарлаа: ' . $e->getMessage())
                            ->danger()
                            ->duration(15000)
                            ->send();
                    }
                }),
        ];
    }
}
