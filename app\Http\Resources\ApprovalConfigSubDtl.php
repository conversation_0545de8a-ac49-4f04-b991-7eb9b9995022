<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class Apartment
 *
 * @mixin \App\Models\Apartment
 */
class ApprovalConfigSubDtl extends JsonResource
{
    const ID          = 'id';
    const USER_ID     = 'user_id';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID        => $this->id,
            self::USER_ID   => $this->user_id,
        ];
    }
}
