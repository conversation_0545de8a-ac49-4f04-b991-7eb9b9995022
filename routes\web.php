<?php

use App\Http\Controllers\PushNotificationController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\File;
use Dedoc\Scramble\Scramble;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/
Route::get('/', fn() => Redirect::to('/admin/login'));

Route::get('/send-notification', [PushNotificationController::class, 'sendPushNotification']);

Route::domain(env('APP_URL'))->group(function () {
    Scramble::registerUiRoute('api');
    Scramble::registerJsonSpecificationRoute('api.json');
});

Route::get('/privacy', function () {
    $path    = resource_path('htmls/privacy.html');
    $content = File::get($path);
    return response($content)->header('Content-Type', 'text/html');
});

