<?php

namespace App\Models\Customer\Time\SalaryPeriod;

use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SalaryPeriod extends Model
{
    use HasFactory;
    const TABLE = 'salary_periods';

    const ID                            = 'id';
    const YEAR                          = 'year';
    const MONTH                         = 'month';
    const CREATED_BY                    = 'created_by';
    const UPDATED_BY                    = 'updated_by';

    const RELATION_SALARY_PERIOD_DTLS = 'salary_period_dtls';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::ID,
        self::YEAR,
        self::MONTH,
        self::CREATED_BY,
        self::UPDATED_BY,
    ];

    public function getNameAttribute()
    {
        return "$this->year $this->month";
    }

    public function salary_period_dtls(): HasMany {
        return $this->hasMany(SalaryPeriodDtl::class)->orderBy(SalaryPeriodDtl::BEGIN_DATE);
    }

    public function getBeginDate() {
        $salaryPeriodDtls = $this->salary_period_dtls;
        if (!isset($salaryPeriodDtls))
            return;
        $sfCount = count($salaryPeriodDtls);
        if ($sfCount <= 0)
            return;
        return $salaryPeriodDtls[0]->begin_date;
    }

    public function getEndDate() {
        $salaryPeriodDtls = $this->salary_period_dtls;
        if (!isset($salaryPeriodDtls))
            return;
        $sfCount = count($salaryPeriodDtls);
        if ($sfCount <= 0)
            return;
        return $salaryPeriodDtls[$sfCount - 1]->end_date;
    }
}
