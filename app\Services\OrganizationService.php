<?php

namespace App\Services;

use App\Models\User;
use App\Models\Organization;
use App\Models\DatabaseConfig;
use App\Models\Constant\ConstData;
use App\Services\AdminService;
use App\Exceptions\SystemException;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class OrganizationService
{
    public $adminService;
    public function __construct(AdminService $adminService) {
        $this->adminService   = $adminService;
    }

    public function createDataBaseConfig($organizationName, $userId) {
        $dbName   = DatabaseConfig::getCNByOrganizationName($organizationName);
        $database = DatabaseConfig::where(DatabaseConfig::DATABASE, $dbName)->first();
        if ($database)
            throw new SystemException(ConstData::SYSTEM_EXCEPTION, 3, $dbName);

        $driver       = 'mysql';
        $host         = config('services.database_config.db_host');
        $port         = config('services.database_config.db_port');
        $userName     = config('services.database_config.db_username');
        $password     = config('services.database_config.db_password');

        $organizationAdminUser = User::find($userId);
        if (!$organizationAdminUser)
            throw new SystemException(ConstData::SYSTEM_EXCEPTION, 3, $dbName);

        $databaseConfig = DatabaseConfig::create(
            [
                DatabaseConfig::CONNECTION_NAME         => $dbName,
                DatabaseConfig::DRIVER                  => $driver,
                DatabaseConfig::HOST                    => $host,
                DatabaseConfig::PORT                    => $port,
                DatabaseConfig::DATABASE                => $dbName,
                DatabaseConfig::USER_NAME               => $userName,
                DatabaseConfig::PASSWORD                => empty($password) ? '' : Hash::make($password),
                DatabaseConfig::CREATED_BY              => $userId,
            ]
        );
        $this->createNewDbWithConnection($databaseConfig, $password);
        return $databaseConfig;
    }

    public function createNewDbWithConnection(DatabaseConfig $databaseConfig, $password = '') {
        $driver       = $databaseConfig->driver;
        $host         = $databaseConfig->host;
        $port         = $databaseConfig->port;
        $database     = $databaseConfig->database;
        $userName     = $databaseConfig->user_name;

        $defaultConnection = config('database.default');
        $newConnection     = config("database.connections.$defaultConnection");
        $newConnection['database'] = $database;
        config(["database.connections.{$database}" => $newConnection]);

        DB::statement('CREATE DATABASE ' . $database);

        $path = config_path('database.php');
        $contents = File::get($path);

        $newConfig = "'{$database}' => [
            'driver' => '{$driver}',
            'url' => env('DATABASE_URL'),
            'host' => '{$host}',
            'port' => '{$port}',
            'database' => '{$database}',
            'username' => '{$userName}',
            'password' => '{$password}',
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        //%newConfig%";

        $contents = str_replace('%newConfig%', $newConfig, $contents);
        $contents = str_replace("//'{$database}", "'{$database}", $contents);
        File::put($path, $contents);

        Artisan::call('migrate:refresh', ['--path' => 'database/migrations/customer', '--database' => $database, '--force' => TRUE ]);
        Artisan::call('config:cache');
        Artisan::call('config:clear');
        // Artisan::call('db:seed', [ '--class' => 'CustomerConfigurationSeeder', '--database' => $database, '--force' => TRUE ]);
        
    }

    public function fixConnectionConfig($orgRegNumber) {
        $organization = Organization::where(Organization::REGISTRATION_NUMBER, $orgRegNumber)->first();
        if (!$organization)
            return;
        $databaseConfig = $organization->database_config;
        if (!$databaseConfig)
            return;
        $connectionName = $databaseConfig->connection_name;
        $conConfig     = config("database.connections.$connectionName");
        if ($conConfig)
            return;
        $password = config('services.database_config.db_password');
        $driver       = $databaseConfig->driver;
        $host         = $databaseConfig->host;
        $port         = $databaseConfig->port;
        $database     = $databaseConfig->database;
        $userName     = $databaseConfig->user_name;

        $defaultConnection = config('database.default');
        $newConnection     = config("database.connections.$defaultConnection");
        $newConnection['database'] = $database;
        config(["database.connections.{$database}" => $newConnection]);

        $path = config_path('database.php');
        $contents = File::get($path);

        $newConfig = "'{$database}' => [
            'driver' => '{$driver}',
            'url' => env('DATABASE_URL'),
            'host' => '{$host}',
            'port' => '{$port}',
            'database' => '{$database}',
            'username' => '{$userName}',
            'password' => '{$password}',
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        //%newConfig%";

        $contents = str_replace('%newConfig%', $newConfig, $contents);
        $contents = str_replace("//'{$database}", "'{$database}", $contents);
        File::put($path, $contents);
        Artisan::call('config:cache');
        Artisan::call('config:clear');
    }
}
