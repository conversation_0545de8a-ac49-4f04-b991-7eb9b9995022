<?php

namespace App\Models;

use App\Models\Constant\ConstData;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

use Filament\Panel;
use Filament\Models\Contracts\FilamentUser;

class User extends Authenticatable implements FilamentUser
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;
    protected $connection = ConstData::ADMIN_DB;

    const TABLE = 'users';

    const ID                        = 'id';
    const NAME                      = 'name';
    const PHONE                     = 'phone';
    const SMS_VERIFIED_AT           = 'sms_verified_at';
    const EMAIL                     = 'email';
    const EMAIL_VERIFIED_AT         = 'email_verified_at';
    const PASSWORD                  = 'password';
    const ACTIVE_STATUS             = 'active_status';

    // const RELATION_ROLES            = 'roles';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        self::NAME,
        self::PHONE,
        self::EMAIL,
        self::PASSWORD,
        self::ACTIVE_STATUS,
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        self::PASSWORD,
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        self::EMAIL_VERIFIED_AT => 'datetime',
        self::SMS_VERIFIED_AT   => 'datetime',
        self::ACTIVE_STATUS     => 'boolean',
    ];

    public function canAccessPanel(Panel $panel): bool
    {
        $id = $panel->getId();
        return $this->canAccess($id);
    }

    public function canAccess($id) {
        return ($this->isSuperAdmin() && $id == ConstData::ROLE_SUPER_ADMIN) || ($this->isAdmin() && $id == ConstData::ROLE_ADMIN) || (!$this->isSuperAdmin() && !$this->isAdmin() && $id == ConstData::ROLE_ADMIN);
    }

    public function organizations(): BelongsToMany
    {
        return $this->belongsToMany(Organization::class, 'user_organization');
    }

    public function hasMyRole($roleName)
    {
        return $this->roles->contains('name', $roleName);
    }

    public function isSuperAdmin()
    {
        return $this->hasMyRole(ConstData::ROLE_SUPER_ADMIN);
    }
    public function isAdmin()
    {
        return $this->hasMyRole(ConstData::ROLE_ADMIN);
    }
}
