<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class SalaryDayFrequency
 *
 * @mixin \App\Models\Customer\Time\SalaryDayFrequency
 */
class SalaryPeriod extends JsonResource
{
    const ID                            = 'id';
    const YEAR                          = 'year';
    const MONTH                         = 'month';
    const SALARY_PERIOD_DTLS            = 'salary_period_dtls';
    const SALARY_PERIOD_SUB_DTLS        = 'salary_period_sub_dtls';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                            => $this->id,
            self::YEAR                          => $this->year,
            self::MONTH                         => $this->month,
            self::SALARY_PERIOD_DTLS            => SalaryPeriodDtl::collection($this->salary_period_dtls),
        ];
    }
}
