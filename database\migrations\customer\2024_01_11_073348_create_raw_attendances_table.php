<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRawAttendancesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('raw_attendances'))
            return;
        Schema::create('raw_attendances', function (Blueprint $table) {
            $table->id();
            $table->string('attendance_code');
            $table->dateTime('register_date');
            $table->unsignedBigInteger('type');
            $table->string('from_name');
            $table->unsignedDecimal('latitude',  $precision = 9, $scale = 6)->nullable();
            $table->unsignedDecimal('longitude', $precision = 9, $scale = 6)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('raw_attendances');
    }
}
