<?php

namespace App\Models\Customer\Time\Schedule\MainTimeSchedule;

use App\Models\Customer\Time\Schedule\TimeScheduleDtl;
use App\Http\Tools\DateTool;
use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MainTimeScheduleDtl extends TimeScheduleDtl
{
    use HasFactory;
    const TABLE = 'main_time_schedule_dtls';

    const MAIN_TIME_SCHEDULE_ID = 'main_time_schedule_id';

    const BEGIN_DATE_TIME       = 'begin_date_time';
    const END_DATE_TIME         = 'end_date_time';
    const IS_HOLIDAY            = 'is_holiday';
    const IS_DYNAMIC            = 'is_dynamic';
    const UNDURLUGUU_MIN        = 'undurluguu_min';

    const TYPE_SIMPLE           = 1;
    const TYPE_DYNAMIC          = 2;
    const TYPE_HOLIDAY          = 3;

    public function __construct($attributes = array()) {
        $this->connection             = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $casts = [
        self::HAS_BREAK_TIME => 'boolean',
        self::IS_DYNAMIC     => 'boolean',
    ];

    public function time_schedule(): BelongsTo {
        return $this->belongsTo(MainTimeSchedule::class);
    }

    public function time_schedule_dtl_bts(): HasMany{
        return $this->hasMany(MainTimeScheduleDtlBt::class);
    }

    public function getTypeNameAttribute() {
        return $this->type == 1 || $this->type == 2 ? 'Ажиллана' : 'Амарна';
    }

    public function getDayOfWeekNameAttribute() {
        $value = '';
        $day = $this->day_of_week;
        switch ($this->day_of_week) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
                $value = "$day дэхь өдөр";
                break;
            case 6:
                $value = 'Хагас сайн өдөр';
                break;
            case 7:
                $value = 'Бүтэн сайн өдөр';
                break;

            default:
                # code...
                break;
        }
        return $value;
    }

    public function loadTimeFields() {
        switch ($this->type) {
            case TimeScheduleDtl::TYPE_SIMPLE:
                $this->rb_time_str = null;
                $this->re_time_str = null;
                $this->work_min    = null;
                break;
            case TimeScheduleDtl::TYPE_DYNAMIC:
                $this->begin_time_str = null;
                $this->end_time_str   = null;
                break;
            default:
                break;
        }
    }

    public function getDefaultMainTimeScheduleDtlBts() {
        return [new MainTimeScheduleDtlBt([
            MainTimeScheduleDtlBt::BEGIN_TIME_STR => '12:00',
            MainTimeScheduleDtlBt::END_TIME_STR   => '13:00',
            MainTimeScheduleDtlBt::CREATED_BY     => auth()->user()->id,
        ])];
    }

    public function setSimpleBeginAndEndTime($salaryDate) {
        $beginTimeStrArray  = explode(':', $this->begin_time_str);
        $endTimeStrArray    = explode(':', $this->end_time_str);
        $beginDateWithTime  = DateTool::setDateTime($salaryDate, $beginTimeStrArray[0], $beginTimeStrArray[1]);

        $is_midnight        = $this->end_time_str == '00:00';
        $endDateWithTime    = DateTool::setDateTime($is_midnight ? DateTool::addDays($salaryDate, 1) : $salaryDate, $endTimeStrArray[0], $endTimeStrArray[1]);

        $this->begin_date_time = $beginDateWithTime;
        $this->end_date_time   = $endDateWithTime;
    }
}
