<?php

namespace App\Models\Customer\Time\Approval;

use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ApprovalConfigHdr extends Model
{
    use HasFactory;
    const TABLE               = 'approval_config_hdrs';

    const ID                  = 'id';
    const NAME                = 'name';
    const CREATED_BY          = 'created_by';
    const UPDATED_BY          = 'updated_by';

    public function __construct($attributes = array()) {
        $this->connection             = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        self::ID,
        self::NAME,
        self::CREATED_BY,
    ];

    protected $casts = [
        self::RELATION_APPROVAL_CONFIG_DTLS     => 'array',
        self::RELATION_APPROVAL_CONFIG_SUB_DTLS => 'array',
    ];

    const RELATION_APPROVAL_CONFIG_DTLS     = 'approval_config_dtls';
    const RELATION_APPROVAL_CONFIG_SUB_DTLS = 'approval_config_sub_dtls';

    public function approval_config_dtls(): HasMany {
        return $this->hasMany(ApprovalConfigDtl::class);
    }

    public function approval_config_sub_dtls(): HasMany {
        return $this->hasMany(ApprovalConfigSubDtl::class);
    }
}
