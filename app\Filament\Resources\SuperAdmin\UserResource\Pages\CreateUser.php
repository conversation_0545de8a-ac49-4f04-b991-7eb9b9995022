<?php

namespace App\Filament\Resources\SuperAdmin\UserResource\Pages;

use App\Models\Constant\ConstData;
use App\Models\Role;
use App\Filament\Resources\SuperAdmin\UserResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Hash;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['password'] = Hash::make('password');
        return $data;
    }
}
