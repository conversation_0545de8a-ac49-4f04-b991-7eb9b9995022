<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cash_request_config_dtls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cash_request_config_id')->constrained('cash_request_configs')->onDelete('cascade');
            $table->unsignedDecimal('limit_amount', $precision = 24, $scale = 2)->default(0);
            $table->unsignedBigInteger('employee_user_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cash_request_config_dtls');
    }
};
