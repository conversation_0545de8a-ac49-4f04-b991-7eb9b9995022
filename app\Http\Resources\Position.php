<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class Apartment
 *
 * @mixin \App\Models\Apartment
 */
class Position extends JsonResource
{
    const ID                    = 'id';
    const NAME                  = 'name';
    const SHORT_NAME            = 'short_name';
    const DEPARTMENT_ID         = 'department_id';
    const DEPARTMENT            = 'department';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                => $this->id,
            self::NAME              => $this->name,
            self::SHORT_NAME        => $this->short_name,
            self::DEPARTMENT_ID     => $this->department_id,
            self::DEPARTMENT        => new Department($this->department),
        ];
    }
}
