<?php

namespace App\Services\Time;

use Illuminate\Support\Facades\Http;

class BioTimeService
{
    public function createUser($dcHost, $dcPort, $employeeId, $fullName, $attendanceCode) {
        $bioUrl   = config('services.biotime.url');
        $url      = "$bioUrl/users/?host=$dcHost&port=$dcPort";
        $response = Http::post($url, [
            'name'        => $fullName,
            'password'    => "",
            'user_id'     => $attendanceCode,
            'uid'         => $employeeId,
            'privilege'   => 0,
        ]);
        return $response;
    }

    public function getUser($dcHost, $dcPort, $attendanceCode) {
        try {
            $url      = config('services.biotime.url');
            $params   = [
                'by'     => 'user_id',
                'host'   => $dcHost,
                'port'   => $dcPort,
            ];
            $fullUrl  = "$url/users/$attendanceCode";
            $response = Http::timeout(2)->get($fullUrl, $params);    
            return $response;
        } catch (\Throwable $th) {
            return null;
        }
    }

    public function deleteUser($dcHost, $dcPort, $attendanceCode) {
        $url      = config('services.biotime.url');
        $params   = [
            'by'     => 'user_id',
            'host'   => $dcHost,
            'port'   => $dcPort,
        ];
        $fullUrl  = "$url/users/$attendanceCode";
        $response = Http::delete($fullUrl, $params);
        return $response->json();
    }

    public function getRawAttendances($dcHost, $dcPort, $beginDate, $endDate) {
        $url      = config('services.biotime.url');
        $params   = [
            'host'   => $dcHost,
            'port'   => $dcPort,
        ];
        if (isset($beginDate))
            $params['start'] = $beginDate;
        if (isset($endDate))
            $params['end'] = $endDate;
        $response = Http::get($url.'/attendances/', $params);
        if ($response->successful())    
            return $response->json();
        else 
            return [];
    }
}
