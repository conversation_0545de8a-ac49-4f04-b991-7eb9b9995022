<?php

namespace App\Models\Customer\Time\TimeRequest;

use App\Http\Tools\DateTool;
use App\Models\Customer\Employee;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TimeRequest extends Model
{
    use HasFactory;
    const TABLE               = 'time_requests';

    const ID                  = 'id';
    const EMPLOYEE_ID         = 'employee_id';
    const BEGIN_DATE          = 'begin_date';
    const END_DATE            = 'end_date';
    const DESCRIPTION         = 'description';
    const ATT_STATUS          = 'att_status';
    const CONFIRM_STATUS      = 'confirm_status';
    const CREATED_BY          = 'created_by';
    const UPDATED_BY          = 'updated_by';
    const IS_USED             = 'is_used';

    const RELATION_EMPLOYEE                = 'employee';
    const RELATION_TIME_REQUEST_DTLS       = 'time_request_dtls';

    const CONFIRM_STATUS_SENT              = 1;
    const CONFIRM_STATUS_UNCONFIRMED       = 2;
    const CONFIRM_STATUS_CONFIRMED         = 3;
    const CONFIRM_STATUS_TEMP_CONFIRMED    = 4;

    const CONFIRM_STATUS_NAME_SENT              = 'Илгээсэн';
    const CONFIRM_STATUS_NAME_UNCONFIRMED       = 'Цуцалсан';
    const CONFIRM_STATUS_NAME_CONFIRMED         = 'Батлагдсан';
    const CONFIRM_STATUS_NAME_TEMP_CONFIRMED    = 'Түр батлагдсан';

    const ALL_CONFIRM_STATUS = [
        self::CONFIRM_STATUS_SENT,
        self::CONFIRM_STATUS_UNCONFIRMED,
        self::CONFIRM_STATUS_CONFIRMED, 
    ];

    const REQUEST_CONFIRM_STATUS = [
        self::CONFIRM_STATUS_UNCONFIRMED,
        self::CONFIRM_STATUS_CONFIRMED,
    ];

    public function getAttStatusNames() {
        $attendanceStatuses[self::CONFIRM_STATUS_SENT]           = self::CONFIRM_STATUS_NAME_SENT;
        $attendanceStatuses[self::CONFIRM_STATUS_UNCONFIRMED]    = self::CONFIRM_STATUS_NAME_UNCONFIRMED;
        $attendanceStatuses[self::CONFIRM_STATUS_CONFIRMED]      = self::CONFIRM_STATUS_NAME_CONFIRMED;
        return $attendanceStatuses;
    }

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        self::ID,
        self::EMPLOYEE_ID,
        self::BEGIN_DATE,
        self::END_DATE,
        self::DESCRIPTION,
        self::ATT_STATUS,
        self::CONFIRM_STATUS,
        self::CREATED_BY,
        self::IS_USED
    ];

    public function employee() {
        return $this->belongsTo(Employee::class);
    }

    public function time_request_dtls() {
        return $this->hasMany(TimeRequestDtl::class);
    }

    public function getAttStatusName() {
        switch ($this->att_status) {
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_UNKNOWN_WORK:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_UNKNOWN_WORK;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_UNKNOWN_EFFORT:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_UNKNOWN_EFFORT;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_EFFORT:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_EFFORT;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_WORK;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_BREAK:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_BREAK;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_BREAK_EFFORT:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_BREAK_EFFORT;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_LATE:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_LATE;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_ABSENT:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_ABSENT;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_FURLOUGH;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_OVER:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_OVER;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_MATERNITY:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_MATERNITY;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_VACATION:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_VACATION;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_SICK:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SICK;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_NIGHT_WORK:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_NIGHT_WORK;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_COMPENSATORY_REST:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_COMPENSATORY_REST;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_REQUEST_WORK:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_REQUEST_WORK;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_OUT_WORK:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_OUT_WORK;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH_SHORT:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_FURLOUGH.' ('.EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_FURLOUGH_SHORT.')';
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH_MEDIUM:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_FURLOUGH.' ('.EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_FURLOUGH_MEDIUM.')';
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH_LONG:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_FURLOUGH.' ('.EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_FURLOUGH_LONG.')';
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH.' ('.EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_BIRTHDAY.')';
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_KID_BIRTHDAY:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH.' ('.EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_KID_BIRTHDAY.')';
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_HALFDAY:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH.' ('.EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_HALFDAY.')';
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE:
                $attStatusName = EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH.' ('.EmployeeAttendanceDtl::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_NOSMOKE.')';
                break;
            default:
                # code...
                break;
        }
        return $attStatusName;
    }

    public function getConfirmStatusName() {
        $confirmStatusName = '';
        switch ($this->confirm_status) {
            case self::CONFIRM_STATUS_SENT:
            case self::CONFIRM_STATUS_TEMP_CONFIRMED:
                $confirmStatusName = 'Илгээсэн';
                break;
            case self::CONFIRM_STATUS_CONFIRMED:
                $confirmStatusName = 'Баталсан';
                break;
            case self::CONFIRM_STATUS_UNCONFIRMED:
                $confirmStatusName = 'Цуцалсан';
                break;
            default:
                # code...
                break;
        }
        return $confirmStatusName;
    }

    public function getDiffHoursAttribute() {
        $diffMin = DateTool::getMinuteBetweenDates($this->begin_date, $this->end_date);
        return $diffMin/60;
    }
}
