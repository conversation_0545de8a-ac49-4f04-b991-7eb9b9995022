<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cash_request_dtls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cash_request_id')->constrained();
            $table->unsignedBigInteger('employee_user_id');
            $table->longText('description')->nullable();
            $table->enum('status', ['approved', 'rejected'])->default('approved');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cash_request_dtls');
    }
};
