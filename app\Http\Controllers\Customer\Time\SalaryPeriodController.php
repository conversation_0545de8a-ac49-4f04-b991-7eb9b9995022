<?php

namespace App\Http\Controllers\Customer\Time;

use App\Models\Customer\Time\SalaryPeriod\SalaryPeriod;
use App\Http\Requests\Customer\Time\SalaryPeriod\GetSalaryPeriodsRequest;
use App\Http\Resources\SalaryPeriod as SalaryPeriodResource;
use App\Services\ConnectionService;
use App\Http\Controllers\Controller;

class SalaryPeriodController extends Controller
{
    public function getCN() {
        $service = resolve(ConnectionService::class);
        return $service->getCNForEmployeeUser();
    }
    /**
     * Цалингийн үечлэлүүдийг жагсаалтаар авах.
     */
    public function index(GetSalaryPeriodsRequest $request)
    {
        $limit         = $request->input(GetSalaryPeriodsRequest::LIMIT, 10);
        $filters       = $request->input(GetSalaryPeriodsRequest::FILTER);
        $cn            = $this->getCN();
        $salaryPeriods = SalaryPeriod::on($cn);
        if (isset($filters[GetSalaryPeriodsRequest::FILTER_YEAR])) {
            $year          = $filters[GetSalaryPeriodsRequest::FILTER_YEAR];
            $salaryPeriods = $salaryPeriods->where(SalaryPeriod::YEAR, $year);
        }
        if (isset($filters[GetSalaryPeriodsRequest::FILTER_MONTH])) {
            $month         = $filters[GetSalaryPeriodsRequest::FILTER_MONTH];
            $salaryPeriods = $salaryPeriods->where(SalaryPeriod::MONTH, $month);
        }
        $salaryPeriods = $salaryPeriods->paginate($limit);
        return SalaryPeriodResource::collection($salaryPeriods);
    }
}
