<?php

namespace App\Http\Requests\Customer\Time\EmployeeAttendance;

use Illuminate\Foundation\Http\FormRequest;

class GetEmployeeAttendancesRequest extends FormRequest
{
    const LIMIT                  = 'limit';
    const FILTER                 = 'filter';
    const FILTER_YEAR            = 'year';
    const FILTER_MONTH           = 'month';
    const SORT                   = 'sort';
    const SORT_FIELD             = 'field';
    const SORT_TYPE              = 'type';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::FILTER_YEAR  => 'nullable|integer|between:2000,3000',
            self::FILTER_MONTH => 'nullable|integer|between:1,12',
        ];
    }
}
