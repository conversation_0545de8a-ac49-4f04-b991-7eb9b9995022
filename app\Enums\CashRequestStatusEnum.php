<?php

namespace App\Enums;
use Filament\Support\Contracts\HasLabel;
 
enum CashRequestStatusEnum: string implements HasLabel
{
    case PENDING       = 'pending';
    case TEMP_APPROVED = 'temp_approved';
    case REJECTED      = 'rejected';
    case APPROVED      = 'approved';


    public function getLabelColor(): ?string
    {
        return match ($this) {
            self::APPROVED      => 'success',
            self::TEMP_APPROVED => 'warning',
            self::REJECTED      => 'danger',
            self::PENDING       => 'gray',
        };
    }
    
    public function getLabel(): ?string
    {
        return match ($this) {
            self::APPROVED      => 'Баталсан',
            self::TEMP_APPROVED => 'Батлагдаж дуусаагүй',
            self::REJECTED      => 'Цуцалсан',
            self::PENDING       => 'Хүлээгдэж байгаа',
        };
    }
}
