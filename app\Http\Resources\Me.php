<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class Me extends JsonResource
{
    const ID                           = 'id';
    const PHONE                        = 'phone';
    const EMPLOYEE                     = 'employee';
    const BATLALH_ERKH_BGA_ESEKH       = 'batlalh_erkh_bga_esekh';
    const EMPLOYEE_ROLES               = 'employee_roles';

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'                        => $this->id,
            'phone'                     => intval($this->phone),
            'employee'                  => new Employee($this->employee),
            'batlalh_erkh_bga_esekh'    => $this->batlalh_erkh_bga_esekh,
            'employee_roles'            => EmployeeRole::collection($this->employee_roles),
        ];
    }
}
