<?php

namespace App\Models\Customer\Voucher;

use App\Models\Customer\Department;
use App\Models\Customer\Employee;
use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VoucherTohirgoo extends Model
{
    use HasFactory;

    const ID             = 'id';
    const NAME           = 'name';
    const BEGIN_DATE     = 'begin_date';
    const INTERVAL_MONTH = 'interval_month';
    const AMOUNT         = 'amount';
    const IS_USED        = 'is_used';

    const RELATION_DEPARTMENTS = 'departments';
    const RELATION_EMPLOYEES   = 'employees';

    public function __construct($attributes = array()) {
        $this->connection             = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::NAME,
        self::BEGIN_DATE,
        self::INTERVAL_MONTH,
        self::AMOUNT,
        self::IS_USED
    ];

    protected $casts = [
        self::RELATION_DEPARTMENTS => 'array',
        self::RELATION_EMPLOYEES   => 'array',
    ];

    public function departments()
    {
        return $this->belongsToMany(Department::class, 'voucher_tohirgoo_department');
    }

    public function employees()
    {
        return $this->belongsToMany(Employee::class, 'voucher_tohirgoo_employee');
    }
}
