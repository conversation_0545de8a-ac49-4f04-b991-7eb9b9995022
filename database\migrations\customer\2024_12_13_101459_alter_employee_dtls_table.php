<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasColumn('employee_dtls', 'uniq_id'))
            return;
        Schema::table('employee_dtls', function (Blueprint $table) {
            $table->string('uniq_id')->nullable();
            $table->datetime('expired_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employee_dtls', function (Blueprint $table) {
            $table->dropColumn('uniq_id');
            $table->dropColumn('expired_at'); 
        });
    }
};
