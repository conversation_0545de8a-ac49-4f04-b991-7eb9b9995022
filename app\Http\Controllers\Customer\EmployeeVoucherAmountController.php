<?php

namespace App\Http\Controllers\Customer;

use App\Models\Constant\ConstData;
use App\Models\Customer\Merchant;
use App\Models\Customer\Employee;
use App\Models\Customer\EmployeeDtl;
use App\Models\Customer\Voucher\EmployeeVoucher;
use App\Models\Customer\Voucher\EmployeeVoucherAmount;
use App\Models\Customer\Voucher\EmployeeVoucherSpent;
use App\Models\Customer\Voucher\EmployeeVoucherSpentDtl;
use App\Enums\EmployeeVoucherStatusEnum;
use App\Http\Requests\GetEmployeeVoucherAmountByUniqId;
use App\Http\Requests\SpendEmployeeVoucherRequest;
use App\Http\Resources\EmployeeVoucherAmount as EmployeeVoucherAmountResource;
use App\Http\Resources\EmployeeVoucherAmountWithEmployee as EmployeeVoucherAmountWithEmployeeResource;
use App\Http\Resources\EmployeeDtlUE as EmployeeDtlUEResource;
use App\Services\ConnectionService;
use App\Http\Controllers\Controller;
use App\Exceptions\SystemException;
use App\Services\ToolService;
use App\Http\Tools\DateTool;
use App\Models\Customer\MerchantEmployeeUser;
use App\Models\EmployeeUser;
use App\Services\NotificationService;

class EmployeeVoucherAmountController extends Controller
{
    public function getCN() {
        $service = resolve(ConnectionService::class);
        return $service->getCNForEmployeeUser();
    }

    public function getAUEmployee() 
    {
        $cn           = $this->getCN();
        $employeeUser = auth()->user();
        $employee     = Employee::on($cn)->where(Employee::PHONE, $employeeUser->phone)->first();
        return $employee;
    }

    public function uniqId() {
        $cn           = $this->getCN();
        $employeeUser = auth()->user();
        $employee     = Employee::on($cn)->where(Employee::PHONE, $employeeUser->phone)->first();
        $nowDateTime  = DateTool::nowDateTime();
        if ($employee->employee_dtl->expired_at < $nowDateTime) {
            $uniqueId = resolve(ToolService::class)->generateUniqId();
            $employee->employee_dtl->uniq_id    = $uniqueId;
            $employee->employee_dtl->expired_at = DateTool::addMinute($nowDateTime, 3);
            $employee->employee_dtl->save();
        }
        return new EmployeeDtlUEResource($employee->employee_dtl);
    }

    public function getEmployeeVoucherAmountByUniqId(GetEmployeeVoucherAmountByUniqId $request) {
        $cn          = $this->getCN();
        $uniqId      = $request->input(GetEmployeeVoucherAmountByUniqId::PARAMETER_UNIQ_ID);
        $employeeDtl = EmployeeDtl::on($cn)->where(EmployeeDtl::UNIQ_ID, $uniqId)->first();
        if (!isset($employeeDtl))
            throw new SystemException(ConstData::SYSTEM_EXCEPTION, 11);
        $employeeDtl = EmployeeDtl::on($cn)->where(EmployeeDtl::UNIQ_ID, $uniqId)
                        ->where(EmployeeDtl::EXPIRED_AT, '>', DateTool::nowDateTime())->first(); 
        if (!isset($employeeDtl))
            throw new SystemException(ConstData::SYSTEM_EXCEPTION, 12);
        $employeeVoucherAmount = EmployeeVoucherAmount::on($cn)->where(EmployeeVoucher::EMPLOYEE_ID, $employeeDtl->employee_id)->first();
        if (!isset($employeeVoucherAmount))
            throw new SystemException(ConstData::SYSTEM_EXCEPTION, 14);
        return new EmployeeVoucherAmountWithEmployeeResource($employeeVoucherAmount);
    }

    public function spendVoucher(SpendEmployeeVoucherRequest $request) {
        $cn           = $this->getCN();
        $uniqId       = $request->input(SpendEmployeeVoucherRequest::PARAMETER_UNIQ_ID);
        $amount       = $request->input(SpendEmployeeVoucherRequest::PARAMETER_AMOUNT);
       
        $employeeDtl = EmployeeDtl::on($cn)->where(EmployeeDtl::UNIQ_ID, $uniqId)->first();
        if (!isset($employeeDtl))
            throw new SystemException(ConstData::SYSTEM_EXCEPTION, 11);
        $employeeDtl = EmployeeDtl::on($cn)->where(EmployeeDtl::EXPIRED_AT, '>', DateTool::nowDateTime())->first(); 
        if (!isset($employeeDtl))
            throw new SystemException(ConstData::SYSTEM_EXCEPTION, 12);

        $unusedAmount = EmployeeVoucher::on($cn)
            ->where(EmployeeVoucher::EMPLOYEE_ID, $employeeDtl->employee_id)
            ->where(EmployeeVoucher::AMOUNT, '!=', EmployeeVoucher::USED_AMOUNT)
            ->where(EmployeeVoucher::STATUS, '!=', EmployeeVoucherStatusEnum::BUREN)
            ->where(EmployeeVoucher::IS_ACTIVE, true)
            ->sum(EmployeeVoucher::UNUSED_AMOUNT);

        if ($amount > $unusedAmount)
            throw new SystemException(ConstData::SYSTEM_EXCEPTION, 13);

        $employeeVouchers = EmployeeVoucher::on($cn)
            ->where(EmployeeVoucher::EMPLOYEE_ID, $employeeDtl->employee_id)
            ->where(EmployeeVoucher::AMOUNT, '!=', EmployeeVoucher::USED_AMOUNT)
            ->where(EmployeeVoucher::STATUS, '!=', EmployeeVoucherStatusEnum::BUREN)
            ->where(EmployeeVoucher::IS_ACTIVE, true)
            ->orderBy(EmployeeVoucher::CREATED_AT)
            ->get();

        $employeeUser = auth()->user();

        $merchantEmployeeUser = MerchantEmployeeUser::on($cn)
            ->where(MerchantEmployeeUser::EMPLOYEE_USER_ID, $employeeUser->id)
            ->first();

        if (!isset($merchantEmployeeUser))
            throw new SystemException(ConstData::SYSTEM_EXCEPTION, 15);

        $merchant = Merchant::on($cn)->where(Merchant::ID, $merchantEmployeeUser->merchant_id)->first();
        if (!isset($merchant))
            throw new SystemException(ConstData::SYSTEM_EXCEPTION, 15);
        
        $employeeVoucherSpent = EmployeeVoucherSpent::on($cn)->create([
            EmployeeVoucherSpent::EMPLOYEE_ID               => $employeeDtl->employee_id,
            EmployeeVoucherSpent::AMOUNT                    => $amount,
            EmployeeVoucherSpent::MERCHANT_COMPANY_NAME     => $merchant->name,
            EmployeeVoucherSpent::MERCHANT_LOCATION_NAME    => $merchant->location_name,
            EmployeeVoucherSpent::MERCHANT_EMPLOYEE_USER_ID => $employeeUser->id,
        ]);

        $remainingAmount = $amount;
        $totalAmount     = 0;

        foreach ($employeeVouchers as $employeeVoucher) {
            if ($remainingAmount <= 0) break;

            $usedAmount = min($employeeVoucher->unused_amount, $remainingAmount);

            $employeeVoucher->used_amount   += $usedAmount;
            $employeeVoucher->unused_amount -= $usedAmount;
            $employeeVoucher->status         = $employeeVoucher->unused_amount > 0 ? 'dutuu' : 'buren';
            $employeeVoucher->save();

            EmployeeVoucherSpentDtl::on($cn)->create([
                EmployeeVoucherSpentDtl::EMPLOYEE_VOUCHER_SPENT_ID => $employeeVoucherSpent->id,
                EmployeeVoucherSpentDtl::EMPLOYEE_VOUCHER_ID       => $employeeVoucher->id,
                EmployeeVoucherSpentDtl::AMOUNT                    => $usedAmount
            ]);

            $totalAmount     += $usedAmount;
            $remainingAmount -= $usedAmount;
        }

        $employeeVoucherAmount = EmployeeVoucherAmount::on($cn)
            ->where(EmployeeVoucherAmount::EMPLOYEE_ID, $employeeDtl->employee_id)
            ->first();

        // Хэрвээ дүн тооцож хадгалах хэрэгтэй бол энд шинэчлэх боломжтой
        // Жишээ нь:
        // $employeeVoucherAmount->amount = ...;
        $employeeVoucherAmount->save();

        $empPhone = optional($employeeDtl->employee)->phone;
        $empUser  = $empPhone
            ? EmployeeUser::where(EmployeeUser::ORGANIZATION_ID, auth()->user()->organization->id)
                ->where(EmployeeUser::PHONE, $empPhone)
                ->first()
            : null;

        if ($empUser) {
            $topic = (config('app.env') === 'production' ? 'production' : 'dev') . '-user-' . $empUser->id;
            resolve(NotificationService::class)->sendPushNotification(
                $topic,
                'Ваучер-н зарцуулалт',
                "Та амжилттай {$totalAmount} төгрөгний ваучер-н зарцуулалтаа хийлээ."
            );
            $employeeVoucherAmount->topic = $topic;
        }
        return new EmployeeVoucherAmountResource($employeeVoucherAmount);
    }
}
