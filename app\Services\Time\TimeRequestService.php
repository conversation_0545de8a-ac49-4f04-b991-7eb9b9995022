<?php

namespace App\Services\Time;

use App\Models\Customer\Employee;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Models\Customer\Time\RequestConfig\RequestConfigDtl;
use App\Models\Customer\Time\TimeRequest\TimeRequest;
use App\Models\Customer\Time\TimeRequest\TimeRequestDtl;
use App\Services\Time\RequestConfigService;
use Illuminate\Database\Eloquent\Builder;

use function Ramsey\Uuid\v1;

class TimeRequestService
{
    public function getConfirmedTimeRequestByBetweenDates($connectionName, $beginDate, $endDate, $departmentIds) {
        $timeRequests = TimeRequest::on($connectionName)->where(TimeRequest::CONFIRM_STATUS, TimeRequest::CONFIRM_STATUS_CONFIRMED);
        if (!empty($departmentIds)) {
            $timeRequests = $timeRequests->whereHas(TimeRequest::RELATION_EMPLOYEE, function (Builder $query) use ($departmentIds) {
                $query->whereIn(Employee::DEPARTMENT_ID, $departmentIds);
            });
        }

        $timeRequests = $timeRequests->where(
            function($query) use($beginDate, $endDate) {
                return $query->where(
                    function($query) use($beginDate, $endDate) {
                        return $query->whereDate(TimeRequest::BEGIN_DATE, '>=', $beginDate)->whereDate(TimeRequest::END_DATE, '<=', $endDate);
                    }
                  )->orWhere(
                    function($query) use($beginDate, $endDate) {
                        return $query->whereDate(TimeRequest::BEGIN_DATE, '<', $beginDate)->whereDate(TimeRequest::END_DATE, '>', $endDate);
                    }
                  )->orWhere(
                    function($query) use($beginDate, $endDate) {
                        return $query->whereDate(TimeRequest::BEGIN_DATE, '<', $beginDate)->whereBetween(TimeRequest::END_DATE, [$beginDate, $endDate]);
                    }
                  )->orWhere(
                    function($query) use($beginDate, $endDate) {
                        return $query->whereBetween(TimeRequest::BEGIN_DATE, [$beginDate, $endDate])->whereDate(TimeRequest::END_DATE, '>', $endDate);
                    }
                  );
            }
          )->orderByDesc(TimeRequest::CREATED_AT)->get();
        return $timeRequests;
    }

    public function getEmployeeTimeRequestsByBetweenDates($cn, $beginDate, $endDate) {
        $user         = auth()->user();
        $timeRequests = TimeRequest::on($cn)->where(TimeRequest::CREATED_BY, $user->id);
        $timeRequests = $timeRequests->where(
            function($query) use($beginDate, $endDate) {
                return $query->where(
                    function($query) use($beginDate, $endDate) {
                        return $query->whereDate(TimeRequest::BEGIN_DATE, '>=', $beginDate)->whereDate(TimeRequest::END_DATE, '<=', $endDate);
                    }
                    )->orWhere(
                    function($query) use($beginDate, $endDate) {
                        return $query->whereDate(TimeRequest::BEGIN_DATE, '<', $beginDate)->whereDate(TimeRequest::END_DATE, '>', $endDate);
                    }
                    )->orWhere(
                    function($query) use($beginDate, $endDate) {
                        return $query->whereDate(TimeRequest::BEGIN_DATE, '<', $beginDate)->whereBetween(TimeRequest::END_DATE, [$beginDate, $endDate]);
                    }
                    )->orWhere(
                    function($query) use($beginDate, $endDate) {
                        return $query->whereBetween(TimeRequest::BEGIN_DATE, [$beginDate, $endDate])->whereDate(TimeRequest::END_DATE, '>', $endDate);
                    }
                    );
            }
            )->orderByDesc(TimeRequest::CREATED_AT)->get();
        return $timeRequests;
    }

    /**
     * Тухайн ажилтаны шийдвэрлэсэн хүсэлтүүд
     */
    public function getDiscussedTimeRequests($cn, bool $isEmployeeUser, $attStatus, $confirmStatus) {
        $user         = auth()->user();
        $timeRequests = TimeRequest::on($cn)->where(TimeRequest::UPDATED_BY, $user->id);
        // ->whereHas(TimeRequest::RELATION_TIME_REQUEST_DTLS, function (Builder $query) use ($user, $isEmployeeUser) {
            // if ($isEmployeeUser)
            //     $query->where(TimeRequestDtl::EMPLOYEE_USER_ID, $user->id);
            // else 
            //     $query->where(TimeRequestDtl::USER_ID, $user->id);
        // });
        if ($attStatus)
            $timeRequests = $timeRequests->where(TimeRequest::ATT_STATUS, $attStatus);
        if ($confirmStatus)
            $timeRequests = $timeRequests->where(TimeRequest::CONFIRM_STATUS, $confirmStatus);
        $timeRequests = $timeRequests->orderByDesc(TimeRequest::CREATED_AT)->get();
        return $timeRequests;
    }

    /**
     * Тухайн ажилтаны шийдвэрлээгүй хүсэлтүүд
     */
    public function getUnDiscussedTimeRequests($cn, $attStatus, $limit, $page = 1) {
        $resultTimeRequests     = [];
        $requestConfigs         = resolve(RequestConfigService::class)->getRequestConfigs();
        $requestConfigs->each(function ($requestConfig) use ($cn, $attStatus, $limit, &$resultTimeRequests) {
            $departmentId           = $requestConfig->department_id;
            $requestConfigEmployees = $requestConfig->request_config_employees;
            $requestConfig->request_config_dtls->each(function ($requestConfigDtl) use ($cn, $departmentId, $requestConfigEmployees, $attStatus, &$resultTimeRequests) {
                $limitHour = $requestConfigDtl->limit_hours;
                $attStatus = $requestConfigDtl->employee_attendance_status;
                $timeRequestsQuery = TimeRequest::on($cn)
                                    ->where(TimeRequest::CONFIRM_STATUS, TimeRequest::CONFIRM_STATUS_SENT)
                                    // ->where(TimeRequest::UPDATED_BY, null)
                                    ->where(TimeRequest::ATT_STATUS, $attStatus);
                if ($requestConfigEmployees->isNotEmpty()) {
                    $timeRequestsQuery = $timeRequestsQuery->whereIn(TimeRequest::EMPLOYEE_ID, $requestConfigEmployees->pluck('employee_id'));
                } else {
                    $timeRequestsQuery = $timeRequestsQuery->whereHas(TimeRequest::RELATION_EMPLOYEE, function (Builder $query) use ($departmentId) {
                        $query->where(Employee::DEPARTMENT_ID, $departmentId);
                    });    
                }
                $timeRequests = $timeRequestsQuery->get();
                $timeRequests->each(function ($timeRequest) use ($limitHour, &$resultTimeRequests) {
                    if ($timeRequest->diffHours > $limitHour) 
                        return;
                    $resultTimeRequests[] = $timeRequest;
                });
            });
        });
        $collection = collect($resultTimeRequests);
        $currentPageItems = $collection->slice(($page - 1) * $limit, $limit)->values();
    
        return new \Illuminate\Pagination\LengthAwarePaginator(
            $currentPageItems,
            $collection->count(),
            $limit,
            $page,
            ['path' => \Illuminate\Pagination\Paginator::resolveCurrentPath()]
        );
    }
}
