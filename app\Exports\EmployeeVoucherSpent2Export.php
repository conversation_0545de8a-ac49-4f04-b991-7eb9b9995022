<?php

namespace App\Exports;

use App\Models\Customer\Voucher\EmployeeVoucherSpent;
use Maatwebsite\Excel\Concerns\{
    FromCollection,
    WithHeadings,
    WithMapping,
    WithStyles,
    ShouldAutoSize
};
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class EmployeeVoucherSpent2Export implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $collection;
    protected int $rowNumber = 0;

    public function __construct($collection)
    {
        $this->collection = $collection;
    }

    public function title(): string
    {
        return 'Employee Voucher Spent';
    }

    public function collection()
    {
        $this->rowNumber = 0;
        return $this->collection;
    }

    public function headings(): array
    {
        return [
            '№',
            'Овог нэр',
            'Хэлтэс',
            'Албан тушаал',
            'Mерчант компани нэр',
            'Mерчантын байршил',
            'Мерчант ажилтаны хэлтэс',
            'Мерчант ажилтаны албан тушаал',
            'Мерчант ажилтаны нэр',
            'Огноо',
            'Нийт дүн'  
        ];
    }

    public function map($record): array
    {
        return [
            ++$this->rowNumber,
            $record?->employee?->full_name ?? '',
            $record?->employee?->department?->name ?? '',
            $record?->employee?->position?->name ?? '',
            $record?->merchant_company_name ?? '',
            $record?->merchant_location_name ?? '',
            $record?->merchant_employee_department_name ?? '',
            $record?->merchant_employee_position_name ?? '',
            $record?->merchant_employee_display_name ?? '',
            $record->created_at->format('Y-m-d H:i:s'),
            $record->amount
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                    'vertical'   => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                ],
                'fill' => [
                    'fillType'   => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F3F3F3'],
                ],
            ],
        ];
    }
}
