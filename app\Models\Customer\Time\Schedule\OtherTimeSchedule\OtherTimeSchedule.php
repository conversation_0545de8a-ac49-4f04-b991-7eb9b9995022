<?php

namespace App\Models\Customer\Time\Schedule\OtherTimeSchedule;

use App\Models\Customer\Time\Schedule\TimeSchedule;
use App\Models\Customer\Time\Schedule\TimeScheduleEmployee;
use App\Http\Tools\DateTool;
use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class OtherTimeSchedule extends TimeSchedule
{
    use HasFactory;
    const TABLE = 'other_time_schedules';

    const CREATER_NAME    = 'creater_name';

    public function __construct($attributes = array()) {
        $this->connection             = ConnectionService::connectionName();
        $attributes[self::HASH_STR]   = '';
        parent::__construct($attributes);
    }

    public function time_schedule_dtls(): HasMany {
        return $this->hasMany(OtherTimeScheduleDtl::class);
    }

    public function time_schedule_employees(): HasMany {
        return $this->hasMany(TimeScheduleEmployee::class, 'time_schedule_id')->where(TimeScheduleEmployee::TYPE, self::TYPE_OTHER);
    }

    public function getOtherTimeScheduleDtlByDate($salaryDate) {
        $dateOfWeek      = DateTool::getDateOfWeek($salaryDate);
        $timeScheduleDtl = collect($this->time_schedule_dtls)->first(function ($value, $key) use ($dateOfWeek) {
            return $value->day_of_week == $dateOfWeek;
        });
        return $timeScheduleDtl;
    }

    public function getDefaultOtherTimeScheduleDtls() {
        $timeScheduleDtlDtls = [];
        for ($i=1; $i <= 7; $i++) {
            $isHoliday = $i == 6 || $i == 7;
            $timeScheduleDtlDtl = new OtherTimeScheduleDtl([
                OtherTimeScheduleDtl::TYPE           => $isHoliday ? OtherTimeScheduleDtl::TYPE_HOLIDAY : OtherTimeScheduleDtl::TYPE_SIMPLE,
                OtherTimeScheduleDtl::DAY_OF_WEEK    => $i,
                OtherTimeScheduleDtl::ADMIT_LATE_MIN => 0,
                OtherTimeScheduleDtl::HAS_BREAK_TIME => !$isHoliday,
                OtherTimeScheduleDtl::BEGIN_TIME_STR => $isHoliday ? '00:00' : '09:00',
                OtherTimeScheduleDtl::END_TIME_STR   => $isHoliday ? '00:00' : '18:00',
            ]);
            $timeScheduleDtlDtls[] = $timeScheduleDtlDtl;
        }
        return $timeScheduleDtlDtls;
    }

    public function getEmployeeCountAttribute() {
        return $this->time_schedule_employees->count();
    }
}
