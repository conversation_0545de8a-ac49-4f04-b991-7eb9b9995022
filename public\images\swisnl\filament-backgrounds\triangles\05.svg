<svg xmlns='http://www.w3.org/2000/svg' width='1920' height='1080'><path d='M960.2,540L1314.6,540.4L1314.3,185.5Z' fill='rgb(87,173,191)' shape-rendering='crispEdges'></path><path d='M1314.3,185.5L959.4,185L960.2,540Z' fill='rgb(129,194,189)' shape-rendering='crispEdges'></path><path d='M960.2,540L1315,894.2L1314.6,540.4Z' fill='rgb(33,148,192)' shape-rendering='crispEdges'></path><path d='M1314.6,540.4L1668.9,540.2L1314.3,185.5Z' fill='rgb(87,147,179)' shape-rendering='crispEdges'></path><path d='M1315,894.2L1668.9,540.2L1314.6,540.4Z' fill='rgb(38,122,181)' shape-rendering='crispEdges'></path><path d='M1668.9,540.2L1669.1,185.4L1314.3,185.5Z' fill='rgb(123,147,172)' shape-rendering='crispEdges'></path><path d='M1314.3,185.5L960.2,-169.1L959.4,185Z' fill='rgb(165,207,197)' shape-rendering='crispEdges'></path><path d='M959.4,185L605.6,539.6L960.2,540Z' fill='rgb(111,198,190)' shape-rendering='crispEdges'></path><path d='M960.2,540L959.5,894.7L1315,894.2Z' fill='rgb(46,135,184)' shape-rendering='crispEdges'></path><path d='M606,894.2L959.5,894.7L960.2,540Z' fill='rgb(75,149,183)' shape-rendering='crispEdges'></path><path d='M1315.2,-169.7L960.2,-169.1L1314.3,185.5Z' fill='rgb(164,199,205)' shape-rendering='crispEdges'></path><path d='M959.4,185L604.4,185.8L605.6,539.6Z' fill='rgb(156,215,185)' shape-rendering='crispEdges'></path><path d='M1668.8,-168.8L1315.2,-169.7L1314.3,185.5Z' fill='rgb(159,171,194)' shape-rendering='crispEdges'></path><path d='M605.7,-170.4L604.4,185.8L959.4,185Z' fill='rgb(190,228,192)' shape-rendering='crispEdges'></path><path d='M251.2,539.3L606,894.2L605.6,539.6Z' fill='rgb(135,190,188)' shape-rendering='crispEdges'></path><path d='M605.6,539.6L606,894.2L960.2,540Z' fill='rgb(91,177,190)' shape-rendering='crispEdges'></path><path d='M1669.1,185.4L1668.8,-168.8L1314.3,185.5Z' fill='rgb(155,158,180)' shape-rendering='crispEdges'></path><path d='M1315,894.2L1670.3,894.9L1668.9,540.2Z' fill='rgb(38,91,166)' shape-rendering='crispEdges'></path><path d='M1668.9,540.2L2024,539.9L1669.1,185.4Z' fill='rgb(82,118,155)' shape-rendering='crispEdges'></path><path d='M1669.1,185.4L2024,184.9L1668.8,-168.8Z' fill='rgb(145,139,161)' shape-rendering='crispEdges'></path><path d='M1669.8,1249.5L1670.3,894.9L1315,894.2Z' fill='rgb(31,57,138)' shape-rendering='crispEdges'></path><path d='M960.8,-524.9L605.7,-170.4L960.2,-169.1Z' fill='rgb(183,224,204)' shape-rendering='crispEdges'></path><path d='M960.2,-169.1L605.7,-170.4L959.4,185Z' fill='rgb(183,224,204)' shape-rendering='crispEdges'></path><path d='M604.4,185.8L251.2,539.3L605.6,539.6Z' fill='rgb(161,217,184)' shape-rendering='crispEdges'></path><path d='M250.8,185L251.2,539.3L604.4,185.8Z' fill='rgb(200,233,180)' shape-rendering='crispEdges'></path><path d='M1670.3,894.9L2024,539.9L1668.9,540.2Z' fill='rgb(39,94,156)' shape-rendering='crispEdges'></path><path d='M960.5,1250.3L1314.5,1249.5L959.5,894.7Z' fill='rgb(37,93,140)' shape-rendering='crispEdges'></path><path d='M959.5,894.7L1314.5,1249.5L1315,894.2Z' fill='rgb(36,91,154)' shape-rendering='crispEdges'></path><path d='M2024,539.9L2024,184.9L1669.1,185.4Z' fill='rgb(105,121,137)' shape-rendering='crispEdges'></path><path d='M250.9,-170.3L250.8,185L604.4,185.8Z' fill='rgb(232,246,188)' shape-rendering='crispEdges'></path><path d='M605.7,1250.2L959.5,894.7L606,894.2Z' fill='rgb(84,118,153)' shape-rendering='crispEdges'></path><path d='M605.7,1250.2L960.5,1250.3L959.5,894.7Z' fill='rgb(62,106,139)' shape-rendering='crispEdges'></path><path d='M1668.8,-168.8L1314.6,-524.3L1315.2,-169.7Z' fill='rgb(159,171,194)' shape-rendering='crispEdges'></path><path d='M1315.2,-169.7L960.8,-524.9L960.2,-169.1Z' fill='rgb(168,208,206)' shape-rendering='crispEdges'></path><path d='M1670.5,-525.2L1314.6,-524.3L1668.8,-168.8Z' fill='rgb(158,159,190)' shape-rendering='crispEdges'></path><path d='M1314.6,-524.3L960.8,-524.9L1315.2,-169.7Z' fill='rgb(164,199,205)' shape-rendering='crispEdges'></path><path d='M1669.6,1604.1L1669.8,1249.5L1314.5,1249.5Z' fill='rgb(23,51,122)' shape-rendering='crispEdges'></path><path d='M1314.5,1249.5L1669.8,1249.5L1315,894.2Z' fill='rgb(22,61,127)' shape-rendering='crispEdges'></path><path d='M1670.3,894.9L2024.9,894.9L2024,539.9Z' fill='rgb(25,67,132)' shape-rendering='crispEdges'></path><path d='M-103.7,539.3L250.5,894.6L251.2,539.3Z' fill='rgb(161,199,195)' shape-rendering='crispEdges'></path><path d='M251.2,539.3L250.5,894.6L606,894.2Z' fill='rgb(142,170,179)' shape-rendering='crispEdges'></path><path d='M250.5,894.6L605.7,1250.2L606,894.2Z' fill='rgb(121,130,151)' shape-rendering='crispEdges'></path><path d='M2024.4,1249.4L2024.9,894.9L1670.3,894.9Z' fill='rgb(16,35,104)' shape-rendering='crispEdges'></path><path d='M2024,539.9L2378.6,185.4L2024,184.9Z' fill='rgb(104,120,135)' shape-rendering='crispEdges'></path><path d='M605.7,-170.4L250.9,-170.3L604.4,185.8Z' fill='rgb(226,244,198)' shape-rendering='crispEdges'></path><path d='M250.8,185L-103.7,539.3L251.2,539.3Z' fill='rgb(187,227,192)' shape-rendering='crispEdges'></path><path d='M604.3,-523.9L250.9,-170.3L605.7,-170.4Z' fill='rgb(226,244,198)' shape-rendering='crispEdges'></path><path d='M960.8,-524.9L604.3,-523.9L605.7,-170.4Z' fill='rgb(194,230,202)' shape-rendering='crispEdges'></path><path d='M2378.6,-169.8L2024.8,-170.5L2024,184.9Z' fill='rgb(137,132,152)' shape-rendering='crispEdges'></path><path d='M2024,184.9L2024.8,-170.5L1668.8,-168.8Z' fill='rgb(138,133,154)' shape-rendering='crispEdges'></path><path d='M2024.8,-170.5L1670.5,-525.2L1668.8,-168.8Z' fill='rgb(148,140,170)' shape-rendering='crispEdges'></path><path d='M1314.6,-524.3L1670.5,-525.2L960.8,-524.9Z' fill='rgb(161,185,200)' shape-rendering='crispEdges'></path><path d='M960.5,1250.3L1314.4,1603.8L1314.5,1249.5Z' fill='rgb(27,85,138)' shape-rendering='crispEdges'></path><path d='M960.2,1603.8L1314.4,1603.8L960.5,1250.3Z' fill='rgb(37,93,140)' shape-rendering='crispEdges'></path><path d='M605.7,1250.2L960.2,1603.8L960.5,1250.3Z' fill='rgb(62,106,139)' shape-rendering='crispEdges'></path><path d='M2024.9,894.9L2378.6,540.4L2024,539.9Z' fill='rgb(29,86,139)' shape-rendering='crispEdges'></path><path d='M2378.6,540.4L2378.6,185.4L2024,539.9Z' fill='rgb(73,110,138)' shape-rendering='crispEdges'></path><path d='M-103.9,185.3L-103.7,539.3L250.8,185Z' fill='rgb(219,240,198)' shape-rendering='crispEdges'></path><path d='M2024.9,894.9L2378.8,894.8L2378.6,540.4Z' fill='rgb(24,66,130)' shape-rendering='crispEdges'></path><path d='M1669.8,1249.5L2024.4,1249.4L1670.3,894.9Z' fill='rgb(16,35,104)' shape-rendering='crispEdges'></path><path d='M2024.4,1604.1L2024.4,1249.4L1669.8,1249.5Z' fill='rgb(9,30,90)' shape-rendering='crispEdges'></path><path d='M-104.7,-169L-103.9,185.3L250.8,185Z' fill='rgb(250,253,206)' shape-rendering='crispEdges'></path><path d='M250.5,894.6L250.8,1250L605.7,1250.2Z' fill='rgb(121,126,134)' shape-rendering='crispEdges'></path><path d='M605.7,1250.2L605.3,1604.8L960.2,1603.8Z' fill='rgb(76,111,137)' shape-rendering='crispEdges'></path><path d='M-103.7,1249L250.8,1250L250.5,894.6Z' fill='rgb(134,131,142)' shape-rendering='crispEdges'></path><path d='M2378.6,-169.8L2024.7,-523.9L2024.8,-170.5Z' fill='rgb(137,132,152)' shape-rendering='crispEdges'></path><path d='M2024.8,-170.5L2024.7,-523.9L1670.5,-525.2Z' fill='rgb(138,133,154)' shape-rendering='crispEdges'></path><path d='M1670.5,-525.2L250.5,-525L960.8,-524.9Z' fill='rgb(173,218,207)' shape-rendering='crispEdges'></path><path d='M960.8,-524.9L250.5,-525L604.3,-523.9Z' fill='rgb(210,237,200)' shape-rendering='crispEdges'></path><path d='M604.3,-523.9L250.5,-525L250.9,-170.3Z' fill='rgb(236,248,198)' shape-rendering='crispEdges'></path><path d='M250.9,-170.3L-104.7,-169L250.8,185Z' fill='rgb(250,253,206)' shape-rendering='crispEdges'></path><path d='M605.3,1604.8L1669.6,1604.1L1314.4,1603.8Z' fill='rgb(27,85,138)' shape-rendering='crispEdges'></path><path d='M1314.4,1603.8L1669.6,1604.1L1314.5,1249.5Z' fill='rgb(22,61,127)' shape-rendering='crispEdges'></path><path d='M2378.7,1248.9L2378.8,894.8L2024.9,894.9Z' fill='rgb(15,35,102)' shape-rendering='crispEdges'></path><path d='M2378.6,540.4L2379.4,-524.9L2378.6,185.4Z' fill='rgb(134,131,143)' shape-rendering='crispEdges'></path><path d='M2379.4,-524.9L2378.6,-169.8L2378.6,185.4Z' fill='rgb(137,132,152)' shape-rendering='crispEdges'></path><path d='M2378.6,185.4L2378.6,-169.8L2024,184.9Z' fill='rgb(134,131,143)' shape-rendering='crispEdges'></path><path d='M-460.1,540.7L-104.5,894.3L-103.7,539.3Z' fill='rgb(165,201,206)' shape-rendering='crispEdges'></path><path d='M-103.7,539.3L-104.5,894.3L250.5,894.6Z' fill='rgb(160,177,196)' shape-rendering='crispEdges'></path><path d='M250.8,1250L605.3,1604.8L605.7,1250.2Z' fill='rgb(111,123,135)' shape-rendering='crispEdges'></path><path d='M960.2,1603.8L605.3,1604.8L1314.4,1603.8Z' fill='rgb(45,101,141)' shape-rendering='crispEdges'></path><path d='M-103.7,-524.4L-104.7,-169L250.9,-170.3Z' fill='rgb(254,255,216)' shape-rendering='crispEdges'></path><path d='M-103.9,185.3L-460,185.4L-103.7,539.3Z' fill='rgb(219,241,199)' shape-rendering='crispEdges'></path><path d='M2024.4,1249.4L2378.7,1248.9L2024.9,894.9Z' fill='rgb(8,29,88)' shape-rendering='crispEdges'></path><path d='M2024.4,1604.1L2378.7,1248.9L2024.4,1249.4Z' fill='rgb(8,29,88)' shape-rendering='crispEdges'></path><path d='M-104.5,894.3L-103.7,1249L250.5,894.6Z' fill='rgb(147,139,167)' shape-rendering='crispEdges'></path><path d='M250.8,1250L250,1605.2L605.3,1604.8Z' fill='rgb(121,126,134)' shape-rendering='crispEdges'></path><path d='M2378.9,1605.1L2024.4,1604.1L1669.6,1604.1Z' fill='rgb(8,29,88)' shape-rendering='crispEdges'></path><path d='M1669.6,1604.1L2024.4,1604.1L1669.8,1249.5Z' fill='rgb(16,35,104)' shape-rendering='crispEdges'></path><path d='M250.5,-525L-103.7,-524.4L250.9,-170.3Z' fill='rgb(250,253,206)' shape-rendering='crispEdges'></path><path d='M2378.8,894.8L2379.4,-524.9L2378.6,540.4Z' fill='rgb(104,120,135)' shape-rendering='crispEdges'></path><path d='M2378.6,-169.8L2379.4,-524.9L2024.7,-523.9Z' fill='rgb(137,132,152)' shape-rendering='crispEdges'></path><path d='M2024.7,-523.9L2379.4,-524.9L1670.5,-525.2Z' fill='rgb(137,132,152)' shape-rendering='crispEdges'></path><path d='M2378.9,1605.1L2379.4,-524.9L2378.8,894.8Z' fill='rgb(29,86,139)' shape-rendering='crispEdges'></path><path d='M-104.7,1604.2L250,1605.2L250.8,1250Z' fill='rgb(134,131,142)' shape-rendering='crispEdges'></path><path d='M605.3,1604.8L2378.9,1605.1L1669.6,1604.1Z' fill='rgb(23,51,122)' shape-rendering='crispEdges'></path><path d='M-459.4,-169.9L-460,185.4L-104.7,-169Z' fill='rgb(255,255,217)' shape-rendering='crispEdges'></path><path d='M-104.7,-169L-460,185.4L-103.9,185.3Z' fill='rgb(251,253,207)' shape-rendering='crispEdges'></path><path d='M-104.5,894.3L-459,894.7L-103.7,1249Z' fill='rgb(147,140,168)' shape-rendering='crispEdges'></path><path d='M-460,185.4L-460.1,540.7L-103.7,539.3Z' fill='rgb(191,229,203)' shape-rendering='crispEdges'></path><path d='M-460.1,540.7L-459,894.7L-104.5,894.3Z' fill='rgb(160,177,197)' shape-rendering='crispEdges'></path><path d='M-103.7,1249L-104.7,1604.2L250.8,1250Z' fill='rgb(137,132,151)' shape-rendering='crispEdges'></path><path d='M-458.6,-524.4L-459.4,-169.9L-104.7,-169Z' fill='rgb(255,255,217)' shape-rendering='crispEdges'></path><path d='M250,1605.2L2378.9,1605.1L605.3,1604.8Z' fill='rgb(37,93,140)' shape-rendering='crispEdges'></path><path d='M2024.4,1604.1L2378.9,1605.1L2378.7,1248.9Z' fill='rgb(8,29,88)' shape-rendering='crispEdges'></path><path d='M2378.7,1248.9L2378.9,1605.1L2378.8,894.8Z' fill='rgb(8,29,88)' shape-rendering='crispEdges'></path><path d='M-459.5,1248.9L-104.7,1604.2L-103.7,1249Z' fill='rgb(137,132,152)' shape-rendering='crispEdges'></path><path d='M250.5,-525L-458.6,-524.4L-103.7,-524.4Z' fill='rgb(255,255,217)' shape-rendering='crispEdges'></path><path d='M-103.7,-524.4L-458.6,-524.4L-104.7,-169Z' fill='rgb(255,255,217)' shape-rendering='crispEdges'></path><path d='M-460.1,540.7L-459.5,1248.9L-459,894.7Z' fill='rgb(158,156,188)' shape-rendering='crispEdges'></path><path d='M-459,894.7L-459.5,1248.9L-103.7,1249Z' fill='rgb(137,132,152)' shape-rendering='crispEdges'></path><path d='M-460.1,540.7L-459.9,1605L-459.5,1248.9Z' fill='rgb(137,132,152)' shape-rendering='crispEdges'></path><path d='M-459.5,1248.9L-459.9,1605L-104.7,1604.2Z' fill='rgb(137,132,152)' shape-rendering='crispEdges'></path><path d='M-104.7,1604.2L-459.9,1605L250,1605.2Z' fill='rgb(137,132,152)' shape-rendering='crispEdges'></path></svg>