<?php

namespace App\Http\Controllers;

use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Laravel\Firebase\Facades\Firebase;

class PushNotificationController extends Controller
{
    protected $notification;
    public function __construct()
    {
        $this->notification = Firebase::messaging();
    }

    public function sendPushNotification()
    {
        $message = CloudMessage::fromArray([
            'notification' => [
                'title' => 'Test Notification Title',
                'body'  => 'Test Notification Body',
            ],
            'topic' => 'production-user-70',
        ]);
        $this->notification->send($message);
        return response()->json(['message' => 'Push notification sent successfully']);
    }
}
