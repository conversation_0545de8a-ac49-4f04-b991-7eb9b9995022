<?php

namespace App\Models\Customer;

use App\Models\Customer\Time\Attendance\RawAttendance;
use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeDtl extends Model
{
    use HasFactory;
    const TABLE           = 'employee_dtls';

    const ID                        = 'id';
    const EMPLOYEE_ID               = 'employee_id';
    const COMPANY_WORK_DATE         = 'company_work_date';
    const ATTENDANCE_CODE           = 'attendance_code';
    const IS_SPECIAL                = 'is_special';
    const IS_LOCATION_ANYWHERE      = 'is_location_anywhere';
    const UNIQ_ID                   = 'uniq_id';
    const EXPIRED_AT                = 'expired_at';
    const CREATED_BY                = 'created_by';
    const UPDATED_BY                = 'updated_by';

    const RELATION_EMPLOYEE         = 'employee';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::ID,
        self::COMPANY_WORK_DATE,
        self::ATTENDANCE_CODE,
        self::IS_SPECIAL,
        self::IS_LOCATION_ANYWHERE,
        self::UNIQ_ID,
        
        self::EXPIRED_AT,
        self::CREATED_BY,
        self::UPDATED_BY,
    ];

    protected $casts = [
        self::IS_SPECIAL            => 'boolean',
        self::IS_LOCATION_ANYWHERE  => 'boolean',
    ];

    public function employee() {
        return $this->belongsTo(Employee::class);
    }

    public function raw_attendances() {
        return $this->hasMany(RawAttendance::class, 'attendance_code', 'attendance_code')->orderBy(RawAttendance::REGISTER_DATE, 'asc');
    }
}
