# Time Request Begin Date Validation

## Overview

The Time Request validation system provides centralized logic to validate `begin_date` based on `att_status` and Configuration settings. This ensures that time requests are submitted within allowed time windows based on the type of request.

## How It Works

### 1. Configuration Setup

For each attendance status that requires validation, create a Configuration record with:
- `config_id`: A unique identifier following the pattern `TIME_REQUEST_{STATUS}_MIN_DATE`
- `value1`: An anchor formula defining the minimum allowed date

### 2. Anchor Formula Format

The `value1` field uses the ChronoAnchor formula format:
```
anchor,{date_source},{time_unit},{shift_count}
```

**Examples:**
- `anchor,now,day,3` - Minimum date is 3 days from now
- `anchor,2024-01-01,day,5` - Minimum date is 5 days from January 1, 2024
- `anchor,now,month,-1` - Minimum date is 1 month before now (past date)

**Supported time units:**
- `minute` - Minutes
- `hour` - Hours  
- `halfday` - Half days (12 hours)
- `day` - Days
- `month` - Months
- `quarter` - Quarters (3 months)
- `halfyear` - Half years (6 months)
- `year` - Years

### 3. Configuration IDs by Attendance Status

The system uses the existing `EmployeeAttendanceDtl::getAttStatusId()` method to map attendance statuses to configuration IDs using the pattern: `TIME_REQUEST_{ATT_STATUS_ID}_MIN_DATE`

**Examples:**
- ATTENDANCE_STATUS_WORK (3) → `ATTENDANCE_STATUS_WORK` → `TIME_REQUEST_ATTENDANCE_STATUS_WORK_MIN_DATE`
- ATTENDANCE_STATUS_FURLOUGH (8) → `ATTENDANCE_STATUS_FURLOUGH` → `TIME_REQUEST_ATTENDANCE_STATUS_FURLOUGH_MIN_DATE`
- ATTENDANCE_STATUS_SICK (13) → `ATTENDANCE_STATUS_SICK` → `TIME_REQUEST_ATTENDANCE_STATUS_SICK_MIN_DATE`

This approach ensures consistency with the existing codebase and automatically supports all attendance statuses defined in `EmployeeAttendanceDtl`.

## Example Configurations

### Example 1: Work requests must be submitted at least 3 days in advance
```sql
INSERT INTO configurations (config_id, value1)
VALUES ('TIME_REQUEST_ATTENDANCE_STATUS_WORK_MIN_DATE', 'anchor,now,day,3');
```

### Example 2: Sick leave can be submitted up to 7 days in the past
```sql
INSERT INTO configurations (config_id, value1)
VALUES ('TIME_REQUEST_ATTENDANCE_STATUS_SICK_MIN_DATE', 'anchor,now,day,-7');
```

### Example 3: Vacation requests must be submitted at least 1 month in advance
```sql
INSERT INTO configurations (config_id, value1)
VALUES ('TIME_REQUEST_ATTENDANCE_STATUS_VACATION_MIN_DATE', 'anchor,now,month,1');
```

### Example 4: Furlough requests from a specific date
```sql
INSERT INTO configurations (config_id, value1)
VALUES ('TIME_REQUEST_ATTENDANCE_STATUS_FURLOUGH_MIN_DATE', 'anchor,2024-01-01,day,0');
```

## Validation Behavior

### Success Cases
- No configuration exists for the attendance status → Request allowed
- Configuration exists but `value1` is empty → Request allowed  
- Configuration formula is invalid → Request allowed (graceful fallback)
- `begin_date` is greater than or equal to calculated minimum date → Request allowed

### Failure Cases
- `begin_date` is less than the calculated minimum date → Request rejected with error message

### Error Messages
Error messages are displayed in Mongolian and include:
- The attendance status name
- The minimum allowed date
- Example: "Эхлэх огноо Ажилласан-ийн хувьд 2024-01-13-ээс эрт байж болохгүй."

## Implementation Details

The validation service leverages existing methods from `EmployeeAttendanceDtl`:
- **`getAttStatusId()`** - Maps attendance status integers to string identifiers
- **`getAttStatusName()`** - Gets localized status names for error messages

This approach ensures consistency with the existing codebase and automatically supports all attendance statuses.

### Files Modified/Created
1. `app/Services/Time/TimeRequestValidationService.php` - Main validation service
2. `app/Http/Requests/Customer/Time/TimeRequest/TimeRequestRequest.php` - Added validation rule
3. `app/Http/Controllers/Customer/Time/TimeRequestController.php` - Added server-side validation
4. `tests/Unit/Services/Time/TimeRequestValidationServiceTest.php` - Unit tests

### Validation Points
1. **Form Request Validation** - Client-side validation in `TimeRequestRequest`
2. **Controller Validation** - Server-side validation in `TimeRequestController::store()`

### Error Handling
- Graceful fallback: If any error occurs during validation, the request is allowed
- Errors are logged for debugging
- Invalid configurations don't block requests

## Testing

Run the validation tests:
```bash
php artisan test tests/Unit/Services/Time/TimeRequestValidationServiceTest.php
```

## Usage in Other Contexts

The `TimeRequestValidationService` can be used independently:

```php
$validationService = resolve(TimeRequestValidationService::class);
$result = $validationService->validateBeginDate('2024-01-15', 3);

if (!$result['valid']) {
    // Handle validation error
    echo $result['message'];
}
```
