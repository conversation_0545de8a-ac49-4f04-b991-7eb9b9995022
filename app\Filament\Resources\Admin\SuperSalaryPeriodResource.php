<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Models\User;
use App\Models\Constant\ConstData;
use App\Models\Customer\Time\SuperSalaryPeriod\SuperSalaryPeriod;
use App\Filament\Resources\Admin\SuperSalaryPeriodResource\Pages;
use App\Filament\Resources\Admin\SuperSalaryPeriodResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class SuperSalaryPeriodResource extends Can
{
    protected static ?string $model = SuperSalaryPeriod::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Цалингийн үечлэл';
    protected static ?string $modelLabel = 'Цалингийн үечлэл';
    protected static ?int $navigationSort = 6;
    protected static ?string $navigationGroup = 'Цалингийн үечлэл';
    protected static ?string $slug = 'super_salary_periods';

    public static function form(Form $form): Form
    {
        return $form
                ->schema([
                    Forms\Components\Section::make()
                        ->schema([
                            Forms\Components\TextInput::make(SuperSalaryPeriod::YEAR)
                                ->label('Жил')
                                ->numeric()
                                ->inputMode('decimal')
                                ->default(now()->year)
                                ->minValue(2020)
                                ->maxValue(2100)
                                ->required(),

                            Forms\Components\TextInput::make(SuperSalaryPeriod::MONTH)
                                ->label('Сар')
                                ->numeric()
                                ->inputMode('decimal')
                                ->default(now()->month)
                                ->minValue(1)
                                ->maxValue(12)
                                ->required(),
                        ])
                        ->columns(1)
                        ->columnSpan(['lg' => fn (?SuperSalaryPeriod $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                        ->label('Үүссэн огноо')
                        ->content(fn (SuperSalaryPeriod $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('created_by')
                            ->label('Үүсгэсэн хэрэглэгч')
                            ->content(fn (SuperSalaryPeriod $record): ?string => User::find($record->created_by)->name),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Өөрчилсөн огноо')
                            ->content(fn (SuperSalaryPeriod $record): ?string => $record->updated_at ? $record->updated_at?->diffForHumans(): ConstData::BAIHGUI),

                        Forms\Components\Placeholder::make('updated_by')
                            ->label('Өөрчилсөн хэрэглэгч')
                            ->content(fn (SuperSalaryPeriod $record): ?string => $record->updated_by ? User::find($record->updated_by)->name : ConstData::BAIHGUI),
                        ])->columns(2)
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?SuperSalaryPeriod $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('year')->label('Жил')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('month')->label('Сар')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\SuperMainSalaryPeriodDtlRelationManager::class,
            RelationManagers\SuperSalaryPeriodDtlsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSuperSalaryPeriods::route('/'),
            'create' => Pages\CreateSuperSalaryPeriod::route('/create'),
            'edit' => Pages\EditSuperSalaryPeriod::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->orderByDesc('year')->orderByDesc('month');
    }
}
