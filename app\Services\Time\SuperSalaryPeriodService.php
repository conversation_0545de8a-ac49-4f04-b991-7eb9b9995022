<?php

namespace App\Services\Time;

use App\Models\Constant\ConstData;
use App\Models\Customer\Time\SuperSalaryPeriod\SuperSalaryPeriod;
use App\Models\Customer\Time\SuperSalaryPeriod\SuperSalaryPeriodDtl;
use App\Models\Customer\Time\SuperSalaryPeriod\SuperSalaryPeriodSubOneDtl;
use App\Models\Customer\Time\SuperSalaryPeriod\SuperSalaryPeriodSubTwoDtl;

use App\Http\Tools\DateTool;
use Illuminate\Database\Eloquent\Builder;
use Kreait\Firebase\Database\Query\Sorter\OrderByKey;

class SuperSalaryPeriodService
{
    public function getSuperSalaryPeriodsWithPluck($cn) {
        return SuperSalaryPeriod::on($cn)->orderByDesc('year')->orderByDesc('month')->get()->pluck(ConstData::NAME, ConstData::ID);
    }
    public function getSuperSalaryPeriodByYearAndMonth($cn, $year, $month) {
        return SuperSalaryPeriod::on($cn)
                ->where(SuperSalaryPeriod::YEAR, $year)
                ->where(SuperSalaryPeriod::MONTH, $month)
                ->first();
    }

    public function getSuperSalaryPeriodDtlByNowDate($cn, $departmentId) {
        $nowDate              = DateTool::nowDate();
        $SuperSalaryPeriodDtl = SuperSalaryPeriodDtl::on($cn)
                ->where(SuperSalaryPeriodDtl::TYPE, 1)
                ->whereHas(SuperSalaryPeriodDtl::RELATION_SUPER_SALARY_PERIOD_SUB_ONE_DTLS, function (Builder $query) use ($departmentId) {
                    $query->where(SuperSalaryPeriodSubOneDtl::DEPARTMENT_ID, $departmentId);
                })
                ->whereHas(SuperSalaryPeriodDtl::RELATION_SUPER_SALARY_PERIOD_SUB_TWO_DTLS, function (Builder $query) use ($nowDate) {
                    $query->where(SuperSalaryPeriodSubTwoDtl::BEGIN_DATE, '<=', $nowDate)
                          ->where(SuperSalaryPeriodSubTwoDtl::END_DATE, '>=', $nowDate);
                })
                ->first();

        if (isset($SuperSalaryPeriodDtl))
            return $SuperSalaryPeriodDtl;

        return SuperSalaryPeriodDtl::on($cn)->where(SuperSalaryPeriodDtl::TYPE, 0)
            ->whereHas(SuperSalaryPeriodDtl::RELATION_SUPER_SALARY_PERIOD_SUB_TWO_DTLS, function (Builder $query) use ($nowDate) {
                $query->where(SuperSalaryPeriodSubTwoDtl::BEGIN_DATE, '<=', $nowDate)
                      ->where(SuperSalaryPeriodSubTwoDtl::END_DATE, '>=', $nowDate);
            })
            ->first();

    }

    public function getSuperSalaryPeriodDtlByYearMonth($cn, $year, $month, $departmentId) {
        $nowDate              = DateTool::nowDate();
        $SuperSalaryPeriodDtl = SuperSalaryPeriodDtl::on($cn)
                ->where(SuperSalaryPeriodDtl::TYPE, 1)
                ->whereHas(SuperSalaryPeriodDtl::RELATION_SUPER_SALARY_PERIOD, function (Builder $query) use ($year, $month) {
                    $query->where(SuperSalaryPeriod::YEAR, $year)
                          ->where(SuperSalaryPeriod::MONTH, $month);
                })
                ->whereHas(SuperSalaryPeriodDtl::RELATION_SUPER_SALARY_PERIOD_SUB_ONE_DTLS, function (Builder $query) use ($departmentId) {
                    $query->where(SuperSalaryPeriodSubOneDtl::DEPARTMENT_ID, $departmentId);
                })
                ->first();

        if (isset($SuperSalaryPeriodDtl))
            return $SuperSalaryPeriodDtl;

        return SuperSalaryPeriodDtl::on($cn)->where(SuperSalaryPeriodDtl::TYPE, 0)
                    ->whereHas(SuperSalaryPeriodDtl::RELATION_SUPER_SALARY_PERIOD, function (Builder $query) use ($year, $month) {
                        $query->where(SuperSalaryPeriod::YEAR, $year)
                              ->where(SuperSalaryPeriod::MONTH, $month);
                    })->first();
    }
}
