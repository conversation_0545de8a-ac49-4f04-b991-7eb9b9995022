<?php

namespace App\Models\Customer;

use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserMerchant extends Model
{
    use HasFactory;

    protected $table = 'user_merchant';

    const USER_ID     = 'user_id';
    const MERCHANT_ID = 'merchant_id';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::USER_ID,
        self::MERCHANT_ID,
    ];
}
