<?php

namespace App\Models;

use App\Models\Constant\ConstData;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @mixin IdeHelperDistrict
 */
class District extends Model
{
    use HasFactory;
    protected $connection = ConstData::ADMIN_DB;
    const TABLE           = 'districts';

    const ID        = 'id';
    const CITY_ID   = 'city_id';
    const NAME      = 'name';

    public function horoos(): HasMany {
        return $this->hasMany(Khoroo::class);
    }

    public static function getDistrictsByCityId($cityId) {
        if (!isset($cityId))
            return [];
        return self::query()->where(self::CITY_ID, $cityId)->get()->pluck(self::NAME, self::ID);
    }
}
