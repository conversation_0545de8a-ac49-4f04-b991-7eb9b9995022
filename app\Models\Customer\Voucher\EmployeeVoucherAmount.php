<?php

namespace App\Models\Customer\Voucher;

use App\Models\Customer\Employee;
use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeVoucherAmount extends Model
{
    use HasFactory;

    const ID                  = 'id';
    const EMPLOYEE_ID         = 'employee_id';
    const UNUSED_TOTAL_AMOUNT = 'unused_total_amount';
    const USED_TOTAL_AMOUNT   = 'used_total_amount';

    const RELATION_EMPLOYEE = 'employee';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::EMPLOYEE_ID,
        self::UNUSED_TOTAL_AMOUNT,
        self::USED_TOTAL_AMOUNT
    ];

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function employee_vouchers()
    {
        return $this->hasMany(EmployeeVoucher::class, 'employee_id', 'employee_id');
    }

    public function getUnusedTotalAmountAttribute() {
        return $this->employee_vouchers->where(EmployeeVoucher::IS_ACTIVE, true)->sum(EmployeeVoucher::UNUSED_AMOUNT);
    }

    public function getUsedTotalAmountAttribute() {
        return $this->employee_vouchers->where(EmployeeVoucher::IS_ACTIVE, true)->sum(EmployeeVoucher::USED_AMOUNT);
    }
}
