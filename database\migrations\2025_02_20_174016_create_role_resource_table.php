<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('role_resource', function (Blueprint $table) {
            $table->id();
            $table->foreignId('role_id')->constrained()->onDelete('cascade');
            $table->string('resource'); // Resource нэрийг хадгална
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('role_resource');
    }
};
