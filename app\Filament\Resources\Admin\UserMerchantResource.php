<?php

namespace App\Filament\Resources\Admin;

use App\Models\User;
use App\Models\Class\Can;
use App\Models\Customer\Merchant;
use App\Services\ConnectionService;
use App\Services\UserService;
use App\Filament\Resources\Admin\UserMerchantResource\Pages;
use App\Filament\Resources\Admin\UserMerchantResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class UserMerchantResource extends Can
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Зарцуулалтын хандах тохиргоо';
    protected static ?string $modelLabel = 'Зарцуулалтын хандах тохиргоо';
    protected static ?int $navigationSort = 113;
    protected static ?string $navigationGroup = 'Ваучер';
    protected static ?string $slug = 'user-merchant';

    public static function form(Form $form): Form
    {
        return $form
                ->schema([
                    Forms\Components\Section::make()
                        ->schema([
                            Forms\Components\Select::make('user_id')
                                ->label('Ажилтан')
                                ->options(function () {
                                    $organizationId = auth()->user()->organizations->first()->id;
                                    return resolve(UserService::class)->getUsersWhoIsYourOrganization($organizationId)->pluck('name', 'id');
                                })
                                ->searchingMessage('Хайж байна...')
                                ->loadingMessage('Ачаалж байна байна...')
                                ->noSearchResultsMessage('Хэлтэс олдсонгүй.')
                                ->required(),
                            Forms\Components\Select::make('merchants')
                                ->label('Мерчантууд')
                                ->options(function () {
                                    $cn = ConnectionService::connectionName();  
                                    return Merchant::on($cn)->pluck('name', 'id');
                                })
                                ->multiple()
                                ->searchingMessage('Хайж байна...')
                                ->loadingMessage('Ачаалж байна байна...')
                                ->noSearchResultsMessage('Хэлтэс олдсонгүй.')
                                ->required(),
                        ])
                        ->columns(1)
                        ->columnSpan(['lg' => fn (?User $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (User $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (User $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?User $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('phone')->label('Утас')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserMerchants::route('/'),
            'create' => Pages\CreateUserMerchant::route('/create'),
            'edit' => Pages\EditUserMerchant::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $cn = ConnectionService::connectionNameByUser(auth()->user());
        return parent::getEloquentQuery()
            ->whereIn('id', function ($query) use ($cn) {
                $query->select('user_id')
                    ->from("$cn.user_merchant")
                    ->whereIn('merchant_id', Merchant::on($cn)->pluck('id'));
            });
    }
}
