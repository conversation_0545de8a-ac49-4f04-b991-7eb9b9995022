<?php

namespace App\Models\Customer\Time\Attendance;

use App\Http\Tools\DateTool;
use App\Models\Customer\Employee;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EmployeeAttendance extends Model
{
    use HasFactory;
    const TABLE = 'employee_attendances';

    const ID                            = 'id';
    const EMPLOYEE_ID                   = 'employee_id';
    const ATT_DATE                      = 'att_date';
    const IO_STRING                     = 'io_string';
    const TIME_STRING                   = 'time_string';
    const SCHEDULE_STRING               = 'schedule_string';
    const DEVICE_ATTENDANCE_STRIDS      = 'device_attendance_strids';
    const SCHEDULE_TIME                 = 'schedule_time';
    const HAS_CONFLICT                  = 'has_conflict';
    const WORK_TIME                     = 'work_time';
    const NIGHT_TIME                    = 'night_time';
    const LATE_TIME                     = 'late_time';
    const ABSENT_TIME                   = 'absent_time';
    const SICK_TIME                     = 'sick_time';
    const EFFORT_TIME                   = 'effort_time';
    const BREAK_TIME                    = 'break_time';
    const BREAK_EFFORT_TIME             = 'break_effort_time';
    const FURLOUGH_TIME                 = 'furlough_time';
    const SALARY_FURLOUGH_TIME          = 'salary_furlough_time';
    const OVER_TIME                     = 'over_time';
    const MATERNITY_TIME                = 'maternity_time';
    const VACATION_TIME                 = 'vacation_time';
    const UNKNOWN_WORK_TIME             = 'unknown_work_time';
    const UNKNOWN_EFFORT                = 'unknown_effort';
    const COMPENSATORY_REST_TIME        = 'compensatory_rest_time';
    const REQUEST_WORK_TIME             = 'request_work_time';
    const OUT_WORK_TIME                 = 'out_work_time';
    const CREATED_BY                    = 'created_by';
    const UPDATED_BY                    = 'updated_by';

    const RELATION_EMPLOYEE                 = 'employee';
    const RELATION_EMPLOYEE_ATTENDANCE_DTLS = 'employee_attendance_dtls';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::ID,
        self::EMPLOYEE_ID,
        self::ATT_DATE,
        self::IO_STRING,
        self::SCHEDULE_STRING,
        self::DEVICE_ATTENDANCE_STRIDS,
        self::SCHEDULE_TIME,
        self::HAS_CONFLICT,
        self::WORK_TIME,
        self::NIGHT_TIME,
        self::LATE_TIME,
        self::ABSENT_TIME,
        self::SICK_TIME,
        self::EFFORT_TIME,
        self::BREAK_TIME,
        self::BREAK_EFFORT_TIME,
        self::FURLOUGH_TIME,
        self::SALARY_FURLOUGH_TIME,
        self::OVER_TIME,
        self::MATERNITY_TIME,
        self::VACATION_TIME,
        self::UNKNOWN_WORK_TIME,
        self::UNKNOWN_EFFORT,
        self::COMPENSATORY_REST_TIME,
        self::REQUEST_WORK_TIME,
        self::OUT_WORK_TIME,
        self::CREATED_BY,
    ];

    public function employee_attendance_dtls(): HasMany {
        return $this->hasMany(EmployeeAttendanceDtl::class)->orderBy(EmployeeAttendanceDtl::BEGIN_DATE);
    }

    public function employee(): BelongsTo {
        return $this->belongsTo(Employee::class);
    }

    public function getUnitWorkTime() {
        return $this->work_time + $this->night_time + $this->over_time;
    }

    public function setTimeColumn($employeeAttendanceDtl) {
        $hasConflict    = false;
        $diffMinute     = $employeeAttendanceDtl->getDifferenceMinutes();
        switch ($employeeAttendanceDtl->attendance_status) {
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_UNKNOWN_EFFORT:
                $this->unknown_effort_time += $diffMinute;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_EFFORT:
                $this->effort_time += $diffMinute;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK:
                $this->work_time += $diffMinute;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_LATE:
                $hasConflict = true;
                $this->late_time += $diffMinute;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_ABSENT:
                $hasConflict = true;
                $this->absent_time += $diffMinute;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_BREAK:
                $this->break_time += $diffMinute;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_BREAK_EFFORT:
                $this->break_effort_time += $diffMinute;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH:
                $this->furlough_time += $diffMinute;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH:
                $this->salary_furlough_time += $diffMinute;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_OVER:
                $this->over_time += $diffMinute;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_MATERNITY:
                $this->maternity_time += $diffMinute;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_VACATION:
                $this->vacation_time += $diffMinute;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_SICK:
                $this->sick_time += $diffMinute;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_NIGHT_WORK:
                $this->night_time += $diffMinute;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_COMPENSATORY_REST:
                $this->compensatory_rest_time += $diffMinute;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_REQUEST_WORK:
                $this->request_work_time += $diffMinute;
                break;
            case EmployeeAttendanceDtl::ATTENDANCE_STATUS_OUT_WORK:
                $this->out_work_time += $diffMinute;
                break;
            default:
                break;
        }
        $this->has_conflict = $hasConflict;
    }

    public function clearTime() {
        $this->work_time                = 0;
        $this->late_time                = 0;
        $this->absent_time              = 0;
        $this->sick_time                = 0;
        $this->effort_time              = 0;
        $this->break_time               = 0;
        $this->break_effort_time        = 0;
        $this->furlough_time            = 0;
        $this->salary_furlough_time     = 0;
        $this->over_time                = 0;
        $this->maternity_time           = 0;
        $this->vacation_time            = 0;
        $this->unknown_work_time        = 0;
        $this->unknown_effort           = 0;
        $this->night_time               = 0;
        $this->compensatory_rest_time   = 0;
        $this->request_work_time        = 0;
        $this->out_work_time            = 0;
    }

    public function getWorkTimeByHBAttribute()
    {
        return DateTool::convertHourAndMin($this->work_time);
    }

    public function getLateTimeByHBAttribute()
    {
        return DateTool::convertHourAndMin($this->late_time);
    }

    public function getAbsentTimeByHBAttribute()
    {
        return DateTool::convertHourAndMin($this->absent_time);
    }

    public function getFurloughTimeByHBAttribute()
    {
        return DateTool::convertHourAndMin($this->furlough_time);
    }

    public function getSickTimeByHBAttribute()
    {
        return DateTool::convertHourAndMin($this->sick_time);
    }

    public function getEffortTimeByHBAttribute()
    {
        return DateTool::convertHourAndMin($this->effort_time);
    }

    public function getStrRawAttendanceAttribute()
    {
        if (!isset($this->device_attendance_strids))
            return "";
        $rawAttendanceIds = explode(",", $this->device_attendance_strids);
        return RawAttendance::select(RawAttendance::REGISTER_DATE)->whereIn(RawAttendance::ID, $rawAttendanceIds)->orderBy(RawAttendance::REGISTER_DATE)->get()->implode(RawAttendance::REGISTER_DATE, ", ");
    }
}
