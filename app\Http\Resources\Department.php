<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class Apartment
 *
 * @mixin \App\Models\Apartment
 */
class Department extends JsonResource
{
    const ID                    = 'id';
    const NAME                  = 'name';
    const SHORT_NAME            = 'short_name';
    const PARENT_ID             = 'parent_id';
    const PARENT_NAME           = 'parent_name';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID             => $this->id,
            self::NAME           => $this->name,
            self::SHORT_NAME     => $this->short_name,
            self::PARENT_ID      => $this->parent_id,
            self::PARENT_NAME    => $this->parent_name,
        ];
    }
}
