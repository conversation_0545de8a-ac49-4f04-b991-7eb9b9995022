<?php

namespace App\Filament\Resources\Admin\UserMerchantResource\Pages;

use App\Models\User;
use App\Models\Customer\UserMerchant;
use Illuminate\Database\Eloquent\Model;
use App\Filament\Resources\Admin\UserMerchantResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateUserMerchant extends CreateRecord
{
    protected static string $resource = UserMerchantResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        $userId = $data[UserMerchant::USER_ID];
        foreach ($data['merchants'] as $key => $value) {
            $userMerchant = new UserMerchant([
                UserMerchant::USER_ID     => $userId,
                UserMerchant::MERCHANT_ID => $value,
            ]);
            $userMerchant->save();
        }
        return User::find($userId);
    }
}
