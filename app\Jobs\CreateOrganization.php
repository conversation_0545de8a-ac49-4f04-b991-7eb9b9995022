<?php

namespace App\Jobs;

use App\Models\Constant\ConstData;
use App\Models\Organization;
use App\Models\DatabaseConfig;
use App\Services\AdminService;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class CreateOrganization implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout   = 1200;
    public $tries     = 1;

    protected $organizationName;
    protected $registrationNumber;
    protected $userId;
    protected $queueUniqueId;

    /**
     * Create a new job instance.
     *
     * @return void
    */
    public function __construct($organizationName, $registrationNumber, $userId)
    {
        $this->organizationName   = $organizationName;
        $this->registrationNumber = $registrationNumber;
        $this->userId             = $userId;
        $this->queueUniqueId      = 'create-organization-'. $this->registrationNumber . '-' . $this->userId;
    }

    public function uniqueId()
    {
        return $this->queueUniqueId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(AdminService $adminService)
    {
        // $organization = Organization::where(Organization::REGISTRATION_NUMBER, $this->registrationNumber)->first();
        // $connectionName = DatabaseConfig::getCNByOrganizationName($this->organizationName);
        // $password     = config('services.database_config.db_password');
        // $adminService->createNewDbWithConnection($connectionName);
    }

    function removeConnectionFromDatabaseConfig($connectionName) {
        // Locate the configuration file
        $configFilePath = config_path('database.php');

        // Read the configuration file contents
        $configContents = file_get_contents($configFilePath);

        // Remove the connection array
        $newContents = preg_replace(
            sprintf("/'%s'\s*=>\s*\[\s*\n(.*?)\n\s*\]\s*,/s", $connectionName),
            '',
            $configContents
        );

        // Save the modified configuration file
        file_put_contents($configFilePath, $newContents);
    }
}
