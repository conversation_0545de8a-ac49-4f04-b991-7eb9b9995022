<?php

namespace App\Models\Customer;

use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Position extends Model
{
    use HasFactory;
    const TABLE           = 'positions';

    const ID                   = 'id';
    const NAME                 = 'name';
    const SHORT_NAME           = 'short_name';
    const DEPARTMENT_ID        = 'department_id';
    const CREATED_BY           = 'created_by';
    const UPDATED_BY           = 'updated_by';
    const HIERARCHY_ID         = 'hierarchy_id';

    const RELATION_HIERARCHY   = 'hierarchy';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::ID,
        self::NAME,
        self::SHORT_NAME,
        self::DEPARTMENT_ID,
        self::CREATED_BY,
        self::UPDATED_BY,
        self::HIERARCHY_ID
    ];

    public function department() {
        return $this->belongsTo(Department::class);
    }

    public static function getPositionsByDepartmentId($departmentId) {
        if (!isset($departmentId))
            return [];
        return Position::where(Position::DEPARTMENT_ID, $departmentId)->get()->pluck(self::NAME, self::ID);
    }

    public function hierarchy() {
        return $this->belongsTo(Hierarchy::class);
    }
}
