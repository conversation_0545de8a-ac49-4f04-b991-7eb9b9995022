<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class TimeNoSPExport implements FromView
{
    public $unitData;
    public $totalData;

    /**
     * TimeExport constructor.
     *
     * @param $dayData
     * @param $unitData
     */
    public function __construct($unitData, $totalData)
    {
        $this->unitData                 = $unitData;
        $this->totalData                = $totalData;
    }

    /**
     * @inheritDoc
     */
    public function view(): View
    {
        return view('exports.timenosp', [
            'unitData'                  => $this->unitData,
            'totalData'                 => $this->totalData,
        ]);
    }
}
