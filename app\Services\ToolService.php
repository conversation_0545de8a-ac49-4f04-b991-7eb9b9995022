<?php

namespace App\Services;

use Exception;

class ToolService
{
    public function __construct()
    {
    }

    function generateUniqId($lenght = 16) {
        if ($lenght <= 0)
            throw new Exception("Length must be a positive integer");    
        if (function_exists("random_bytes"))
            $bytes = random_bytes(ceil($lenght / 2));
        elseif (function_exists("openssl_random_pseudo_bytes")) 
            $bytes = openssl_random_pseudo_bytes(ceil($lenght / 2));
        else
            throw new Exception("no cryptographically secure random function available");
        return substr(bin2hex($bytes), 0, $lenght);
    }
}
