<?php

namespace App\Jobs;

use App\Models\Constant\ConstData;
use App\Imports\EmployeeImport;
use App\Service\DepartmentService;
use App\Service\PositionService;
use App\Exceptions\SystemException;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Storage;

use Illuminate\Support\Facades\DB;
class ImportEmployee implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout   = 1200;
    public $tries     = 1;

    protected $connectionName;
    protected $organizationId;
    protected $user;
    protected $filePath;
    protected $fileName;
    protected $queueUniqueId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($connectionName, $organizationId, $user, $file)
    {
        $this->connectionName   = $connectionName;
        $this->organizationId   = $organizationId;
        $this->user             = $user;
        $baseName       = basename(strval($file));
        $this->fileName = $baseName;
        $this->filePath = Storage::put($baseName, $file);
        $this->queueUniqueId = 'import-employee-'. $this->organizationId . '-' . $this->user->id;
        $this->onConnection(ConstData::QC_REDIS);
    }

    public function uniqueId()
    {
        return $this->queueUniqueId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(DepartmentService $departmentService, PositionService $positionService)
    {
        try {
            $connectionName = $this->connectionName;
            $user           = $this->user;
            $userId         = $user->id;
            $filePath       = $this->filePath;

            $cn = DB::connection($connectionName);
            $cn->beginTransaction();
            $allDepartments = $departmentService->getDepartmentsByUser($connectionName, $user);
            $allPositions   = $positionService->getPositionsByUser($connectionName, $user);
            Excel::import(new EmployeeImport($connectionName, $allDepartments, $allPositions, $userId), $filePath);
            $cn->commit();
        } catch (SystemException $se) {
            $cn->rollBack();
            return $se->getCustomMessage();
        } finally {
            DB::disconnect($connectionName);
        }
    }
}
