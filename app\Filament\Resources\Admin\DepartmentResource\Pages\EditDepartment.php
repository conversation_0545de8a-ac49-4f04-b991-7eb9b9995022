<?php

namespace App\Filament\Resources\Admin\DepartmentResource\Pages;

use App\Models\Customer\Department;

use App\Filament\Resources\Admin\DepartmentResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditDepartment extends EditRecord
{
    protected static string $resource = DepartmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data[Department::UPDATED_BY] = auth()->user()->id;
        return $data;
    }
}
