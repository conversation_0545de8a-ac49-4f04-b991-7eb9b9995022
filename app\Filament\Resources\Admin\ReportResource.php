<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Models\Customer\Time\SalaryPeriod\SalaryPeriodDtl;
use App\Models\Report;

use App\Filament\Resources\Admin\ReportResource\Pages;
use App\Filament\Resources\Admin\ReportResource\RelationManagers;
use App\Models\Customer\Department;
use App\Models\Customer\Time\SuperSalaryPeriod\SuperSalaryPeriodDtl;
use App\Models\Customer\Time\SuperSalaryPeriod\SuperSalaryPeriodSubTwoDtl;
use App\Services\ConnectionService;
use App\Services\Time\SuperSalaryPeriodService;
use App\Services\Time\EmployeeAttendanceService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;

class ReportResource extends Can
{
    protected static ?string $model = Report::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Тайлан';
    protected static ?string $modelLabel = 'тайлан';
    protected static ?int $navigationSort = 100;
    protected static ?string $navigationGroup = 'Тайлан';
    protected static ?string $slug = 'reports';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('pdf1') 
                    ->label('PDF')
                    ->color('success')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->form([
                        Forms\Components\Select::make(Report::SUPER_SALARY_PERIOD_ID)
                            ->label('Цалингийн үечлэл')
                            ->options(
                                function (callable $get, $set) {
                                    $cn = ConnectionService::connectionNameByUser(auth()->user());
                                    $superSalaryPeriods = resolve(SuperSalaryPeriodService::class)->getSuperSalaryPeriodsWithPluck($cn);
                                    if (!$get(Report::SUPER_SALARY_PERIOD_ID)) {
                                        $set(Report::SUPER_SALARY_PERIOD_DTL_ID, null);
                                        $set(Report::SUPER_SALARY_PERIOD_SUB_TWO_DTL_ID, null);
                                    }
                                    return $superSalaryPeriods;
                                })
                            ->searchable()
                            ->required()
                            ->live(),

                        Forms\Components\Toggle::make(Report::IS_MAIN)
                            ->label('Үндсэн эсэх')
                            ->default(false)
                            ->inline(false)
                            ->onIcon('heroicon-m-bolt')
                            ->offIcon('heroicon-m-bolt-slash')
                            ->live(),

                        Forms\Components\Select::make(Report::SUPER_SALARY_PERIOD_DTL_ID)
                            ->label('Тохиргоо')
                            ->options(
                                function (callable $get, callable $set) {
                                    $isMain                = $get(Report::IS_MAIN);
                                    $superSalaryPeriodId   = $get(Report::SUPER_SALARY_PERIOD_ID);
                                    $superSalaryPeriodDtls = SuperSalaryPeriodDtl::where(SuperSalaryPeriodDtl::TYPE, $isMain ? 0 : 1)->where(SuperSalaryPeriodDtl::SUPER_SALARY_PERIOD_ID, $superSalaryPeriodId)->get()->pluck('department_names', SalaryPeriodDtl::ID);
                                    if (count($superSalaryPeriodDtls)> 0) {
                                        $id = $superSalaryPeriodDtls->keys()->first();
                                        if ($isMain) 
                                            $superSalaryPeriodDtls[$id] = 'Тохиргоо хийгээгүй бусад бүх хэлтэсүүд';
                                        $set(Report::SUPER_SALARY_PERIOD_DTL_ID, $id);
                                    }
                                    return $superSalaryPeriodDtls;
                                })
                            ->required()
                            ->searchable()
                            ->live(),
                            
                        Forms\Components\Toggle::make(Report::IS_FULL_MONTH)
                            ->label('Бүтэн үечлэлээр эсэх')
                            ->default(false)
                            ->inline(false)
                            ->onIcon('heroicon-m-bolt')
                            ->offIcon('heroicon-m-bolt-slash')
                            ->live(),

                        Forms\Components\Select::make(Report::SUPER_SALARY_PERIOD_SUB_TWO_DTL_ID)
                            ->label('Үечлэлийн задаргаа')
                            ->options(
                                function (callable $get, callable $set) {
                                    $superSalaryPeriodDtlId = $get(Report::SUPER_SALARY_PERIOD_DTL_ID);
                                    if (!isset($superSalaryPeriodDtlId))
                                        return;
                                    $superSalaryPeriodSubTwoDtls = SuperSalaryPeriodDtl::find($superSalaryPeriodDtlId)->super_salary_period_sub_two_dtls->pluck(SuperSalaryPeriodSubTwoDtl::FULL_DATE, SuperSalaryPeriodSubTwoDtl::ID);
                                    return $superSalaryPeriodSubTwoDtls;
                                })
                            ->hidden(fn (callable $get) => $get(Report::IS_FULL_MONTH))
                            ->required(fn (callable $get) => !$get(Report::IS_FULL_MONTH))
                            ->searchable(),

                        Forms\Components\Select::make(Report::DEPARTMENTS)
                            ->label('Хэлтэс')
                            ->multiple()
                            ->options(
                                function (callable $get, callable $set) {
                                    $isMain                 = $get(Report::IS_MAIN);
                                    $superSalaryPeriodId    = $get(Report::SUPER_SALARY_PERIOD_ID);
                                    $superSalaryPeriodDtlId = $get(Report::SUPER_SALARY_PERIOD_DTL_ID);
                                    if (!isset($superSalaryPeriodId) || !isset($superSalaryPeriodDtlId))
                                        return;

                                    $relatedDepartments = [];
                                    if (!$isMain) {
                                        $relatedDepartments = SuperSalaryPeriodDtl::find($superSalaryPeriodDtlId)->super_salary_period_sub_one_dtls->pluck(Department::NAME, Department::ID);
                                        $set(Report::RELATED_DEPARTMENTS, $relatedDepartments->keys()->toArray());
                                        return $relatedDepartments;
                                    }
                                    
                                    $ssDepartments = collect();
                                    $dtls          = SuperSalaryPeriodDtl::where(SuperSalaryPeriodDtl::TYPE, 1)->where(SuperSalaryPeriodDtl::SUPER_SALARY_PERIOD_ID, $superSalaryPeriodId)->get();
                                    foreach ($dtls as $dtl) {
                                        $pDtls = $dtl->super_salary_period_sub_one_dtls->pluck(Department::NAME, Department::ID);
                                        $pDtls->each(function ($value, $key) use (&$ssDepartments) {
                                            $ssDepartments[$key] = $value;
                                        });
                                    }
                                    $departments        = Department::all()->pluck(Department::NAME, Department::ID);
                                    $relatedDepartments = $departments->reject(function ($value, $id) use ($ssDepartments) {
                                        return $ssDepartments->first(function ($value, $key) use ($id) {
                                            return $key == $id;
                                        });
                                    });
                                    $set(Report::RELATED_DEPARTMENTS, $relatedDepartments->keys()->toArray());
                                    return $relatedDepartments;
                                })
                            ->searchable()
                            ->distinct()
                            ->searchingMessage('Хайж байна...')
                            ->loadingMessage('Ачаалж байна байна...')
                            ->noSearchResultsMessage('Хайлт олдсонгүй.')
                            ->suffixIcon('heroicon-m-finger-print'),

                        Forms\Components\Hidden::make(Report::RELATED_DEPARTMENTS),

                        Forms\Components\Toggle::make(Report::HAS_DETAIL)
                            ->label('Дэлгэрэнгүй эсэх')
                            ->default(false)
                            ->inline(false)
                            ->onIcon('heroicon-m-bolt')
                            ->offIcon('heroicon-m-bolt-slash'),
                    ])
                    ->action(function (array $data) {
                        $cn = ConnectionService::connectionName();
                        if ($data[Report::IS_FULL_MONTH])
                            $data[Report::SUPER_SALARY_PERIOD_SUB_TWO_DTL_ID] = null;       
                        $superSalaryPeriodDtlId       = $data[Report::SUPER_SALARY_PERIOD_DTL_ID];
                        $superSalaryPeriodSubTwoDtlId = $data[Report::SUPER_SALARY_PERIOD_SUB_TWO_DTL_ID];
                        $departments                  = isset($data[Report::DEPARTMENTS]) && !empty($data[Report::DEPARTMENTS]) ? $data[Report::DEPARTMENTS] : $data[Report::RELATED_DEPARTMENTS];
                        $fileName = resolve(EmployeeAttendanceService::class)->exportMainReport($cn, $superSalaryPeriodDtlId, $superSalaryPeriodSubTwoDtlId, $departments, $data[Report::HAS_DETAIL]);
                        return redirect(url("storage/$fileName"));
                    })
                    ->hidden(function (Tables\Actions\Action $action) {
                        return $action->getRecord()->name != 'Цагийн тайлан';
                    }),
                Tables\Actions\Action::make('pdf2') 
                    ->label('PDF')
                    ->color('success')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->form([
                        Forms\Components\DatePicker::make(Report::BEGIN_DATE)->label('Эхлэх огноо')->required(),
                        Forms\Components\DatePicker::make(Report::END_DATE)->label('Дуусах огноо'),
                        Forms\Components\Select::make(Report::DEPARTMENTS)
                            ->label('Хэлтэс')
                            ->multiple()
                            ->options(Department::all()->pluck(Department::NAME, Department::ID))
                            ->searchable()
                            ->distinct()
                            ->searchingMessage('Хайж байна...')
                            ->loadingMessage('Ачаалж байна байна...')
                            ->noSearchResultsMessage('Хайлт олдсонгүй.')
                            ->suffixIcon('heroicon-m-finger-print'),

            
                    ])
                    ->action(function (array $data) {
                        $cn          = ConnectionService::connectionName();
                        $beginDate   = $data[Report::BEGIN_DATE];
                        $endDate     = $data[Report::END_DATE];
                        $departments = $data[Report::DEPARTMENTS];
                        $fileName    = resolve(EmployeeAttendanceService::class)->exportAttendance($cn, $departments, $beginDate, $endDate);
                        return redirect(url("storage/$fileName"));
                    })
                    ->hidden(function (Tables\Actions\Action $action) {
                        return $action->getRecord()->name != 'Цагийн тайлан | Үечлэлгүй';
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReports::route('/'),
        ];
    }
}
