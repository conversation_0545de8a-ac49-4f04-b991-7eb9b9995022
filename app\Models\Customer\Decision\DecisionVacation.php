<?php

namespace App\Models\Customer\Decision;

use App\Models\Customer\Decision\Decision;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DecisionVacation extends Model
{
    use HasFactory;

    const TABLE = 'decision_vacations';

    const ID                   = 'id';
    const DECISION_ID          = 'decision_id';
    const VACATION_DATE        = 'vacation_date';
    const CREATED_BY           = 'created_by';
    const UPDATED_BY           = 'updated_by';

    const RELATION_DECISION    = 'decision';

    protected $fillable = [
        self::ID,
        self::DECISION_ID,
        self::VACATION_DATE,
        self::CREATED_BY,
    ];

    public function decision() {
        return $this->belongsTo(Decision::class);
    }
}
