<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ApprovalConfig extends JsonResource
{
    const ID                                = 'id';
    const NAME                              = 'name';
    const APRROVAL_CONFIG_DTLS_COUNT        = 'approval_config_dtls_count';
    const APRROVAL_CONFIG_SUB_DTLS_COUNT    = 'approval_config_sub_dtls_count';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                                => $this->id,
            self::NAME                              => $this->name,
            self::APRROVAL_CONFIG_DTLS_COUNT        => $this->approval_config_dtls_count,
            self::APRROVAL_CONFIG_SUB_DTLS_COUNT    => $this->approval_config_sub_dtls_count,
        ];
    }
}
