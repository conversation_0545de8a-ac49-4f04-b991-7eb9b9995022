<?php

namespace App\Filament\Resources\Admin\EmployeeVoucherAmountResource\Pages;

use App\Filament\Resources\Admin\EmployeeVoucherAmountResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Livewire\Attributes\On;

class EditEmployeeVoucherAmount extends EditRecord
{
    protected static string $resource = EmployeeVoucherAmountResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    #[On('employee-voucher-amount-changed')]
    public function refresh() {
        $this->data['unused_total_amount'] = $this->record->unused_total_amount;
        $this->data['used_total_amount']   = $this->record->used_total_amount;
    }
}
