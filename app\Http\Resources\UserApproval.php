<?php

namespace App\Http\Resources;

use App\Models\Admin\User as UserModel;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class User
 *
 * @mixin \App\Models\Admin\User
 */
class UserApproval extends JsonResource
{
    const ID                    = 'id';
    const FIRST_NAME            = 'first_name';
    const LAST_NAME             = 'last_name';
    const EMAIL                 = 'email';
    const PHONE                 = 'phone';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                    => $this->id,
            self::FIRST_NAME            => $this->first_name,
            self::LAST_NAME             => $this->last_name,
            self::EMAIL                 => $this->email,
            self::PHONE                 => $this->phone
        ];
    }
}
