<?php

namespace App\Filament\Resources\Admin\SuperSalaryPeriodResource\Pages;

use App\Models\Customer\Time\SuperSalaryPeriod\SuperSalaryPeriod;
use App\Filament\Resources\Admin\SuperSalaryPeriodResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditSuperSalaryPeriod extends EditRecord
{
    protected static string $resource = SuperSalaryPeriodResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->before(function (SuperSalaryPeriod $record) {
                    foreach ($record->super_salary_period_dtls as $key => $dtl) {
                        $dtl->super_salary_period_sub_one_dtls()->detach();
                        $dtl->super_salary_period_sub_two_dtls()->delete();
                    }
                }),
        ];
    }
}
