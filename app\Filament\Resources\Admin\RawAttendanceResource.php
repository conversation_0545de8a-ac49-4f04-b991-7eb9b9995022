<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Models\Customer\Time\Attendance\RawAttendance;
use App\Filament\Resources\Admin\RawAttendanceResource\Pages;
use App\Filament\Resources\Admin\RawAttendanceResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;


class RawAttendanceResource extends Can
{
    protected static ?string $model = RawAttendance::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Түүхий ирц';
    protected static ?string $modelLabel = 'түүхий ирц';
    protected static ?string $navigationGroup = 'Бусад';
    protected static ?int $navigationSort = 100000;
    protected static ?string $slug = 'raw_attendances';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('employee_dtl.employee.full_name')->label('Нэр')->sortable()->searchable(['first_name', 'last_name']),
                Tables\Columns\TextColumn::make(RawAttendance::REGISTER_DATE)->label('Нэр')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
            ])
            ->bulkActions([
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRawAttendances::route('/'),
        ];
    }

    public static function getEloquentQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return parent::getEloquentQuery()->orderByDesc(RawAttendance::REGISTER_DATE);
    }
}
