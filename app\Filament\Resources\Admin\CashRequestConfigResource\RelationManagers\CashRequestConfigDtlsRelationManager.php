<?php

namespace App\Filament\Resources\Admin\CashRequestConfigResource\RelationManagers;


use App\Models\Customer\CashRequest\CashRequestConfigDtl;
use App\Services\ConnectionService;
use App\Services\EmployeeUserService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Support\RawJs;

class CashRequestConfigDtlsRelationManager extends RelationManager
{
    protected static string $relationship = 'cash_request_config_dtls';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make(CashRequestConfigDtl::LIMIT_AMOUNT)
                    ->label('Мөнгөний хязгаар')
                    ->numeric()
                    ->unique(ignoreRecord: true)
                    ->default(1)
                    ->minValue(1)
                    ->inputMode('decimal')
                     ->mask(RawJs::make('$money($input)'))
                    ->stripCharacters(',')
                    ->numeric()
                    ->minValue(1)
                    ->suffix(' ₮')
                    ->required(),

                Forms\Components\Select::make(CashRequestConfigDtl::EMPLOYEE_USER_ID)
                    ->label('Батлах хэрэглэгч')
                    ->options(function () {
                        $cn = resolve(ConnectionService::class)->connectionName();
                        return resolve(EmployeeUserService::class)->getEmployeeUsersWithFullDisplayName($cn);
                    })    
                    ->searchable()
                    ->searchingMessage('Хайж байна...')
                    ->loadingMessage('Ачаалж байна байна...')
                    ->noSearchResultsMessage('Хэлтэс олдсонгүй.')
                    ->required(),
            ])->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('limit_amount')
            ->columns([
                Tables\Columns\TextColumn::make('limit_amount'),
                Tables\Columns\TextColumn::make('employee_department_name')
                    ->label('Хэлтсийн')
                    ->icon('heroicon-m-building-office'),
                Tables\Columns\TextColumn::make('employee_position_name')
                    ->label('Албан тушаалтай')
                    ->icon('heroicon-m-briefcase'),
                Tables\Columns\TextColumn::make('employee_display_name')
                    ->label('Батлах хэрэглэгч')
                    ->icon('heroicon-m-user'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
