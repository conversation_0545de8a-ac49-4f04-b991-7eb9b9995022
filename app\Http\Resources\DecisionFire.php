<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class DecisionFire
 *
 * @mixin \App\Models\Customer\Time\Decision\DecisionFire
 */
class DecisionFire extends JsonResource
{
    const ID                         = 'id';
    const DECISION_ID                = 'decision_id';
    const FIRE_DATE                  = 'fire_date';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                            => $this->id,
            self::DECISION_ID                   => $this->decision_id,
            self::FIRE_DATE                     => $this->fire_date,
        ];
    }
}
