<?php

namespace App\Filament\Resources\Admin;

use App\Models\Customer\Hierarchy;
use App\Filament\Resources\Admin\HierarchyResource\Pages;
use App\Filament\Resources\Admin\HierarchyResource\RelationManagers;
use App\Models\Class\Can;
use App\Models\Customer\Benefit;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class HierarchyResource extends Can
{
    protected static ?string $model = Hierarchy::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Шатлал';
    protected static ?string $modelLabel = 'Шатлал';
    protected static ?int $navigationSort = 11;
    protected static ?string $navigationGroup = 'Урамшуулал';
    protected static ?string $slug = 'hierarchies';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make(Hierarchy::NAME)
                            ->label('Нэр')
                            ->maxLength(255)
                            ->required(),
                        Forms\Components\Select::make(Hierarchy::RELATION_BENEFITS)
                                ->label('Урамшуулал')
                                ->relationship(name: Hierarchy::RELATION_BENEFITS, titleAttribute: Benefit::NAME)
                                ->options(Benefit::all()->pluck(Benefit::NAME, 'id'))
                                ->multiple()
                                ->searchable()
                                ->distinct()
                                ->searchingMessage('Хайж байна...')
                                ->loadingMessage('Ачаалж байна байна...')
                                ->noSearchResultsMessage('Хайлт олдсонгүй.')
                                ->suffixIcon('heroicon-m-finger-print'),    
                    ])
                    ->columns(1)
                    ->columnSpan(['lg' => fn (?Hierarchy $record) => $record === null ? 3 : 2]),

            Forms\Components\Section::make()
                ->schema([
                    Forms\Components\Placeholder::make('created_at')
                        ->label('Created at')
                        ->content(fn (Hierarchy $record): ?string => $record->created_at?->diffForHumans()),

                    Forms\Components\Placeholder::make('updated_at')
                        ->label('Last modified at')
                        ->content(fn (Hierarchy $record): ?string => $record->updated_at?->diffForHumans()),
                ])
                ->columnSpan(['lg' => 1])
                ->hidden(fn (?Hierarchy $record) => $record === null),
        ])
        ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Hierarchy::NAME)->label('Нэр')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHierarchies::route('/'),
            'create' => Pages\CreateHierarchy::route('/create'),
            'edit' => Pages\EditHierarchy::route('/{record}/edit'),
        ];
    }
}
