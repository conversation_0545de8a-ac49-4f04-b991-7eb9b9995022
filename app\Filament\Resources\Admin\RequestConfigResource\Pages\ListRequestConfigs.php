<?php

namespace App\Filament\Resources\Admin\RequestConfigResource\Pages;

use App\Filament\Resources\Admin\RequestConfigResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListRequestConfigs extends ListRecords
{
    protected static string $resource = RequestConfigResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-o-plus'),
        ];
    }
}
