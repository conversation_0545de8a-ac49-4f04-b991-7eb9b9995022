<?php

namespace App\Filament\Resources\Admin\PositionResource\Pages;

use App\Models\Customer\Position;

use App\Filament\Resources\Admin\PositionResource;
use Filament\Resources\Pages\CreateRecord;

class CreatePosition extends CreateRecord
{
    protected static string $resource = PositionResource::class;


    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data[Position::CREATED_BY] = auth()->user()->id;
        return $data;
    }
}
