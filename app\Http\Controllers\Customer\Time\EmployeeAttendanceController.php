<?php

namespace App\Http\Controllers\Customer\Time;

use App\Models\Customer\Employee;
use App\Models\Customer\Time\Attendance\EmployeeAttendance;
use App\Http\Requests\Customer\Time\EmployeeAttendance\GetEmployeeAttendancesRequest;
use App\Http\Resources\EmployeeAttendance as EmployeeAttendanceResource;
use App\Http\Controllers\Controller;
use App\Services\Time\SuperSalaryPeriodService;
use App\Services\ConnectionService;

class EmployeeAttendanceController extends Controller
{
    public function getCN() {
        $service = resolve(ConnectionService::class);
        return $service->getCNForEmployeeUser();
    }

    /**
     * Системд бүртгэгдсэн ирцийн мэдээлэлүүдийг жагсаалтаар авах.
     */
    public function index(GetEmployeeAttendancesRequest $request)
    {
        $limit         = $request->input(GetEmployeeAttendancesRequest::LIMIT, 10);
        $filters       = $request->input(GetEmployeeAttendancesRequest::FILTER);

        $cn            = $this->getCN();
        $user          = auth()->user();
        $employee      = Employee::on($cn)->where(Employee::PHONE, $user->phone)->first();

        $rawAttendances = EmployeeAttendance::on($cn)->where(EmployeeAttendance::EMPLOYEE_ID, $employee->id);
        if (isset($filters[GetEmployeeAttendancesRequest::FILTER_YEAR]) && isset($filters[GetEmployeeAttendancesRequest::FILTER_MONTH])) {
            $year     = $filters[GetEmployeeAttendancesRequest::FILTER_YEAR];
            $month    = $filters[GetEmployeeAttendancesRequest::FILTER_MONTH];

            $salaryPeriodService  = resolve(SuperSalaryPeriodService::class);
            $superSalaryPeriod    = $salaryPeriodService->getSuperSalaryPeriodByYearAndMonth($cn, $user, $year, $month);
            $superSalaryPeriodDtl = $superSalaryPeriod->getSuperSalaryPeriodDtl($employee->department_id);
            $beginDate            = $superSalaryPeriodDtl->getBeginDate();
            $endDate              = $superSalaryPeriodDtl->getEndDate();
            $rawAttendances       = $rawAttendances->where(EmployeeAttendance::ATT_DATE, '>=', $beginDate)->where(EmployeeAttendance::ATT_DATE, '<=', $endDate);
        }
        $rawAttendances = $rawAttendances->paginate($limit);
        return EmployeeAttendanceResource::collection($rawAttendances);
    }
}
