<?php

namespace Database\Factories;

use App\Models\Customer\Time\Attendance\RawAttendance;
use Illuminate\Database\Eloquent\Factories\Factory;

class RawAttendanceFactory extends Factory
{
    protected $model = RawAttendance::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $ac  = rand(10001,10100);   
        $min = strtotime('2024-02-01 05:00:00');
        $max = strtotime('2024-02-28 05:00:00');
        $val = rand($min, $max);
        $rd  = date('Y-m-d H:i:s', $val);
        return [
            RawAttendance::ATTENDANCE_CODE => $ac,
            RawAttendance::REGISTER_DATE   => $rd,
            RawAttendance::TYPE            => 0,
            RawAttendance::FROM_NAME       => 'fake',   
        ];
    }
}
