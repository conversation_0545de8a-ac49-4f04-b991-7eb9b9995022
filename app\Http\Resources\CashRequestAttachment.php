<?php

namespace App\Http\Resources;

use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CashRequestAttachment extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'              => $this->id,
            'cash_request_id' => $this->cash_request_id,
            'file_path'       => Storage::disk('minio')->temporaryUrl($this->file_path,now()->addMinutes(5)),
            'original_name'   => $this->original_name,
        ];
    }
}
