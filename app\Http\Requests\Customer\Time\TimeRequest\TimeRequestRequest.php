<?php

namespace App\Http\Requests\Customer\Time\TimeRequest;

use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Services\Time\TimeRequestValidationService;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class TimeRequestRequest extends FormRequest
{
    const PARAMETER_BEGIN_DATE                 = 'begin_date';
    const PARAMETER_END_DATE                   = 'end_date';
    const PARAMETER_DESCRIPTION                = 'description';
    const PARAMETER_ATT_STATUS                 = 'att_status';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'begin_date'         => [
                'required',
                'date',
                function ($attribute, $value, $fail) {
                    $attStatus = $this->input('att_status');
                    if ($attStatus) {
                        $validationService = resolve(TimeRequestValidationService::class);

                        // First validate using value1 field (existing logic)
                        $result1 = $validationService->validateValue1($value, $attStatus);
                        if (!$result1['valid']) {
                            $fail($result1['message']);
                            return;
                        }

                        // Then validate using value field (salary furlough and furlough duration logic)
                        $endDate = $this->input('end_date');
                        $result2 = $validationService->validateValue($value, $attStatus, $endDate);
                        if (!$result2['valid']) {
                            $fail($result2['message']);
                            return;
                        }
                    }
                }
            ],
            'end_date'           => 'required|date|after_or_equal:begin_date',
            'description'        => 'nullable|string|max:255',
            /**
             * 3: Work, 8: Furlough, 13: Sick
             */
            'att_status'         => ['required', Rule::in(EmployeeAttendanceDtl::REQUEST_ATTENDANCE_STATUS)],
        ];
    }
}
