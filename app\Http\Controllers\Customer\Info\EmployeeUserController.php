<?php

namespace App\Http\Controllers\Customer\Info;

use App\Models\Constant\ConstData;
use App\Models\Customer\Employee;
use App\Models\EmployeeUser;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\UpdatePasswordRequest;
use App\Http\Resources\Me as MeResource;
use App\Http\Resources\Token as TokenResource;
use App\Http\Controllers\Controller;
use App\Services\ConnectionService;
use App\Exceptions\SystemException;
use App\Models\Customer\Time\RequestConfig\RequestConfigDtl;
use App\Services\EmployeeUserService;
use Illuminate\Support\Facades\Hash;

class EmployeeUserController extends Controller
{
    public function getCN() {
        $service = resolve(ConnectionService::class);
        return $service->getCNForEmployeeUser();
    }
    /**
     *  Employee login by own account.
     */
    public function login(LoginRequest $request) {
        $phone        = $request->input(LoginRequest::PARAMETER_PHONE);
        $password     = $request->input(LoginRequest::PARAMETER_PASSWORD);
        $employeeUser = EmployeeUser::where(EmployeeUser::PHONE, $phone)->first();
        if (!isset($employeeUser) || (isset($employeeUser) && !Hash::check($password, $employeeUser->password)))
            throw new SystemException(ConstData::AUTH_EXCEPTION, 2);
        if (isset($user) && !$user->active_status)
            throw new SystemException(ConstData::SYSTEM_EXCEPTION, 8);
        return new TokenResource($employeeUser->createToken($employeeUser->id));
    }

    /**
     * Ажилтны өөрийн мэдэээлэл.
     * Системд нэвтэрсэн ажилтны мэдээлэлийг өөрт нь харуулах зориулалттай.
     */
    public function me()
    {
        $employeeUser  = auth()->user();
        $cn            = $this->getCN();
        $emplpoyeeUser = resolve(EmployeeUserService::class)->getEmployeeUser($employeeUser->id);
        $employee      = Employee::on($cn)->where(Employee::PHONE, $emplpoyeeUser->phone)->first();
        $emplpoyeeUser->employee = $employee;
        $emplpoyeeUser->batlalh_erkh_bga_esekh = RequestConfigDtl::on($cn)->where(RequestConfigDtl::EMPLOYEE_USER_ID, $emplpoyeeUser->id)->first() ? true : false;  
        return new MeResource($emplpoyeeUser);
    }

    /**
     * Update password
     * Ажилтан мобайл апликейшн рүү нэвтрэх нууц үгээ солих зориулалттай.
     */
    public function updatePassword(UpdatePasswordRequest $request) {
        $phone          = $request->input(UpdatePasswordRequest::PARAMETER_PHONE);
        $oldPassword    = $request->input(UpdatePasswordRequest::PARAMETER_OLD_PASSWORD);
        $password       = $request->input(UpdatePasswordRequest::PARAMETER_PASSWORD);
        $organizationId = auth()->user()->organization->id;
        $employeeUser   = EmployeeUser::where(EmployeeUser::ORGANIZATION_ID, $organizationId)->where(EmployeeUser::PHONE, $phone)->first();
        if (!isset($employeeUser) || (isset($employeeUser) && !Hash::check($oldPassword, $employeeUser->password)))
            throw new SystemException(ConstData::AUTH_EXCEPTION, 2);
        $employeeUser->password = Hash::make($password);
        $employeeUser->save();
        return new TokenResource($employeeUser->createToken($employeeUser->id));
    }
}
