<?php

namespace App\Models\Customer\Time\Schedule\OtherTimeSchedule;

use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OtherTimeScheduleDtlBt extends Model
{
    use HasFactory;
    const TABLE = 'other_time_schedule_dtl_bts';

    const ID                            = 'id';
    const MAIN_TIME_SCHEDULE_DTL_ID     = 'other_time_schedule_dtl_id';
    const BEGIN_TIME_STR                = 'begin_time_str';
    const END_TIME_STR                  = 'end_time_str';
    const CHECK_IO                      = 'check_io';
    const CREATED_BY                    = 'created_by';

    public function __construct($attributes = array()) {
        $this->connection             = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::ID,
        self::MAIN_TIME_SCHEDULE_DTL_ID,
        self::BEGIN_TIME_STR,
        self::END_TIME_STR,
        self::CHECK_IO,
        self::CREATED_BY,
    ];

    protected $casts = [
        self::CHECK_IO   => 'boolean',
    ];

    public function getHashStr() {;
        return $this->begin_time_str.'-'.$this->end_time_str.'-'.($this->check_io?1:0);
    }
}
