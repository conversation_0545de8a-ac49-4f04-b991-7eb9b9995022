<?php

namespace App\Service\Time;

use App\Models\Customer\Decision\Decision;
use App\Models\Customer\Decision\DecisionFire;
use App\Models\Customer\Decision\DecisionLongTermFurlough;
use App\Models\Customer\Decision\DecisionVacation;
use App\Models\Customer\Decision\DecisionMaternity;
use App\Models\Constant\ConstData;
use App\Exceptions\SystemException;
use App\Models\Customer\Employee;
use Illuminate\Database\Eloquent\Builder;

class DecisionService
{
    /**
     * - Урт хугацааны чөлөө
     * - Жирэмсний амралт
     * - Ээлжийн амралт
     */
    public function getCurrentEmployeeDecisionByPerDay($connectionName, $employeeId, $attDate) {
        $decision = new Decision();
        $decision->setConnection($connectionName);
        $decision = $decision->where(Decision::EMPLOYEE_ID, $employeeId)
                             ->where(function (Builder $query) use ($attDate) {
                        return $query ->whereHas(Decision::RELATION_LONG_TERM_FURLOUGH, function (Builder $query) use ($attDate) {
                                    $query->whereDate(DecisionLongTermFurlough::BEGIN_DATE, '<=', $attDate)
                                            ->whereDate(DecisionLongTermFurlough::END_DATE, '>=', $attDate);
                                })->orWhereHas(Decision::RELATION_VACATIONS, function (Builder $query) use ($attDate) {
                                    $query->whereDate(DecisionVacation::VACATION_DATE, $attDate);
                                })->orWhereHas(Decision::RELATION_MATERNITIES, function (Builder $query) use ($attDate) {
                                    $query->whereDate(DecisionMaternity::BEGIN_DATE, '<=', $attDate)
                                            ->whereDate(DecisionMaternity::END_DATE, '>=', $attDate);
                                });
                    })->get();

        if (count($decision) > 1) {
            throw new SystemException(ConstData::TIME_EXCEPTION, 315);
        }
        return $decision;
    }

    /**
     * - Урт хугацааны чөлөө
     * - Жирэмсний амралт
     * - Ээлжийн амралт
     */
    public function getDecisionsByBetweenDates($connectionName, $beginDate, $endDate, $departmentIds) {
        $decisions = new Decision();
        $decisions = $decisions->on($connectionName)->with(Decision::RELATION_LONG_TERM_FURLOUGH, Decision::RELATION_VACATIONS, Decision::RELATION_MATERNITIES)->where(function (Builder $query) use ($beginDate, $endDate) {
                        return $query->whereHas(Decision::RELATION_LONG_TERM_FURLOUGH, function (Builder $query) use ($beginDate, $endDate) {
                                    $query->where(
                                            function($query) use($beginDate, $endDate) {
                                                return $query->where(
                                                    function($query) use($beginDate, $endDate) {
                                                        return $query->whereDate(DecisionLongTermFurlough::BEGIN_DATE, '>=', $beginDate)->whereDate(DecisionLongTermFurlough::END_DATE, '<=', $endDate);
                                                    }
                                                  )->orWhere(
                                                    function($query) use($beginDate, $endDate) {
                                                        return $query->whereDate(DecisionLongTermFurlough::BEGIN_DATE, '<', $beginDate)->whereDate(DecisionLongTermFurlough::END_DATE, '>', $endDate);
                                                    }
                                                  )->orWhere(
                                                    function($query) use($beginDate, $endDate) {
                                                        return $query->whereDate(DecisionLongTermFurlough::BEGIN_DATE, '<', $beginDate)->whereBetween(DecisionLongTermFurlough::END_DATE, [$beginDate, $endDate]);
                                                    }
                                                  )->orWhere(
                                                    function($query) use($beginDate, $endDate) {
                                                        return $query->whereBetween(DecisionLongTermFurlough::BEGIN_DATE, [$beginDate, $endDate])->whereDate(DecisionLongTermFurlough::END_DATE, '>', $endDate);
                                                    }
                                                  );
                                            }
                                          );
                                })->orWhereHas(Decision::RELATION_VACATIONS, function (Builder $query) use ($beginDate, $endDate) {
                                    $query->whereBetween(DecisionVacation::VACATION_DATE, [$beginDate, $endDate]);
                                })->orWhereHas(Decision::RELATION_MATERNITIES, function (Builder $query) use ($beginDate, $endDate) {
                                    $query->where(
                                                function($query) use($beginDate, $endDate) {
                                                    return $query->where(
                                                        function($query) use($beginDate, $endDate) {
                                                            return $query->whereDate(DecisionMaternity::BEGIN_DATE, '>=', $beginDate)->whereDate(DecisionMaternity::END_DATE, '<=', $endDate);
                                                        }
                                                      )->orWhere(
                                                        function($query) use($beginDate, $endDate) {
                                                            return $query->whereDate(DecisionMaternity::BEGIN_DATE, '<', $beginDate)->whereDate(DecisionMaternity::END_DATE, '>', $endDate);
                                                        }
                                                      )->orWhere(
                                                        function($query) use($beginDate, $endDate) {
                                                            return $query->whereDate(DecisionMaternity::BEGIN_DATE, '<', $beginDate)->whereBetween(DecisionMaternity::END_DATE, [$beginDate, $endDate]);
                                                        }
                                                      )->orWhere(
                                                        function($query) use($beginDate, $endDate) {
                                                            return $query->whereBetween(DecisionMaternity::BEGIN_DATE, [$beginDate, $endDate])->whereDate(DecisionMaternity::END_DATE, '>', $endDate);
                                                        }
                                                      );
                                                }
                                              );
                                });
                    });
        if (count($departmentIds) > 0) {
            $decisions = $decisions->whereHas(Decision::RELATION_EMPLOYEE, function (Builder $query) use ($departmentIds) {
                $query->whereIn(Employee::DEPARTMENT_ID, $departmentIds);
            });
        }
        $decisions = $decisions->get();
        return $decisions;
    }

    public function checkEmployeeDecision($connectionName, $employeeId, $beginDate, $endDate) {
        $decision = new Decision();
        $decision->setConnection($connectionName);
        $decision = $decision->where(Decision::EMPLOYEE_ID, $employeeId)->where(function (Builder $query) use ($beginDate, $endDate) {
                        return $query ->whereHas(Decision::RELATION_LONG_TERM_FURLOUGH, function (Builder $query) use ($beginDate, $endDate) {
                                        $query->whereDate(DecisionLongTermFurlough::BEGIN_DATE, '<=', $endDate)
                                              ->whereDate(DecisionLongTermFurlough::END_DATE, '<=', $beginDate);
                                    })->orWhereHas(Decision::RELATION_VACATIONS, function (Builder $query) use ($beginDate) {
                                        $query->whereDate(DecisionVacation::VACATION_DATE, $beginDate);
                                    })->orWhereHas(Decision::RELATION_MATERNITIES, function (Builder $query) use ($beginDate, $endDate) {
                                        $query->whereDate(DecisionMaternity::BEGIN_DATE, '<=', $endDate)
                                              ->whereDate(DecisionMaternity::END_DATE, '<=', $beginDate);
                                    });
                    })->get();

        if (count($decision) > 1) {
            throw new SystemException(ConstData::TIME_EXCEPTION, 315);
        }
        return $decision;
    }

    /**
     * Халагдах тушаал
     */
    public function getCurrentEmployeeDecisionFireByPerDay($connectionName, $employeeId, $salaryDate) {
        $decision = new Decision();
        $decision->setConnection($connectionName);
        $decision = $decision->where(Decision::EMPLOYEE_ID, $employeeId)
                             ->whereHas(Decision::RELATION_FIRE, function (Builder $query) use ($salaryDate) {
                                $query->whereDate(DecisionFire::FIRE_DATE, $salaryDate);
                             })->first();
        return $decision;
    }

    /**
     * Томилогдох тушаалуудыг авах
     */
    public function getDecisionHiresByBetweenDate($connectionName, $beginDate, $endDate, $departmentIds) {
        $decisions = new Decision();
        $decisions = $decisions->on($connectionName)->with(Decision::RELATION_HIRE)->where(Decision::TYPE, Decision::TYPE_HIRE);
        if (count($departmentIds) > 0) {
            $decisions = $decisions->whereHas(Decision::RELATION_EMPLOYEE, function (Builder $query) use ($departmentIds) {
                $query->whereIn(Employee::DEPARTMENT_ID, $departmentIds);
            });
        }
        $decisions = $decisions->get();
        return $decisions;
    }

    /**
     * Халагдах тушаалуудыг авах
     */
    public function getDecisionFiresByBetweenDate($connectionName, $beginDate, $endDate, $departmentIds) {
        $decisions = new Decision();
        $decisions = $decisions->on($connectionName)->with(Decision::RELATION_FIRE)->where(Decision::TYPE, Decision::TYPE_FIRE);
        if (count($departmentIds) > 0) {
            $decisions = $decisions->whereHas(Decision::RELATION_EMPLOYEE, function (Builder $query) use ($departmentIds) {
                $query->whereIn(Employee::DEPARTMENT_ID, $departmentIds);
            });
        }
        $decisions = $decisions->get();
        return $decisions;
    }

    /**
     * ТУХАЙН ЦАЛИНГИЙН ҮЕЧЛЭЛД ЭЭЛЖИЙН АМРАЛТЫН ТУШААЛ ГАРСАН АЖИЛЧДЫН ТОО
     */
    public function getEmployeeCountInVacation($connectionName, $beginDate, $endDate, $departmentIds = []) {
        $vacationEmployeeCount = new Decision();
        $vacationEmployeeCount->setConnection($connectionName);
        $vacationEmployeeCount = $vacationEmployeeCount->whereHas(Decision::RELATION_VACATIONS, function (Builder $query) use ($beginDate, $endDate) {
            $query->whereBetween(DecisionVacation::VACATION_DATE, [$beginDate, $endDate]);
        });

        if (!empty($departmentIds)) {
            $vacationEmployeeCount = $vacationEmployeeCount->whereHas(Decision::RELATION_EMPLOYEE . '.department', function (Builder $query) use ($departmentIds) {
                $query->whereIn(Employee::DEPARTMENT_ID, $departmentIds);
            });
        }

        $vacationEmployeeCount = $vacationEmployeeCount->count();
        return $vacationEmployeeCount;
    }

}
