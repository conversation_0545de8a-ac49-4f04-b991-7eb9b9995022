<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Models\Customer\Employee;
use App\Models\Customer\Voucher\EmployeeVoucherAmount;
use App\Filament\Resources\Admin\EmployeeVoucherAmountResource\Pages;
use App\Filament\Resources\Admin\EmployeeVoucherAmountResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Support\RawJs;

class EmployeeVoucherAmountResource extends Can
{
    protected static ?string $model = EmployeeVoucherAmount::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Ажилтаны ваучер';
    protected static ?string $modelLabel = 'Ажилтаны ваучер';
    protected static ?int $navigationSort = 111;
    protected static ?string $navigationGroup = 'Ваучер';
    protected static ?string $slug = 'employee-voucher-amounts';

    public static function form(Form $form): Form
    {
        return $form
                ->schema([
                    Forms\Components\Section::make()
                        ->schema([
                            forms\Components\Select::make(EmployeeVoucherAmount::EMPLOYEE_ID)
                                ->label('Ажилтан')
                                ->searchable()
                                ->required()
                                ->options(function () {
                                    return Employee::doesntHave('employee_voucher_amounts')->get()->pluck('full_name_with_dp', Employee::ID);
                                })
                                ->hidden(fn (?EmployeeVoucherAmount $record) => $record !== null),
                            Forms\Components\TextInput::make('department_name')
                                ->label('Хэлтэс')
                                ->formatStateUsing(fn (?EmployeeVoucherAmount $record): string => $record?->employee?->department?->name ?? '-')
                                ->disabled(true)
                                ->hidden(fn (?EmployeeVoucherAmount $record) => $record === null),

                            Forms\Components\TextInput::make('position_name')
                                ->label('Албан тушаал')
                                ->formatStateUsing(fn (?EmployeeVoucherAmount $record): string => $record?->employee?->position?->name ?? '-')
                                ->disabled(true)
                                ->hidden(fn (?EmployeeVoucherAmount $record) => $record === null),

                            Forms\Components\TextInput::make('employee.last_name')
                                ->label('Овог')
                                ->formatStateUsing(fn (?EmployeeVoucherAmount $record): string => $record?->employee?->last_name ?? '-')
                                ->disabled(true)
                                ->hidden(fn (?EmployeeVoucherAmount $record) => $record === null),

                            Forms\Components\TextInput::make('employee.first_name')
                                ->label('Нэр')
                                ->formatStateUsing(fn (?EmployeeVoucherAmount $record): string => $record?->employee?->first_name ?? '-')
                                ->disabled(true)
                                ->hidden(fn (?EmployeeVoucherAmount $record) => $record === null),

                            Forms\Components\TextInput::make(EmployeeVoucherAmount::UNUSED_TOTAL_AMOUNT)
                                ->label('Ашиглаагүй дүн')
                                ->mask(RawJs::make('$money($input)'))
                                ->stripCharacters(',')
                                ->disabled(true)
                                ->hidden(fn (?EmployeeVoucherAmount $record) => $record === null),

                            Forms\Components\TextInput::make(EmployeeVoucherAmount::USED_TOTAL_AMOUNT)
                                ->label('Ашигласан дүн')
                                ->mask(RawJs::make('$money($input)'))
                                ->stripCharacters(',')
                                ->disabled(true)
                                ->hidden(fn (?EmployeeVoucherAmount $record) => $record === null),
                        ])
                        ->columns(2)
                        ->columnSpan(['lg' => fn (?EmployeeVoucherAmount $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (EmployeeVoucherAmount $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (EmployeeVoucherAmount $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?EmployeeVoucherAmount $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('employee.department.name')->label('Хэлтэс')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('employee.position.name')->label('Албан тушаал')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('employee.last_name')->label('Овог')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('employee.first_name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(EmployeeVoucherAmount::UNUSED_TOTAL_AMOUNT)->label('Нийт дүн')->money('MNT')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(EmployeeVoucherAmount::USED_TOTAL_AMOUNT)->label('Ашигласан дүн')->money('MNT')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\EmployeeVouchersRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'create' => Pages\CreateEmployeeVoucherAmount::route('/create'),
            'index' => Pages\ListEmployeeVoucherAmounts::route('/'),
            'edit' => Pages\EditEmployeeVoucherAmount::route('/{record}/edit'),
        ];
    }
}
