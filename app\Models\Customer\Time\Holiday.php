<?php

namespace App\Models\Customer\Time;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Services\ConnectionService;

class Holiday extends Model
{
    use HasFactory;

    const TABLE = 'holiday';

    const ID                            = 'id';
    const NAME                          = 'name';
    const BEGIND_DATE                   = 'begin_date';
    const END_DATE                      = 'end_date';
    const CREATED_BY                    = 'created_by';
    const UPDATED_BY                    = 'updated_by';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::ID,
        self::NAME,
        self::BEGIND_DATE,
        self::END_DATE,
        self::CREATED_BY,
        self::UPDATED_BY,
    ];
}
