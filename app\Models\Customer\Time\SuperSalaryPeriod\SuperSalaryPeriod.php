<?php

namespace App\Models\Customer\Time\SuperSalaryPeriod;

use App\Services\ConnectionService;
use App\Http\Tools\DateTool;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class SuperSalaryPeriod extends Model
{
    use HasFactory;

    const ID         = 'id';
    const YEAR       = 'year';
    const MONTH      = 'month';
    const CREATED_BY = 'created_by';
    const UPDATED_BY = 'updated_by';

    const RELATION_SUPER_MAIN_SALARY_PERIOD_DTLS  = 'super_main_salary_period_dtl';
    const RELATION_SUPER_OTHER_SALARY_PERIOD_DTLS = 'super_other_salary_period_dtls';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::YEAR,
        self::MONTH,
        self::CREATED_BY,
        self::UPDATED_BY,
    ];

    public function getNameAttribute()
    {
        return "$this->year $this->month";
    }

    public function super_main_salary_period_dtl()
    {
        return $this->hasOne(SuperSalaryPeriodDtl::class)->where(SuperSalaryPeriodDtl::TYPE, 0);
    }

    public function super_other_salary_period_dtls()
    {
        return $this->hasMany(SuperSalaryPeriodDtl::class)->where(SuperSalaryPeriodDtl::TYPE, 1);
    }

    public function getSuperSalaryPeriodDtl($departmentId) 
    {
        return $this->super_salary_period_dtls()->whereHas(SuperSalaryPeriodDtl::RELATION_SUPER_SALARY_PERIOD_SUB_ONE_DTLS, function (Builder $query) use ($departmentId) {
            $query->where(SuperSalaryPeriodSubOneDtl::DEPARTMENT_ID, $departmentId);
        })->first();
    }

    public function getDate()
    {
        return DateTool::createDate($this->year, $this->month, 1);
    }
}
