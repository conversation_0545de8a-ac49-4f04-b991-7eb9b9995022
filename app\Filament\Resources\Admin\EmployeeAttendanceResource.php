<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Models\Constant\ConstData;
use App\Models\Customer\Employee;
use App\Models\Customer\Time\Attendance\EmployeeAttendance;
use App\Jobs\ProcessEmployeeAttendance;
use App\Services\ConnectionService;

use App\Filament\Resources\Admin\EmployeeAttendanceResource\Pages;
use App\Filament\Resources\Admin\EmployeeAttendanceResource\RelationManagers;
use App\Http\Tools\DateTool;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Models\Customer\Time\SuperSalaryPeriod\SuperSalaryPeriod;
use App\Services\Time\SuperSalaryPeriodService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Actions\Action;
use Filament\Tables\Enums\FiltersLayout;
use Illuminate\Database\Eloquent\Builder;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Database\Eloquent\Collection;

class EmployeeAttendanceResource extends Can
{
    protected static ?string $model = EmployeeAttendance::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Ирцийн хяналт';
    protected static ?string $modelLabel = 'ирцийн хяналт';
    protected static ?int $navigationSort = 4;
    protected static ?string $navigationGroup = 'Ирц';
    protected static ?string $slug = 'employee_attendances';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('id')
                    ->label('Код')
                    ->inlineLabel(true),
                Forms\Components\TextInput::make('att_date')
                    ->label('Огноо')
                    ->inlineLabel(true),
                Forms\Components\TextInput::make('getFullNameAttribute')
                    ->label('Овог нэр')
                    ->formatStateUsing(fn (EmployeeAttendance $record): string => $record->employee->getFullNameAttribute())
                    ->inlineLabel(true),
                Forms\Components\TextInput::make('getWorkTimeByHBAttribute')
                    ->label('Ажилсан цаг')
                    ->formatStateUsing(fn (EmployeeAttendance $record): string => $record->getWorkTimeByHBAttribute())
                    ->inlineLabel(true),
                Forms\Components\TextInput::make('getLateTimeByHBAttribute')
                    ->label('Хоцорсон цаг')
                    ->formatStateUsing(fn (EmployeeAttendance $record): string => $record->getLateTimeByHBAttribute())
                    ->inlineLabel(true),
                Forms\Components\TextInput::make('getAbsentTimeByHBAttribute')
                    ->label('Тасалсан цаг')
                    ->formatStateUsing(fn (EmployeeAttendance $record): string => $record->getAbsentTimeByHBAttribute())
                    ->inlineLabel(true),
                Forms\Components\TextInput::make('getFurloughTimeByHBAttribute')
                    ->label('Чөлөө авсан цаг')
                    ->formatStateUsing(fn (EmployeeAttendance $record): string => $record->getFurloughTimeByHBAttribute())
                    ->inlineLabel(true),
                Forms\Components\TextInput::make('getSickTimeByHBAttribute')
                    ->label('Өвчтэй цаг')
                    ->formatStateUsing(fn (EmployeeAttendance $record): string => $record->getSickTimeByHBAttribute())
                    ->inlineLabel(true),
                Forms\Components\TextInput::make('getEffortTimeByHBAttribute')
                    ->label('Идэвхийн цаг')
                    ->formatStateUsing(fn (EmployeeAttendance $record): string => $record->getEffortTimeByHBAttribute())
                    ->inlineLabel(true),
                Forms\Components\TextInput::make('getStrRawAttendanceAttribute')
                    ->label('Ирц бүртгүүлсэн байдал')
                    ->formatStateUsing(fn (EmployeeAttendance $record): string => $record->getStrRawAttendanceAttribute())
                    ->inlineLabel(true),
                Forms\Components\TextInput::make('created_at')
                    ->label('Үүссэн огноо')
                    ->formatStateUsing(fn (EmployeeAttendance $record): string => $record->created_at->format('Y-m-d H:i:s'))
                    ->inlineLabel(true),
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('employee.department.name')->label('Хэлтэс')->sortable(),
                Tables\Columns\TextColumn::make('employee.fullName')->label('Овог нэр')->sortable(),
                Tables\Columns\TextColumn::make('att_date')->label('Огноо')->sortable(),
                Tables\Columns\TextColumn::make('workTimeByHB')->label('Ажилсан')->sortable(),
                Tables\Columns\TextColumn::make('lateTimeByHB')->label('Хоцорсон')->sortable(),
                Tables\Columns\TextColumn::make('absentTimeByHB')->label('Тасалсан')->sortable(),
                Tables\Columns\TextColumn::make('effortTimeByHB')->label('Идэвх')->sortable(),
            ])
            ->headerActions([
                Action::make('calcAttendance')
                    ->label('Ирц тооцоолох')
                    ->form([
                        Forms\Components\Select::make('salary_period_id')
                        ->label('Үечлэл')
                        ->options(function () {
                            $cn = ConnectionService::connectionNameByUser(auth()->user());
                            return resolve(SuperSalaryPeriodService::class)->getSuperSalaryPeriodsWithPluck($cn);
                        })
                        ->required(),
                    ])
                    ->icon('heroicon-m-calculator')
                    ->action(function (array $data) {
                        $salaryPeriod = SuperSalaryPeriod::find($data['salary_period_id']);
                        ProcessEmployeeAttendance::dispatch(auth()->user(), $salaryPeriod->year, $salaryPeriod->month);
                    }),
                ])
            ->filters([
                Filter::make('filterDates')
                    ->form([
                        Forms\Components\Select::make('employee_id')
                            ->label('Ажилтан')
                            ->options(Employee::all()->pluck(Employee::FULL_NAME, ConstData::ID))
                            ->searchable(),
                        Forms\Components\DatePicker::make('begin_date')->label('Эхлэх огноо')->default(DateTool::getYesterday()),
                        Forms\Components\DatePicker::make('end_date')->label('Дуусах огноо')->default(now()),
                    ])->columns(3)
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['employee_id'],
                                fn (Builder $query, $employeeId): Builder => $query->where('employee_id', $employeeId),
                            )
                            ->when(
                                $data['begin_date'],
                                fn (Builder $query, $date): Builder => $query->whereDate('att_date', '>=', $date),
                            )
                            ->when(
                                $data['end_date'],
                                fn (Builder $query, $date): Builder => $query->whereDate('att_date', '<=', $date),
                            )
                            ;
                    }),
                ], layout: FiltersLayout::Modal)
                ->filtersFormWidth(MaxWidth::FourExtraLarge)
                ->hiddenFilterIndicators()
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->action(function (Collection $records) {
                            foreach ($records as $record) {
                                $cn = ConnectionService::connectionNameByUser(auth()->user());
                                EmployeeAttendanceDtl::on($cn)->where(EmployeeAttendanceDtl::EMPLOYEE_ATTENDANCE_ID, $record->id)->delete();
                                $record->delete();
                            }
                        })
                    ,
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\EmployeeAttendanceDtlsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEmployeeAttendances::route('/'),
            'view' => Pages\ViewEmployeeAttendance::route('/{record}'),
        ];
    }
}
