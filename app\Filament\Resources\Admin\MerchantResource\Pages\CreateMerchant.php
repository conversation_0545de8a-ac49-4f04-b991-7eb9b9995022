<?php

namespace App\Filament\Resources\Admin\MerchantResource\Pages;

use App\Models\Constant\ConstData;
use App\Models\EmployeeUser;
use App\Models\EmployeeRole;
use App\Models\Customer\MerchantEmployeeUser;
use App\Filament\Resources\Admin\MerchantResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateMerchant extends CreateRecord
{
    protected static string $resource = MerchantResource::class;

    protected function afterCreate(): void
    {
        $merchant             = $this->record;
        $newEmployeeUsers     = $this->data['employee_users'];
        $employeeRoleMerchant = EmployeeRole::where(EmployeeRole::NAME, ConstData::EMPLOYEE_ROLE_MERCHANT)->first();
        // Role байхгүй бол алдаа хаяна
        if (!$employeeRoleMerchant) {
            throw new \RuntimeException('Merchant role not found!');
        }
        foreach ($newEmployeeUsers as $key => $employeeUserId) {
            MerchantEmployeeUser::create([
                MerchantEmployeeUser::MERCHANT_ID      => $merchant->id,
                MerchantEmployeeUser::EMPLOYEE_USER_ID => $employeeUserId
            ]);
            $employeeUser = EmployeeUser::find($employeeUserId);
            if (!$employeeUser) {
                continue; // null бол алгас
            }
            if ($employeeUser->employee_roles()->where('employee_roles.id', $employeeRoleMerchant->id)->exists()) {
                continue;
            }
            $alreadyHasRole = $employeeUser->employee_roles()->where('employee_roles.id', $employeeRoleMerchant->id)->exists();
            if (!$alreadyHasRole) {
                $employeeUser->employee_roles()->attach($employeeRoleMerchant->id); // илүү шууд, хурдан
            }
        }
    }
}
