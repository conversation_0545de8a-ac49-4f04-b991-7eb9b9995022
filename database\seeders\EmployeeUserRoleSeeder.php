<?php

namespace Database\Seeders;

use App\Models\Constant\ConstData;
use App\Models\EmployeeRole;
use App\Models\EmployeeUser;
use Illuminate\Database\Seeder;

class EmployeeUserRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $employeeUsers = EmployeeUser::doesntHave(EmployeeUser::RELATION_EMPLOYEE_ROLES)->get();
        $employeeRole  = EmployeeRole::where(EmployeeRole::NAME, ConstData::EMPLOYEE_ROLE_EMPLOYEE)->first();
        if (!isset($employeeRole))
            return;
        foreach ($employeeUsers as $key => $employeeUser) {
            $employeeUser->employee_roles()->attach($employeeRole->id);
        }
    }
}
