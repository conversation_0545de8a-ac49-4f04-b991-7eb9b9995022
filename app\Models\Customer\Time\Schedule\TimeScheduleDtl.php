<?php

namespace App\Models\Customer\Time\Schedule;

use App\Http\Tools\DateTool;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TimeScheduleDtl extends Model
{
    use HasFactory;
    const TABLE = 'time_schedule_dtls';

    const ID                    = 'id';
    const TIME_SCHEDULE_ID      = 'time_schedule_id';
    const TYPE                  = 'type';
    const DAY_OF_WEEK           = 'day_of_week';
    const ADMIT_LATE_MIN        = 'admit_late_min';
    const HAS_BREAK_TIME        = 'has_break_time';
    const BFR_MIN               = 'bfr_min';
    const AFR_MIN               = 'afr_min';
    const BEGIN_TIME_STR        = 'begin_time_str';
    const END_TIME_STR          = 'end_time_str';
    const RB_TIME_STR           = 'rb_time_str';
    const RE_TIME_STR           = 're_time_str';
    const WORK_MIN              = 'work_min';
    const OVER_BEGIN_TIME_STR   = 'over_begin_time_str';
    const OVER_END_TIME_STR     = 'over_end_time_str';
    const CREATED_BY            = 'created_by';
    const UPDATED_BY            = 'updated_by';

    const RELATION_TIME_SCHEDULE         = 'time_schedule';
    const RELATION_TIME_SCHEDULE_DTL_BTS = 'time_schedule_dtl_bts';

    const TYPE_SIMPLE           = 1;
    const TYPE_DYNAMIC          = 2;
    const TYPE_OVER             = 3;
    const TYPE_SIMPLE_OVER      = 4;
    const TYPE_DYNAMIC_OVER     = 5;

    const ALL_TYPES = [
        self::TYPE_SIMPLE,
        self::TYPE_DYNAMIC,
        self::TYPE_OVER,
        self::TYPE_SIMPLE_OVER,
        self::TYPE_DYNAMIC_OVER,
    ];

    const BEGIN_DATE_TIME       = 'begin_date_time';
    const END_DATE_TIME         = 'end_date_time';

    const OVER_BEGIN_DATE_TIME  = 'over_begin_date_time';
    const OVER_END_DATE_TIME    = 'over_end_date_time';

    protected $fillable = [
        self::ID,
        self::TIME_SCHEDULE_ID,
        self::TYPE,
        self::DAY_OF_WEEK,
        self::ADMIT_LATE_MIN,
        self::HAS_BREAK_TIME,
        self::BFR_MIN,
        self::AFR_MIN,
        self::BEGIN_TIME_STR,
        self::END_TIME_STR,
        self::RB_TIME_STR,
        self::RE_TIME_STR,
        self::WORK_MIN,
        self::OVER_BEGIN_TIME_STR,
        self::OVER_END_TIME_STR,
        self::CREATED_BY
    ];

    public function calcFields() {
        switch ($this->type) {
            case TimeScheduleDtl::TYPE_SIMPLE:
                $this->rb_time_str = null;
                $this->re_time_str = null;
                $this->work_min    = null;
                break;
            case TimeScheduleDtl::TYPE_DYNAMIC:
                $this->begin_time_str = null;
                $this->end_time_str   = null;
                break;
            default:
                break;
        }
    }

    protected $casts = [
        self::HAS_BREAK_TIME => 'boolean',
    ];

    public function hasOverTime() {
        return $this->type == 3 || $this->type == 4 || $this->type == 5;
    }

    public function setOverBeginAndEndTime($salaryDate) {
        if ($this->type == 3 || $this->type == 4 || $this->type == 5) {
            $overBeginTimeStrArray      = explode(':', $this->over_begin_time_str);
            $overEndTimeStrArray        = explode(':', $this->over_end_time_str);
            $overBeginDateWithTime      = DateTool::setDateTime($salaryDate, $overBeginTimeStrArray[0], $overBeginTimeStrArray[1]);
            $overEndDateWithTime        = DateTool::setDateTime($salaryDate, $overEndTimeStrArray[0], $overEndTimeStrArray[1]);
            $this->over_begin_date_time = $overBeginDateWithTime;
            $this->over_end_date_time   = $overEndDateWithTime;
        }
    }

    public function setSimpleBeginAndEndTime($salaryDate) {
        $beginTimeStrArray  = explode(':', $this->begin_time_str);
        $endTimeStrArray    = explode(':', $this->end_time_str);
        $beginDateWithTime  = DateTool::setDateTime($salaryDate, $beginTimeStrArray[0], $beginTimeStrArray[1]);

        $is_midnight        = $this->end_time_str == '00:00';
        $endDateWithTime    = DateTool::setDateTime($is_midnight ? DateTool::addDays($salaryDate, 1) : $salaryDate, $endTimeStrArray[0], $endTimeStrArray[1]);

        $this->begin_date_time = $beginDateWithTime;
        $this->end_date_time   = $endDateWithTime;
    }

    public function setDynamicBeginAndEndTime($salaryDate, $deviceData, $timeRequests) {
        $rbTimeStrArray = explode(':', $this->rb_time_str);
        $reTimeStrArray = explode(':', $this->re_time_str);

        $rbDateWithTime = DateTool::setDateTime($salaryDate, $rbTimeStrArray[0], $rbTimeStrArray[1]);
        $reDateWithTime = DateTool::setDateTime($salaryDate, $reTimeStrArray[0], $reTimeStrArray[1]);

        $btMin       = $this->getBtMinute();
        $rangeMin    = $this->work_min + $btMin;
        $timeRequest = $timeRequests->where('att_status', 3)
            ->where('begin_date', '<=', $salaryDate)
            ->where('end_date', '>=', $salaryDate)
            ->sortBy('begin_date')->first();
        if (count($deviceData) > 0) {
            $hasBreak = false;
            foreach ($deviceData as $key => $deviceDatum) {
                if ($deviceDatum->register_date < $rbDateWithTime) {
                    //  07:00-с өмнө ирсэн байвал 
                    $this->begin_date_time = $rbDateWithTime;
                    $hasBreak = true;
                } else if ($deviceDatum->register_date >= $rbDateWithTime && $deviceDatum->register_date <= $reDateWithTime) {
                    //  07:00 болон түүнээс хойш мөн 10:00 болон үүнээс өмнө ирсэн байвал 
                    $this->begin_date_time = DateTool::setZeroSecond($deviceDatum->register_date);
                    $hasBreak = true;
                } else {
                    $beginDateTime = $reDateWithTime;
                    if (isset($timeRequest)) {
                        if ($timeRequest->begin_date < $reDateWithTime && $timeRequest->begin_date > $rbDateWithTime)
                            $beginDateTime = $timeRequest->begin_date;
                        else if ($timeRequest->begin_date <= $reDateWithTime && $timeRequest->begin_date <= $rbDateWithTime)
                            $beginDateTime = $rbDateWithTime;
                    }
                    $this->begin_date_time = $beginDateTime;
                    $hasBreak = true;
                }
                $this->end_date_time = DateTool::addMinute($this->begin_date_time, $rangeMin);

                if ($hasBreak)
                    break;
            }
        } else {
            $this->begin_date_time = $timeRequest ? $timeRequest->begin_date : $reDateWithTime;
            $this->end_date_time = DateTool::addMinute($this->begin_date_time, $rangeMin);
        }
    }

    public function getBtMinute() {
        $timeScheduleDtlBts = $this->time_schedule_dtl_bts;
        $mins = 0;
        foreach ($timeScheduleDtlBts as $key => $value) {
            $btStart = strtotime($value->begin_time_str);
            $btEnd   = strtotime($value->end_time_str);
            $mins   += ($btEnd - $btStart) / 60;
        }
        return $mins;
    }

    /*
        Тухайн өдөр хэдэн цаг ажиллаж байгааг тооцох
     */
    public function getScheduleTime() {
        $carbonBeginDate = DateTool::parseCarbonDate($this->begin_date_time);
        $carbonEndDate   = DateTool::parseCarbonDate($this->end_date_time);
        $timeScheduleDtlBts = $this->time_schedule_dtl_bts;

        $mins = $carbonBeginDate->diffInMinutes($carbonEndDate);

        foreach ($timeScheduleDtlBts as $key => $value) {
            $btStart = strtotime($value->begin_time_str);
            $btEnd   = strtotime($value->end_time_str);
            $mins   -= ($btEnd - $btStart) / 60;
        }
        return $mins;
    }

    public function getHashStr() {
        $type               = $this->type;
        $beginTimeStr       = $this->begin_time_str;
        $endTimeStr         = $this->end_time_str;
        $bfrMin             = $this->bfr_min;
        $afrMin             = $this->afr_min;
        $dayOfWeek          = $this->day_of_week;
        $admitLateMin       = $this->admit_late_min;
        $rbTimeStr          = $this->rb_time_str;
        $reTimeStr          = $this->re_time_str;
        $workMin            = $this->work_min;
        $overBeginTimeStr   = $this->over_begin_time_str;
        $overEndTimeStr     = $this->over_end_time_str;
        switch ($type) {
            case self::TYPE_SIMPLE:
                $hashTimeStr = $dayOfWeek.'-'.$admitLateMin.'-'.$bfrMin.'-'.$afrMin.'-'.$beginTimeStr.'-'.$endTimeStr;
                break;
            case self::TYPE_DYNAMIC:
                $hashTimeStr = $dayOfWeek.'-'.$admitLateMin.'-'.$bfrMin.'-'.$afrMin.'-'.$rbTimeStr.'-'.$reTimeStr.'-'.$workMin;
                break;
            case self::TYPE_OVER:
                $hashTimeStr = $dayOfWeek.'-'.$overBeginTimeStr.'-'.$overEndTimeStr;
                break;
            case self::TYPE_SIMPLE_OVER:
                $hashTimeStr = $dayOfWeek.'-'.$admitLateMin.'-'.$bfrMin.'-'.$afrMin.'-'.$beginTimeStr.'-'.$endTimeStr.'-'.$overBeginTimeStr.'-'.$overEndTimeStr;
                break;
            case self::TYPE_DYNAMIC_OVER:
                $hashTimeStr = $dayOfWeek.'-'.$admitLateMin.'-'.$bfrMin.'-'.$afrMin.'-'.$rbTimeStr.'-'.$reTimeStr.'-'.$workMin.'-'.$overBeginTimeStr.'-'.$overEndTimeStr;
                break;
            default:
                break;
        }
        $timeScheduleDtlBts = $this->time_schedule_dtl_bts;
        if ($timeScheduleDtlBts) {
            foreach ($timeScheduleDtlBts as $key => $timeScheduleDtlBt) {
                $hashTimeStr .= '-' . $timeScheduleDtlBt->getHashStr();
            }
        }
        return $hashTimeStr;
    }

    public function clearOtherColumns() {
        unset($this->time_schedule_dtl_bts);
    }
}
