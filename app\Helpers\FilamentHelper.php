<?php

namespace App\Helpers;

use Illuminate\Support\Facades\File;

class FilamentHelper
{
    public static function getFilamentResources(): array
    {
        $resourcePath = app_path('Filament/Resources/Admin');
        $namespace = "App\\Filament\\Resources\\Admin\\";

        if (!File::exists($resourcePath)) {
            return [];
        }

        $files = File::allFiles($resourcePath);
        $resources = [];

        foreach ($files as $file) {
            $className = pathinfo($file->getFilename(), PATHINFO_FILENAME);
            $fullClassName = $namespace . $className;
            if (class_exists($fullClassName) && is_subclass_of($fullClassName, \Filament\Resources\Resource::class)) {
                $resources[$className] = str_replace('Resource', '', $className);
            }
        }
        return $resources;
    }
}
