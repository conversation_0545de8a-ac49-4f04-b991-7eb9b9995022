<?php

namespace App\Models\Constant;

class Message
{
    private static $authenticationMessages = [
        1 => 'Таны хуучин нууц үг тохирохгүй байна.',
        2 => 'Таны нэвтрэх нэр эсвэл нууц үг тохирохгүй байна.',
        3 => 'Идэвхжүүлсэн кодны хугацаа дууссан байна.',
        4 => 'Та бүртгэлтэй төхөөрөмжөөс нэвтэрч орно уу',
        5 => 'Таны эрх хүрэхгүй байна.',
        6 => 'Копанийн тохиргоо олдсонгүй.',
    ];

    private static $systemMessages = [
        1 => 'DatabaseConfig тохиргоо олдсонгүй.',
        2 => 'Байгууллага олдсонгүй.',
        3 => 'Ийт нэртэй бааз үүссэн байна.',
        4 => 'UserOrgDbConfigMapping тохиргоо олдсонгүй.',
        5 => 'Хэрэглэгч олдсонгүй.',
        6 => 'SmsToken олдсонгүй. ',
        7 => 'SmsToken тохирохгүй байна.',
        8 => 'Хэрэглэгч идэвхгүй төлөвтэй байна.',
        9 => 'Утасны дугаар бүртгэгдээгүй байна.',
        10 => 'Утасны дугаар бүртгэгдсэн байна.',
        11 => 'Ажилтны voucher uniq id-нь буруу байна',
        12 => 'Ажилтны voucher uniq id-ний хугацаа нь дууссан байна',
        13 => 'Зарцуулалх дүн voucher-н дүнгээс их байна',
        14 => 'Ажилтан дээр тохируулагдсан ваучер олдсонгүй.',
        15 => 'Ажилтан дээр тохируулагдсан Мерчант олдсонгүй',
    ];

    private static $infoMessages = [
        101 => "Алба олдсонгүй.",
        102 => "Албан тушаал олдсонгүй.",
        103 => "Ажилтан олдсонгүй.",
    ];

    private static $configMessages = [
        1 => "Тохиргоо олдсонгүй.",
    ];

    private static $employeeMessages = [
        201 => "Ажилтан идэвхгүй төлөвтэй байна.",
        202 => "Ажилтан идэвхтэй төлөвтэй байна.",
    ];

    private static $timeMessages = [
        301 => "Цагийн хуваарь олдсонгүй.",
        302 => "Цагийн хуваарийн эхлэх огноо нь давхцаж байна.",
        303 => "Цагийн хуваарийг өөчрлөх боломжгүй. Учир нь эхлэх огноо нь өмнөх цагийн хуваарийн эхлэх огноотой ижил эсвэл урдуур байж болохгүй.",
        304 => "Цагийн хуваарийг өөчрлөх боломжгүй. Учир нь эхлэх огноо нь өмнөх цагийн хуваарийн эхлэх огноотой ижил эсвэл хойгуур байж болохгүй.",
        305 => "Цагийн хуваарийн нэр давхцаж байна.",
        306 => "Цалингийн үечлэл олдсонгүй. Та цалингийн үечлэлээ тохируулна уу.",
        307 => "Цалингийн үечлэл өөрчлөх боломжгүй байна. Учир нь цалингийн үечлэлийн төлөв нь өөрчлөгдсөн байна.",
        308 => "Эхлэх огноо нь дуусах огнооноос хойгуур байж болохгүй.",
        309 => "Багаас нь их рүү эрэмлэн огноогоо оруулна уу.",
        310 => "Төхөөрөмж олдсонгүй.",
        311 => "Цалингийн үечлэл олдсонгүй.",
        312 => "Ажилтаны бүлэг олдсонгүй.",
        313 => "Ирцийн задаргаа олдсонгүй.",
        314 => "EmployeeTimeSchedule's beginDate is wrong",
        315 => "Ажилтан дээр нэг өдөрт дээл тал нь 1 тушаал байх боломжтой.",
        316 => "Цагийн хуваарь давхцаж байна.",
        317 => "Ээлжийн цагийн хуваарь давхцаж байна.",
        318 => "Цалингийн үечлэлийн огнооноос хэтэрсэн байна.",
        319 => "Ажилтаны фонд цаг давхцаж байна.",
        320 => "Баяр ёслолын өдөр давхцаж байна.",
        321 => "Ирц олдсонгүй.",
        322 => "Хүсэлтийн тохиргоо олдсонгүй.",
        323 => "Хүсэлт олдсонгүй.",
        324 => "Хүсэлтийн төлөв өөрчлөгдсөн байна.",
        325 => "Ажилтаны утасны дугаар тохирохгүй байна.",
        327 => "Та хүсэлтийг шийдвэрлэсэн байна.",
        328 => "Хүсэлтийг баталсан эсвэл цуцалсан байвал боломжгүй.",
        329 => "Байршил олдсонгүй.",
        330 => "Таны байршил тохируулагдсан байршил биш байна.",
        331 => "Танд тохируулагдсан байршил алга байна.",
        332 => "Хамгийн багадаа 1 минутын дараа уг үйлдэл боломжтой.",
        333 => "Рөүтэр олдсонгүй.",
        334 => "Ажилласан цаг нь нөхөж амрах цагтай тэнцэхгүй байна. Та оруулаж буй цагаа шалгана уу.",
        335 => "Таны wifi router тохируулагдсан wifi router биш байна."
    ];

    private static $decisionMessages = [
        401 => "Тушаал шийдвэр олдсонгүй.",
    ];

    public static function getAuthMessage($key) {
        return self::$authenticationMessages[$key];
    }

    public static function getSystemMessage($key) {
        return self::$systemMessages[$key];
    }

    public static function getConfigMessage($key) {
        return self::$configMessages[$key];
    }

    public static function getInfoMessage($key) {
        return self::$infoMessages[$key];
    }

    public static function getEmployeeMessage($key) {
        return self::$employeeMessages[$key];
    }

    public static function getTimeMessage($key) {
        return self::$timeMessages[$key];
    }

    public static function getDecisionMessage($key) {
        return self::$decisionMessages[$key];
    }
}
