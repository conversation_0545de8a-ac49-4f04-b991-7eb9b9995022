# Time Request Validation - Count Limit Feature

## Overview

The `validateCountLimit` method in `TimeRequestValidationService` has been improved to actually count existing TimeRequest records and validate against configured limits for salary furlough requests.

## How It Works

### 1. Configuration Format

The salary furlough configurations use the `ChronoCountComponent` format in the `value` field:

```
count,staticType,count,chronoType,length
```

**Example**: `count,day,3,quarter,1`
- `staticType`: day (not used in validation logic)
- `count`: 3 (maximum allowed requests in the period)
- `chronoType`: quarter (defines the period: month, quarter, halfyear, year)
- `length`: 1 (not used in validation logic)

### 2. Validation Logic

1. **Period Calculation**: Based on the `chronoType`, calculate the current session period:
   - `month`: Current calendar month (e.g., 2024-02-01 to 2024-02-29)
   - `quarter`: Current quarter (e.g., Q1: 2024-01-01 to 2024-03-31)
   - `halfyear`: Current half-year (e.g., H1: 2024-01-01 to 2024-06-30)
   - `year`: Current year (e.g., 2024-01-01 to 2024-12-31)

2. **Count Existing Requests**: Query the database to count existing TimeRequest records where:
   - `employee_id` matches current user's employee
   - `att_status` matches the request type
   - `begin_date` falls within the calculated period
   - `confirm_status` is not cancelled (SENT, CONFIRMED, or TEMP_CONFIRMED)

3. **Validate Limit**: If existing count >= configured limit, reject the request

### 3. Supported Configuration IDs

- `ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY`
- `ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_KID_BIRTHDAY`
- `ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_HALFDAY`
- `ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE`

## Example Scenarios

### Scenario 1: Quarter-based Birthday Leave

**Configuration**: `count,day,quarter,1,2`
- Maximum 2 birthday leave requests per quarter

**Current Date**: 2024-02-15 (Q1)
**Period**: 2024-01-01 to 2024-03-31
**Existing Requests**: 1 birthday leave request in Q1
**New Request**: 2024-02-20
**Result**: ✅ VALID (1 < 2)

### Scenario 2: Monthly Kid Birthday Leave

**Configuration**: `count,day,month,1,1`
- Maximum 1 kid birthday leave request per month

**Current Date**: 2024-02-15
**Period**: 2024-02-01 to 2024-02-29
**Existing Requests**: 1 kid birthday leave request in February
**New Request**: 2024-02-25
**Result**: ❌ INVALID (1 >= 1)

### Scenario 3: Yearly No-Smoke Leave

**Configuration**: `count,day,year,1,5`
- Maximum 5 no-smoke leave requests per year

**Current Date**: 2024-08-15
**Period**: 2024-01-01 to 2024-12-31
**Existing Requests**: 3 no-smoke leave requests in 2024
**New Request**: 2024-08-20
**Result**: ✅ VALID (3 < 5)

## Error Messages

When the limit is exceeded, the system returns a detailed error message in Mongolian:

```
{AttStatusName}-ийн хүсэлтийн тоо одоогийн {SessionTypeName} хугацаанд ({PeriodStart} - {PeriodEnd}) хязгаарлагдсан тооноос ({Limit}) хэтэрсэн байна. Одоогийн тоо: {CurrentCount}
```

**Example**:
```
Төрсөн өдрийн чөлөө-ийн хүсэлтийн тоо одоогийн улирлын хугацаанд (2024-01-01 - 2024-03-31) хязгаарлагдсан тооноос (2) хэтэрсэн байна. Одоогийн тоо: 2
```

## Integration

The count limit validation is automatically called as part of the `validateValue` method, which is integrated into:

1. **TimeRequestRequest** - Form validation
2. **TimeRequestController** - Server-side validation

Both `validateValue1` (anchor-based) and `validateValue` (count-based) validations are executed for comprehensive validation.

## Database Query

The validation performs the following query:

```sql
SELECT COUNT(*) FROM time_requests 
WHERE employee_id = ? 
  AND att_status = ? 
  AND begin_date >= ? 
  AND begin_date <= ? 
  AND confirm_status IN (1, 3, 4)
```

This ensures only non-cancelled requests are counted towards the limit.

## Vacation Request Validation

### Overview

The `validateValue` method also handles `ATTENDANCE_STATUS_VACATION` requests with special logic for company work date validation.

### Configuration Format

Vacation configurations use the `ChronoAnchorComponent` format in the `value` field:

```
anchor,date_source,type,shift_count
```

**Example**: `anchor,employee_dtl.company_work_date,day,365`
- `date_source`: employee_dtl.company_work_date (uses employee's company work date)
- `type`: day (time unit for shift calculation)
- `shift_count`: 365 (number of units to add to the anchor date)

### Validation Logic

When `date_source` is `employee_dtl.company_work_date`:

1. **Get Employee Data**: Retrieve current user's employee and employee_dtl records
2. **Calculate Minimum Date**: `company_work_date + shift_count`
3. **Primary Validation**: Begin date must be greater than the calculated minimum date
4. **Secondary Validation**: **AND** begin date must be greater than ALL existing vacation requests' `begin_date + shift_count`

**Both conditions must be satisfied (AND logic)**

### Example Scenarios

#### Scenario 1: New Employee Vacation Request

**Configuration**: `anchor,employee_dtl.company_work_date,day,365`
- Employee must work for 1 year before taking vacation

**Employee Data**:
- `company_work_date`: 2023-06-01
- Current date: 2024-02-15

**Calculation**:
- Minimum date: 2023-06-01 + 365 days = 2024-05-31
- Request date: 2024-02-15
- Result: ❌ INVALID (2024-02-15 <= 2024-05-31)

#### Scenario 2: Experienced Employee with Previous Vacation

**Same Configuration**: `anchor,employee_dtl.company_work_date,day,365`

**Employee Data**:
- `company_work_date`: 2022-01-01
- Previous vacation request: 2023-08-15

**New Request**: 2024-02-15
**Primary Check**: 2024-02-15 > 2022-01-01 + 365 days = 2023-01-01 ✅
**Secondary Check**: 2024-02-15 > 2023-08-15 + 365 days = 2024-08-14 ❌
**Result**: ❌ INVALID (second condition fails)

#### Scenario 3: Valid Request After All Conditions

**Same Configuration**: `anchor,employee_dtl.company_work_date,day,365`

**Employee Data**:
- `company_work_date`: 2022-01-01
- Previous vacation request: 2023-08-15

**New Request**: 2024-08-20
**Primary Check**: 2024-08-20 > 2022-01-01 + 365 days = 2023-01-01 ✅
**Secondary Check**: 2024-08-20 > 2023-08-15 + 365 days = 2024-08-14 ✅
**Result**: ✅ VALID (both conditions satisfied)

### Error Messages

When validation fails, the system returns detailed error messages:

**For company work date validation**:
```
Эхлэх огноо {AttStatusName}-ийн хувьд компанид ажилласан огноо ({CompanyWorkDate}) + {ShiftCount} {Type} = {MinimumDate}-ээс хойш байх ёстой.
```

**For existing request validation**:
```
Эхлэх огноо {AttStatusName}-ийн хувьд өмнөх хүсэлтийн огноо ({ExistingRequestDate}) + {ShiftCount} {Type} = {ShiftedDate}-ээс хойш байх ёстой.
```

**Examples**:
```
Эхлэх огноо Амралт-ийн хувьд компанид ажилласан огноо (2023-06-01) + 365 day = 2024-05-31-ээс хойш байх ёстой.

Эхлэх огноо Амралт-ийн хувьд өмнөх хүсэлтийн огноо (2023-08-15) + 365 day = 2024-08-14-ээс хойш байх ёстой.
```

### Supported Configuration ID

- `ATTENDANCE_STATUS_VACATION`

### Integration

The vacation validation is automatically integrated into the same validation pipeline as salary furlough requests, ensuring comprehensive validation for all special request types.

## Furlough Duration Validation

### Overview

The `validateValue` method also handles furlough duration validation for SHORT, MEDIUM, and LONG furlough types, ensuring the duration between `begin_date` and `end_date` falls within configured ranges.

### Configuration Format

Furlough configurations use the `ChronoRangeComponent` format in the `value` field:

```
range,type,from,to
```

**Example**: `range,day,0,2`
- `type`: day (time unit for duration calculation)
- `from`: 0 (minimum duration)
- `to`: 2 (maximum duration)

### Validation Logic

1. **Check End Date**: Requires both `begin_date` and `end_date` to be provided
2. **Calculate Duration**: `end_date - begin_date` in the specified time unit
3. **Validate Range**: Duration must be within the configured `from` to `to` range (inclusive)

### Supported Configuration IDs

- `ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_SHORT`
- `ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_MEDIUM`
- `ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_LONG`

### Example Scenarios

#### Scenario 1: Short Furlough (0-2 days)

**Configuration**: `range,day,0,2`
- Short furlough must be 0-2 days duration

**Request**:
- `begin_date`: 2024-02-15
- `end_date`: 2024-02-16
- Duration: 1 day
- Result: ✅ VALID (1 is within 0-2 range)

#### Scenario 2: Medium Furlough (3-7 days)

**Configuration**: `range,day,3,7`
- Medium furlough must be 3-7 days duration

**Request**:
- `begin_date`: 2024-02-15
- `end_date`: 2024-02-16
- Duration: 1 day
- Result: ❌ INVALID (1 is not within 3-7 range)

#### Scenario 3: Long Furlough (8-30 days)

**Configuration**: `range,day,8,30`
- Long furlough must be 8-30 days duration

**Request**:
- `begin_date`: 2024-02-15
- `end_date`: 2024-03-01
- Duration: 15 days
- Result: ✅ VALID (15 is within 8-30 range)

### Supported Time Units

- `minute`: Minutes between dates
- `hour`: Hours between dates
- `halfday`: Half-days (12-hour periods)
- `day`: Calendar days
- `month`: Calendar months
- `quarter`: Quarters (3-month periods)
- `halfyear`: Half-years (6-month periods)
- `year`: Calendar years

### Error Messages

When duration validation fails:

```
Эхлэх болон дуусах огнооны зөрүү {AttStatusName}-ийн хувьд {MinDuration}-{MaxDuration} {TypeName} байх ёстой. Одоогийн зөрүү: {ActualDuration} {TypeName}
```

**Example**:
```
Эхлэх болон дуусах огнооны зөрүү Богино хугацаа чөлөө-ийн хувьд 0-2 өдөр байх ёстой. Одоогийн зөрүү: 5 өдөр
```

### Integration

The furlough duration validation is automatically integrated into the validation pipeline and works alongside:

1. **Anchor-based validation** (`validateValue1`)
2. **Count-based validation** (salary furlough types)
3. **Company work date validation** (vacation types)

All validations are executed to ensure comprehensive request validation.
