<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeRole extends Model
{
    use HasFactory;

    const ID          = 'id';
    const NAME        = 'name';
    const DESCRIPTION = 'description';

    protected $fillable = [
        self::NAME,
        self::DESCRIPTION,
    ];

    public function employee_users()
    {
        return $this->belongsToMany(EmployeeUser::class, 'employee_user_role');
    }
}
