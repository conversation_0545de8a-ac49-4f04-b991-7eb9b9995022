<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOtherTimeScheduleDtlBtsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('other_time_schedule_dtl_bts'))
            return;
        Schema::create('other_time_schedule_dtl_bts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('other_time_schedule_dtl_id')->constrained('other_time_schedule_dtls')->onDelete('cascade');
            $table->string('begin_time_str');
            $table->string('end_time_str');
            $table->boolean('check_io')->default(0);
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('other_time_schedule_dtl_bts');
    }
}
