<?php

namespace App\Service;

use App\Models\Customer\Position;

class PositionService
{
    public $userDepartmentService;
    public function __construct(UserDepartmentService $userDepartmentService) {
        $this->userDepartmentService = $userDepartmentService;
    }

    public function getPositionsByUser($connectionName, $user) {
        $isOrganizationRole = $this->userDepartmentService->isOrganizationRole($user);
        $positions = new Position();
        $positions = $positions->on($connectionName);
        if (!$isOrganizationRole) {
            $departmentIds = $this->userDepartmentService->getDepartmentIdsByUserId($user->id);
            if (!empty($departmentIds)) {
                $positions = $positions->whereIn(Position::DEPARTMENT_ID, $departmentIds);
            }
        }
        $positions = $positions->get();
        return $positions;
    }
}
