# Chrono Components - Formula Parsing Functionality

## Overview
All Chrono components now support parsing formula strings back into their individual component values. This enables proper form hydration when editing existing Configuration records.

## Components with Parsing Support

### 1. ChronoAnchorComponent
**Formula Format**: `anchor,date,type,shift`

**Examples**:
- Custom date: `anchor,2024-01-15,day,5`
- Model field: `anchor,employee.created_at,month,-2`

**Parsed Fields**:
- `type`: The chrono type (minute, hour, day, month, etc.)
- `shift_count`: The shift amount (can be negative)
- `date_source`: Either 'custom' or the model field path
- `custom_date`: The date value (only for custom dates)

### 2. ChronoRangeComponent
**Formula Format**: `range,type,from,to`

**Examples**:
- `range,day,5,10`
- `range,month,-3,2`

**Parsed Fields**:
- `type`: The chrono type
- `from`: The starting range value (can be negative)
- `to`: The ending range value (can be negative)

### 3. ChronoCountComponent
**Formula Format**: `count,staticType,count,chronoType,length`

**Examples**:
- `count,day,10,month,5`
- `count,year,15,quarter,-2`

**Parsed Fields**:
- `static_type`: The static chrono type
- `count`: The count value (can be negative)
- `chrono_type`: The dynamic chrono type
- `length`: The length value (can be negative)

## Implementation in ConfigurationResource

### Form Hydration
The EditConfiguration page uses Filament's `mutateFormDataBeforeFill` method to parse stored formula strings and populate form fields when editing records.

### Example Implementation
```php
// In EditConfiguration.php
protected function mutateFormDataBeforeFill(array $data): array
{
    $record = $this->getRecord();

    if ($record) {
        // Parse VALUE field for vacation_anchor
        if ($record->value && $record->config_id === 'ATTENDANCE_STATUS_VACATION') {
            $parsed = ChronoAnchorComponent::parseFormula($record->value);
            $data['vacation_anchor_type'] = $parsed['type'];
            $data['vacation_anchor_shift_count'] = $parsed['shift_count'];
            $data['vacation_anchor_date_source'] = $parsed['date_source'];
            $data['vacation_anchor_custom_date'] = $parsed['custom_date'];
        }

        // Parse VALUE1 field for anchor_config
        if ($record->value1) {
            $parsed = ChronoAnchorComponent::parseFormula($record->value1);
            $data['anchor_config_type'] = $parsed['type'];
            $data['anchor_config_shift_count'] = $parsed['shift_count'];
            $data['anchor_config_date_source'] = $parsed['date_source'];
            $data['anchor_config_custom_date'] = $parsed['custom_date'];
        }
    }

    return $data;
}
```

## Error Handling
All parsing methods handle invalid formulas gracefully by returning null values for all fields, ensuring the form doesn't break with malformed data.

## Formula Format
All components use the new standardized formula format:
- ChronoAnchorComponent: `anchor,date,type,shift`
- ChronoRangeComponent: `range,from_type,from_shift,to_type,to_shift`
- ChronoCountComponent: `count,static_type,count,chrono_type,length`

## Testing
Comprehensive tests cover:
- Valid formula parsing for all components
- Negative value handling
- Invalid formula graceful handling
- Edge cases (malformed strings, invalid component counts)

## Migration from Old Formats
If you have existing configurations using old formats, they need to be updated:

**Old ChronoAnchor format**: `2024-01-15+day*5` → **New**: `anchor,2024-01-15,day,5`
**Old ChronoRange format**: `5{day}-10{day}` → **New**: `range,day,5,10`
**Old ChronoCount format**: `{day},{month}*5,10` → **New**: `count,day,10,month,5`

Run tests with:
```bash
php artisan test tests/Feature/ChronoComponentsParsingTest.php
```

## Benefits
1. **Seamless Editing**: Users can now edit existing configurations without losing data
2. **Data Integrity**: Formula strings are properly parsed and validated
3. **User Experience**: Form fields are correctly populated when editing records
4. **Robust Error Handling**: Invalid formulas don't break the interface
5. **Consistent Behavior**: All chrono components work the same way

## Usage Flow
1. **Create**: User fills form → Components generate formula → Stored in database
2. **Edit**: Database formula → Parsed by component → Form fields populated → User edits → New formula generated → Stored in database

This creates a complete round-trip data flow that preserves all user input and provides a seamless editing experience.
