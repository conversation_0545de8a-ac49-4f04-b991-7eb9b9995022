<?php

namespace App\Models\Customer\Time\RequestConfig;

use App\Models\EmployeeUser;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Services\ConnectionService;
use App\Services\EmployeeUserService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RequestConfigDtl extends Model
{
    use HasFactory;

    const ID                = 'id';
    const REQUEST_CONFIG_ID = 'request_config_id';
    const LIMIT_HOURS       = 'limit_hours';
    const TYPE              = 'type';
    const EMPLOYEE_USER_ID  = 'employee_user_id';

    const RELATION_REQUEST_CONFIG = 'requestConfig';
    const RELATION_EMPLOYEE_USER  = 'employee_user';

    const TYPE_CHULUU         = 'chuluu';
    const TYPE_ILUU_TSAG      = 'iluu_tsag';
    const TYPE_GADUUR_AJILLAH = 'gaduur_ajillah';
    const TYPE_VACATION      = 'vacation';
    const TYPE_FURLOUGH_SHORT = 'furlough_short';
    const TYPE_FURLOUGH_MEDIUM = 'furlough_medium';
    const TYPE_FURLOUGH_LONG = 'furlough_long';
    const TYPE_SALARY_FURLOUGH_BIRTHDAY = 'salary_furlough_birthday';
    const TYPE_SALARY_FURLOUGH_KID_BIRTHDAY = 'salary_furlough_kid_birthday';
    const TYPE_SALARY_FURLOUGH_HALFDAY = 'salary_furlough_halfday';
    const TYPE_SALARY_FURLOUGH_NOSMOKE = 'salary_furlough_nosmoke';

    const TYPE_OPTIONS = [
        self::TYPE_CHULUU         => 'Чөлөө',
        self::TYPE_ILUU_TSAG      => 'Илүү цаг',
        self::TYPE_GADUUR_AJILLAH => 'Гадуур ажиллах',
        self::TYPE_VACATION       => 'Ээлжийн амралт',
        self::TYPE_FURLOUGH_SHORT => 'Чөлөө (Богино)',
        self::TYPE_FURLOUGH_MEDIUM => 'Чөлөө (Дунд)',
        self::TYPE_FURLOUGH_LONG => 'Чөлөө (Урт)',
        self::TYPE_SALARY_FURLOUGH_BIRTHDAY => 'Цалинтай чөлөө (Төрсөн өдөр)',
        self::TYPE_SALARY_FURLOUGH_KID_BIRTHDAY => 'Цалинтай чөлөө (Хүүхдийн төрсөн өдөр)',
        self::TYPE_SALARY_FURLOUGH_HALFDAY => 'Цалинтай чөлөө (Хагас өдөр)',
        self::TYPE_SALARY_FURLOUGH_NOSMOKE => 'Цалинтай чөлөө (Тамхи татдаггүй)',
    ];

    protected $fillable = [
        self::REQUEST_CONFIG_ID,
        self::LIMIT_HOURS,
        self::TYPE,
        self::EMPLOYEE_USER_ID
    ];

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    public function getEmployeeAttendanceStatusAttribute(): string
    {
        switch ($this->type) {
            case RequestConfigDtl::TYPE_CHULUU:
                $attStatus = EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH;
                break;
            case RequestConfigDtl::TYPE_ILUU_TSAG:
                $attStatus = EmployeeAttendanceDtl::ATTENDANCE_STATUS_OVER;
                break;
            case RequestConfigDtl::TYPE_GADUUR_AJILLAH: 
                $attStatus = EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK;
                break;
            case RequestConfigDtl::TYPE_VACATION:
                $attStatus = EmployeeAttendanceDtl::ATTENDANCE_STATUS_VACATION;
                break;
            case RequestConfigDtl::TYPE_FURLOUGH_SHORT:
                $attStatus = EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH_SHORT;
                break;
            case RequestConfigDtl::TYPE_FURLOUGH_MEDIUM:
                $attStatus = EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH_MEDIUM;
                break;
            case RequestConfigDtl::TYPE_FURLOUGH_LONG:
                $attStatus = EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH_LONG;
                break;
            case RequestConfigDtl::TYPE_SALARY_FURLOUGH_BIRTHDAY:
                $attStatus = EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY;
                break;
            case RequestConfigDtl::TYPE_SALARY_FURLOUGH_KID_BIRTHDAY:
                $attStatus = EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_KID_BIRTHDAY;
                break;
            case RequestConfigDtl::TYPE_SALARY_FURLOUGH_HALFDAY:
                $attStatus = EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_HALFDAY;
                break;
            case RequestConfigDtl::TYPE_SALARY_FURLOUGH_NOSMOKE:
                $attStatus = EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE;
                break;
        }
        return $attStatus;
    }

    public function requestConfig()
    {
        return $this->belongsTo(RequestConfig::class);
    }

    public function employee_user()
    {
        return $this->belongsTo(EmployeeUser::class);
    }

    public function getEmployeeDepartmentNameAttribute(): string
    {
        $employeeUser = resolve(EmployeeUserService::class)->getEmployeeUser($this->employee_user_id);
        if (!isset($employeeUser))
            return '';
        return $employeeUser->getEmployeeDepartmentName();
    }

    public function getEmployeePositionNameAttribute(): string
    {
        $employeeUser = resolve(EmployeeUserService::class)->getEmployeeUser($this->employee_user_id);
        if (!isset($employeeUser))
            return '';
        return $employeeUser->getEmployeePositionName();
    }

    public function getEmployeeDisplayNameAttribute(): string
    {
        $employeeUser = resolve(EmployeeUserService::class)->getEmployeeUser($this->employee_user_id);
        if (!isset($employeeUser))
            return '';
        return $employeeUser->getEmployeeDisplayName();
    }
}
