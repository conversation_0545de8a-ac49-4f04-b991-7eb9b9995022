<?php

namespace App\Models;

use App\Models\Constant\ConstData;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Configuration extends Model
{
    use HasFactory;

    protected $connection     = ConstData::ADMIN_DB;
    const TABLE               = 'configurations';

    const ID                  = 'id';
    const CONFIG_ID           = 'config_id';
    const VALUE               = 'value';
    const CREATED_BY          = 'created_by';
    const UPDATED_BY          = 'updated_by';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        self::ID,
        self::CONFIG_ID,
        self::VALUE,
        self::CREATED_BY,
    ];
}
