<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('employee_voucher_spents'))
            return;
        Schema::create('employee_voucher_spents', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('employee_id');
            $table->unsignedDecimal('amount', $precision = 24, $scale = 2);
            $table->string('merchant_company_name');
            $table->string('merchant_location_name');
            $table->unsignedBigInteger('merchant_employee_user_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employee_voucher_spents');
    }
};
