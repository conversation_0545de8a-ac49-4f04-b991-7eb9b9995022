<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Models\Constant\ConstData;
use App\Models\Customer\Department;
use App\Filament\Resources\Admin\RequestConfigResource\Pages;
use App\Filament\Resources\Admin\RequestConfigResource\RelationManagers;
use App\Models\Customer\Time\RequestConfig\RequestConfig;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;

class RequestConfigResource extends Can
{
    protected static ?string $model = RequestConfig::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Цагийн хүсэлтийн тохиргоо';
    protected static ?string $modelLabel = 'Цагийн хүсэлтийн тохиргоо';
    protected static ?int $navigationSort = 11;
    protected static ?string $navigationGroup = 'Цагийн хүсэлт';
    protected static ?string $slug = 'request-configs';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make('department_id')
                            ->label('Хэлтэс')
                            ->unique(ignoreRecord: true)
                            ->options(Department::all()->pluck(ConstData::NAME, ConstData::ID))
                            ->searchable()
                            ->required()
                            ->live()
                            // ->afterStateUpdated(function ($state, callable $set, callable $get, $livewire) {
                            //     $livewire->dispatch('department_id_updated', $state);
                            // }),

                    ])
                    ->columns(1)
                    ->columnSpan(['lg' => fn (?RequestConfig $record) => $record === null ? 3 : 2]),

            Forms\Components\Section::make()
                ->schema([
                    Forms\Components\Placeholder::make('created_at')
                        ->label('Created at')
                        ->content(fn (RequestConfig $record): ?string => $record->created_at?->diffForHumans()),

                    Forms\Components\Placeholder::make('updated_at')
                        ->label('Last modified at')
                        ->content(fn (RequestConfig $record): ?string => $record->updated_at?->diffForHumans()),
                ])
                ->columnSpan(['lg' => 1])
                ->hidden(fn (?RequestConfig $record) => $record === null),
        ])
        ->columns(3);
        }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('department.name')->label('Хэлтсийн нэр')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\RequestConfigDtlsRelationManager::class,
            RelationManagers\RequestConfigEmployeesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRequestConfigs::route('/'),
            'create' => Pages\CreateRequestConfig::route('/create'),
            'edit' => Pages\EditRequestConfig::route('/{record}/edit'),
        ];
    }
}
