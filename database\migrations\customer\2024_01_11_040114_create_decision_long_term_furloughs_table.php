<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDecisionLongTermFurloughsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('decision_long_term_furloughs'))
        return;
        Schema::create('decision_long_term_furloughs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('decision_id')->constrained();
            $table->date('begin_date');
            $table->date('end_date');
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('decision_long_term_furloughs');
    }
}
