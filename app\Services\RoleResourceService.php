<?php

namespace App\Services;

use App\Models\RoleResource;

class RoleResourceService
{
    public function __construct()
    {
    }

    function canView($class) {
        $user = auth()->user();
        if (!$user) return false;
        foreach ($user->roles as $role) {
            $resourceName = class_basename($class);
            if (RoleResource::where('role_id', $role->id)->where('resource', $resourceName)->exists())
                return true;
        }
        return false;
    }
}
