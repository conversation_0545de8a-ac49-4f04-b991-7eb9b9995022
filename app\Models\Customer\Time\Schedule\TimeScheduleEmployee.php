<?php

namespace App\Models\Customer\Time\Schedule;

use App\Models\Customer\Employee;
use App\Models\Customer\Time\Schedule\MainTimeSchedule\MainTimeSchedule;
use App\Models\Customer\Time\Schedule\OtherTimeSchedule\OtherTimeSchedule;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Services\ConnectionService;
class TimeScheduleEmployee extends Model
{
    use HasFactory;

    const ID                    = 'id';
    const TYPE                  = 'type';
    const TIME_SCHEDULE_ID      = 'time_schedule_id';
    const EMPLOYEE_ID           = 'employee_id';
    const BEGIN_DATE            = 'begin_date';
    const END_DATE              = 'end_date';
    const CREATED_BY            = 'created_by';
    const UPDATED_BY            = 'updated_by';

    const RELATION_EMPLOYEE            = 'employee';
    const RELATION_TIME_SCHEDULE       = 'time_schedule';
    const RELATION_MAIN_TIME_SCHEDULE  = 'main_time_schedule';
    const RELATION_OTHER_TIME_SCHEDULE = 'other_time_schedule';

    const HAS_END_DATE          = 'has_end_date';

    const TYPE_MAIN        = 1;
    const TYPE_OTHER       = 2;

    public function __construct($attributes = array()) {
        $this->connection             = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::ID,
        self::TYPE,
        self::TIME_SCHEDULE_ID,
        self::EMPLOYEE_ID,
        self::BEGIN_DATE,
        self::END_DATE,
        self::CREATED_BY,
        self::UPDATED_BY,
    ];

    public function employee(): BelongsTo {
        return $this->belongsTo(Employee::class);
    }

    public function time_schedule(): BelongsTo {
        switch ($this->type) {
            case self::TYPE_MAIN:
                return $this->belongsTo(MainTimeSchedule::class);
            case self::TYPE_OTHER:
                return $this->belongsTo(OtherTimeSchedule::class, 'time_schedule_id');
            default:
                return null;
        }
    }

    public function main_time_schedule(): BelongsTo {
        return $this->belongsTo(MainTimeSchedule::class);
    }

    public function other_time_schedule(): BelongsTo {
        return $this->belongsTo(OtherTimeSchedule::class, 'time_schedule_id', 'id');
    }
}
