<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Models\Constant\ConstData;
use App\Models\Organization;
use App\Models\City;
use App\Models\District;
use App\Models\Khoroo;
use App\Services\AdminService;

use App\Filament\Resources\SuperAdmin\OrganizationResource\Pages;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class OrganizationResource extends Resource
{
    protected static ?string $model = Organization::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Байгууллага';
    protected static ?string $modelLabel = 'байгууллага';
    protected static ?int $navigationSort = 3;
    protected static ?string $slug = 'organizations';

    public static function form(Form $form): Form
    {
        return $form
                ->schema([
                    Forms\Components\Section::make()
                        ->schema([
                            Forms\Components\TextInput::make(Organization::NAME)
                                ->label('Нэр')
                                ->maxLength(50)
                                ->unique(ignoreRecord: true)
                                ->disabled(fn (?Organization $record) => $record != null)
                                ->required(),

                            Forms\Components\TextInput::make(Organization::REGISTRATION_NUMBER)
                                ->label('Регистрийн №')
                                ->maxLength(10)
                                ->unique(ignoreRecord: true)
                                ->disabled(fn (?Organization $record) => $record != null)
                                ->required(),

                            Forms\Components\Select::make(Organization::RELATION_USERS)
                                ->label('Хэрэглэгч')
                                ->relationship(name: Organization::RELATION_USERS, titleAttribute: ConstData::NAME)
                                ->multiple()
                                ->options(
                                    function () {
                                        $service = resolve(AdminService::class);
                                        return $service->getAdminUsers()->pluck(ConstData::NAME, ConstData::ID);
                                    })
                                ->searchable()
                                ->required(),

                            Forms\Components\Select::make(Organization::CITY_ID)
                                ->label('Аймаг/Хот')
                                ->options(City::all()->pluck(ConstData::NAME, ConstData::ID))
                                ->searchable(),

                            Forms\Components\Select::make(Organization::DISTRICT_ID)
                                ->label('Сум/Дүүрэг')
                                ->options(
                                    function (callable $get) {
                                        return District::getDistrictsByCityId($get(Organization::CITY_ID));
                                    })
                                ->searchable(),

                            Forms\Components\Select::make(Organization::KHOROO_ID)
                                ->label('Баг/Хороо')
                                ->options(
                                    function (callable $get) {
                                        return Khoroo::getKhoroosByDistrictId($get(Organization::CITY_ID), $get(Organization::DISTRICT_ID));
                                    })
                                ->searchable(),
                        ])
                        ->columns(1)
                        ->columnSpan(['lg' => fn (?Organization $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Organization $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Organization $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Organization $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('registration_number')->label('Регистрийн №')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrganizations::route('/'),
            'create' => Pages\CreateOrganization::route('/create'),
            'edit' => Pages\EditOrganization::route('/{record}/edit'),
        ];
    }
}
