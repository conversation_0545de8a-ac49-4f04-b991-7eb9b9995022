<?php

namespace App\Models\Customer;

use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\Pivot;

class MerchantEmployeeUser extends Pivot
{
    use HasFactory;

    const MERCHANT_ID      = 'merchant_id';
    const EMPLOYEE_USER_ID = 'employee_user_id';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }
}
