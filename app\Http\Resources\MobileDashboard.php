<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MobileDashboard extends JsonResource
{
    const IPSEN_TSAG                                         = 'ipsen_tsag';
    const TARAKH_TSAG                                        = 'tarakh_tsag';
    const ENE_SARD_AJILVAL_ZOKHIKH_TSAG                      = 'ene_sard_ajilval_zokhikh_tsag';
    const TANI_ENE_SARIIN_UNUUDRIIG_KHURTLEKH_AJILAAGUI_TSAG = 'tani_ene_sariin_unuudriig_khurtlekh_ajilaagui_tsag';
    const NIIT_KHUSELTIIN_TOO                                = 'niit_khuseltiin_too';
    const BATLAGDSAN_KHUSELTIIN_TOO                          = 'batlagdsan_khuseltiin_too';
    const TSUTSLAGSAN_KHUSELTIIN_TOO                         = 'tsutslagsan_khuseltiin_too';
    const KHULEEGDEJ_BUI_KHUSELTIIN_TOO                      = 'khuleegdej_bui_khuseltiin_too';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'ipsen_tsag'                                         => $this->ipsen_tsag,
            'tarakh_tsag'                                        => $this->tarakh_tsag,
            'ene_sard_ajilval_zokhikh_tsag'                      => "$this->ene_sard_ajilval_zokhikh_tsag",
            'tani_ene_sariin_unuudriig_khurtlekh_ajilaagui_tsag' => $this->tani_ene_sariin_unuudriig_khurtlekh_ajilaagui_tsag,
            'niit_khuseltiin_too'                                => $this->niit_khuseltiin_too,
            'batlagdsan_khuseltiin_too'                          => $this->batlagdsan_khuseltiin_too,
            'tsutslagsan_khuseltiin_too'                         => $this->tsutslagsan_khuseltiin_too,
            'khuleegdej_bui_khuseltiin_too'                      => $this->khuleegdej_bui_khuseltiin_too
        ];
    }
}
