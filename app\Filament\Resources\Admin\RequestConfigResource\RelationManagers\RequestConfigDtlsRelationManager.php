<?php

namespace App\Filament\Resources\Admin\RequestConfigResource\RelationManagers;

use App\Models\Constant\ConstData;
use App\Models\Customer\Time\RequestConfig\RequestConfigDtl;
use App\Models\EmployeeRole;
use App\Models\EmployeeUser;
use App\Services\EmployeeUserService;
use App\Services\ConnectionService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Validation\Rules\Unique;
use Illuminate\Database\Eloquent\Builder;

class RequestConfigDtlsRelationManager extends RelationManager
{
    protected static string $relationship = 'request_config_dtls';
    protected static ?string $title = 'Батлах хэрэглэгч';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make(RequestConfigDtl::TYPE)
                    ->label('Төрөл')
                    ->options(RequestConfigDtl::TYPE_OPTIONS)
                    ->required(),

                Forms\Components\TextInput::make(RequestConfigDtl::LIMIT_HOURS)
                    ->label('Цагийн хязгаар')
                    ->numeric()
                    ->default(0)
                    ->inputMode('decimal')
                    ->minValue(1)
                    ->suffix(' цаг')
                    ->required(),

                Forms\Components\Select::make(RequestConfigDtl::EMPLOYEE_USER_ID)
                    ->label('Батлах хэрэглэгч')
                    ->options(function () {
                        $cn = resolve(ConnectionService::class)->connectionName();
                        return resolve(EmployeeUserService::class)->getEmployeeUsersWithFullDisplayName($cn);
                    })
                    ->unique(ignoreRecord: true, modifyRuleUsing: function (Unique $rule, callable $get) {
                        $ownerRecord = $this->getOwnerRecord();
                        return $rule->where(RequestConfigDtl::TYPE, $get(RequestConfigDtl::TYPE))
                                    ->where(RequestConfigDtl::EMPLOYEE_USER_ID, $get(RequestConfigDtl::EMPLOYEE_USER_ID))
                                    ->where(RequestConfigDtl::REQUEST_CONFIG_ID, $ownerRecord->id);
                    })                
                    ->searchable()
                    ->searchingMessage('Хайж байна...')
                    ->loadingMessage('Ачаалж байна байна...')
                    ->noSearchResultsMessage('Хэлтэс олдсонгүй.')
                    ->required(),
            ])->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make(RequestConfigDtl::TYPE)
                    ->label('Төрөл')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'chuluu'         => 'primary',
                        'iluu_tsag'      => 'warning',
                        'gaduur_ajillah' => 'success',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'chuluu'         => 'Чөлөө',
                        'iluu_tsag'      => 'Илүү цаг',
                        'gaduur_ajillah' => 'Гадуур ажиллах',
                    }),
                Tables\Columns\TextColumn::make(RequestConfigDtl::LIMIT_HOURS)
                    ->label('Цагийн хязгаар')
                    ->suffix(' цаг'),
                Tables\Columns\TextColumn::make('employee_department_name')
                    ->label('Хэлтсийн')
                    ->icon('heroicon-m-building-office'),
                Tables\Columns\TextColumn::make('employee_position_name')
                    ->label('Албан тушаалтай')
                    ->icon('heroicon-m-briefcase'),
                Tables\Columns\TextColumn::make('employee_display_name')
                    ->label('Батлах хэрэглэгч')
                    ->icon('heroicon-m-user'),
                    
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-o-plus')
                    ->after(function (RequestConfigDtl $record) {
                        $employeeUser   = resolve(EmployeeUserService::class)->getEmployeeUser($record->employee_user_id);
                        $employeeRole   = EmployeeRole::where(EmployeeRole::NAME, ConstData::EMPLOYEE_ROLE_APPROVER)->first();
                        $employeeUser->employee_roles()->attach($employeeRole->id);
                    }),
            ])
            ->actions([
                Tables\Actions\DeleteAction::make()
                    ->after(function (RequestConfigDtl $record) {
                        $employeeUser   = resolve(EmployeeUserService::class)->getEmployeeUser($record->employee_user_id);
                        $employeeRole   = EmployeeRole::where(EmployeeRole::NAME, ConstData::EMPLOYEE_ROLE_APPROVER)->first();
                        $employeeUser->employee_roles()->detach($employeeRole->id);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
