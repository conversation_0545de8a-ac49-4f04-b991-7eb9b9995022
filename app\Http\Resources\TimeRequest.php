<?php

namespace App\Http\Resources;

use App\Http\Tools\DateTool;
use App\Models\Customer\Time\TimeRequest\TimeRequest as TimeRequestModel;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class SalaryDayFrequency
 *
 * @mixin \App\Models\Customer\Time\SalaryDayFrequency
 */
class TimeRequest extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [

            'id'                            => $this->id,
            'employee_id'                   => $this->employee_id,
            'begin_date'                    => $this->begin_date,
            'end_date'                      => $this->end_date,
            'diff_hours'                    => $this->diff_hours,
            'description'                   => $this->description,
            'att_status'                    => $this->att_status,
            'att_status_name'               => $this->getAttStatusName(),
            'is_confirmed_time_request_dtl' => $this->is_confirmed_time_request_dtl,
            'confirm_status'                => $this->confirm_status,
            'confirm_status_name'           => $this->getConfirmStatusName(),
            // 'time_request_dtls'             => TimeRequestDtl::collection($this->time_request_dtls),
            // 'created_at'                    => DateTool::getDateWithTime($this->created_at),
            'employee'                      => new Employee($this->employee),
        ];
    }
}
