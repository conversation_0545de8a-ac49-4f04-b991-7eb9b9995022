<?php

namespace App\Filament\Resources\Admin\EmployeeResource\Pages;

use App\Models\Constant\ConstData;
use App\Models\Customer\Employee;
use App\Models\Customer\EmployeeDtl;
use App\Models\EmployeeUser;
use App\Models\EmployeeRole;
use App\Models\Customer\Decision\Decision;
use App\Models\Customer\Decision\DecisionHire;
use App\Filament\Resources\Admin\EmployeeResource;
use Filament\Resources\Pages\CreateRecord;

class CreateEmployee extends CreateRecord
{
    protected static string $resource = EmployeeResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data[Employee::STATUS]     = Employee::STATUS_NOT_ACTIVE;
        $data[Employee::CREATED_BY] = auth()->user()->id;
        return $data;
    }

    protected function afterCreate(): void
    {
        $data         = $this->data;
        $employee     = $this->record;
        $employeeUser = $employee->employee_user()->create([
            EmployeeUser::ORGANIZATION_ID => auth()->user()->organizations->first()->id,
            EmployeeUser::PHONE           => $employee->phone,
            EmployeeUser::PASSWORD        => '12345678',
            EmployeeUser::EMPLOYEE_ID     => $employee->id,
        ]);

        $employeeRole = EmployeeRole::where(EmployeeRole::NAME, ConstData::EMPLOYEE_ROLE_EMPLOYEE)->first();
        $employeeUser->employee_roles()->attach($employeeRole->id);

        $decision = Decision::create([
            Decision::EMPLOYEE_ID   => $employee->id,
            Decision::TYPE          => Decision::TYPE_HIRE,
            Decision::DECISION_DATE => $data[Decision::DECISION_DATE],
            Decision::NUMBER        => $data[Decision::NUMBER],
            Decision::CREATED_BY    => $employee->created_by,
        ]);
        $decision->hire()->create([
            DecisionHire::HIRE_DATE  => $data[Employee::RELATION_EMPLOYEE_DTL][EmployeeDtl::COMPANY_WORK_DATE],
            DecisionHire::CREATED_BY => $employee->created_by,
        ]);
        $employee->status = Employee::STATUS_ACTIVE;
        $employee->save();
        $employee->createUserInDevice();
    }
}
