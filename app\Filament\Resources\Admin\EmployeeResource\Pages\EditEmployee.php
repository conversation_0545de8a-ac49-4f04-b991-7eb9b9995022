<?php

namespace App\Filament\Resources\Admin\EmployeeResource\Pages;

use App\Models\EmployeeRole;
use App\Models\Customer\Employee;
use App\Models\Constant\ConstData;
use App\Filament\Resources\Admin\EmployeeResource;
use App\Models\EmployeeUser;
use Filament\Resources\Pages\EditRecord;

class EditEmployee extends EditRecord
{
    protected static string $resource = EmployeeResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data[Employee::UPDATED_BY] = auth()->user()->id;
        return $data;
    }

    protected function afterSave(): void
    {
        $employee = $this->record;
        $employee->createUserInDevice();

        $organizationId = auth()->user()->organizations->first()->id;

        // 1. Ажилтныг байгууллагад бүртгэгдсэн эсэхийг шалгах
        $employeeUser = EmployeeUser::where([
            [EmployeeUser::ORGANIZATION_ID, $organizationId],
            [EmployeeUser::EMPLOYEE_ID, $employee->id]
        ])->first();

        if (!$employeeUser) {
            // 2. Утасны дугаараар хайх (энэ ажилтны дугаартай, гэхдээ EMPLOYEE_ID байхгүй бол)
            $employeeUser = EmployeeUser::where([
                [EmployeeUser::ORGANIZATION_ID, $organizationId],
                [EmployeeUser::PHONE, $employee->phone],
                [EmployeeUser::EMPLOYEE_ID, null]
            ])->first();

            if ($employeeUser) {
                $employeeUser->update([EmployeeUser::EMPLOYEE_ID => $employee->id]);
            } else {
                // 3. Шинээр үүсгэх
                $employeeUser = EmployeeUser::create([
                    EmployeeUser::ORGANIZATION_ID => $organizationId,
                    EmployeeUser::PHONE           => $employee->phone,
                    EmployeeUser::PASSWORD        => '12345678',
                    EmployeeUser::EMPLOYEE_ID     => $employee->id,
                ]);
                // 4. ROLE тохируулах
                if ($employeeRole = EmployeeRole::where(EmployeeRole::NAME, ConstData::EMPLOYEE_ROLE_EMPLOYEE)->first())
                    $employeeUser->employee_roles()->attach($employeeRole->id);
            }
        }
        // 5. EMPLOYEE_ID байхгүй бол тохируулах
        if (is_null($employeeUser->employee_id)) 
            $employeeUser->update([EmployeeUser::EMPLOYEE_ID => $employee->id]);
        // 6. Утасны дугаар өөрчлөгдсөн бол шинэчлэх
        if ($employeeUser->phone != $employee->phone) {
            EmployeeUser::where(EmployeeUser::ORGANIZATION_ID, $organizationId)->where(EmployeeUser::PHONE, $employeeUser->phone)->delete();
            $employeeUser->update([
                EmployeeUser::PHONE    => $employee->phone,
                EmployeeUser::PASSWORD => '12345678',
            ]);
        }
    }

}
