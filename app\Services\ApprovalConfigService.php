<?php

namespace App\Service;

use App\Models\Customer\Approval\ApprovalConfigHdr;
use App\Models\Customer\Approval\ApprovalConfigDtl;
use App\Models\Customer\Approval\ApprovalConfigSubDtl;

use Illuminate\Database\Eloquent\Builder;

class ApprovalConfigService
{
    public function getApprovelConfigsByUserId($connectionName, $userId) {
        $approvelConfigs = new ApprovalConfigHdr();
        $approvelConfigs = $approvelConfigs->on($connectionName)
                        ->with(ApprovalConfigHdr::RELATION_APPROVAL_CONFIG_DTLS)
                        ->whereHas(ApprovalConfigHdr::RELATION_APPROVAL_CONFIG_SUB_DTLS, function (Builder $query) use ($userId) {
                                $query->where(ApprovalConfigSubDtl::USER_ID, $userId);
                        })->get();
        return $approvelConfigs;
    }

    public function getApprovelConfigByEmployeeId($connectionName, $employeeId) {
        $approvelConfig = new ApprovalConfigHdr();
        $approvelConfig = $approvelConfig->on($connectionName)
                        ->with(ApprovalConfigHdr::RELATION_APPROVAL_CONFIG_SUB_DTLS)
                        ->whereHas(ApprovalConfigHdr::RELATION_APPROVAL_CONFIG_DTLS, function (Builder $query) use ($employeeId) {
                            $query->where(ApprovalConfigDtl::EMPLOYEE_ID, $employeeId);
                        })->first();
        return $approvelConfig;
    }
}
