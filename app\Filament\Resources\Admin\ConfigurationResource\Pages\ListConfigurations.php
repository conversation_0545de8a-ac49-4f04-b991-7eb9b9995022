<?php

namespace App\Filament\Resources\Admin\ConfigurationResource\Pages;

use App\Filament\Resources\Admin\ConfigurationResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListConfigurations extends ListRecords
{
    protected static string $resource = ConfigurationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}