<?php

namespace App\Filament\Resources\SuperAdmin\PermissionResource\Pages;

use App\Filament\Resources\SuperAdmin\PermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPermissions extends ListRecords
{
    protected static string $resource = PermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-s-plus'),
        ];
    }
}
