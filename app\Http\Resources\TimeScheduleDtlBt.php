<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class TimeScheduleDtlBt
 *
 * @mixin \App\Models\Customer\Time\TimeScheduleDtlBt
 */
class TimeScheduleDtlBt extends JsonResource
{
    const ID                                = 'id';
    const TIME_SCHEDULE_DTL_ID              = 'time_schedule_dtl_id';
    const BEGIN_TIME_STR                    = 'begin_time_str';
    const END_TIME_STR                      = 'end_time_str';
    const CHECK_IO                          = 'check_io';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                        => $this->id,
            self::TIME_SCHEDULE_DTL_ID      => $this->time_schedule_dtl_id,
            self::BEGIN_TIME_STR            => $this->begin_time_str,
            self::END_TIME_STR              => $this->end_time_str,
            self::CHECK_IO                  => $this->check_io,
        ];
    }
}
