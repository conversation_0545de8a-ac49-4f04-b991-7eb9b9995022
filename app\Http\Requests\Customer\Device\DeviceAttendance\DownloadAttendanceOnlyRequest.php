<?php

namespace App\Http\Requests\Customer\Device\DeviceAttendance;

use Illuminate\Foundation\Http\FormRequest;

class DownloadAttendanceOnlyRequest extends FormRequest
{
    const PARAMETER_YEAR                   = 'year';
    const PARAMETER_MONTH                  = 'month';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::PARAMETER_YEAR                        => 'required|integer|between:2000,3000',
            self::PARAMETER_MONTH                       => 'required|integer|between:1,12',
        ];
    }
}
