<?php

namespace App\Models\Customer;

use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Department extends Model
{
    use HasFactory;

    const TABLE           = 'departments';

    const ID                    = 'id';
    const NAME                  = 'name';
    const SHORT_NAME            = 'short_name';
    const PARENT_ID             = 'parent_id';
    const CREATED_BY            = 'created_by';
    const UPDATED_BY            = 'updated_by';
    const RN                    = 'rn';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::ID,
        self::NAME,
        self::SHORT_NAME,
        self::PARENT_ID,
        self::CREATED_BY,
        self::UPDATED_BY,
        self::RN
    ];

    public function positions(): Has<PERSON>any {
        return $this->hasMany(Position::class);
    }

    public function parent_department(): BelongsTo {
        return $this->belongsTo(Department::class, Department::PARENT_ID);
    }

    public function full_department_name(): string
    {
        if ($this->parent_department) {
            return $this->parent_department->full_department_name() . ' > ' . $this->name;
        }
        return $this->name;
    }
}
