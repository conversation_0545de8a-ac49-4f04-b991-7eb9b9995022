<?php

namespace App\Services;

use App\Models\Customer\Employee;
use App\Models\EmployeeUser;
class EmployeeUserService
{
    public function getEmployeeUser($employeeUserId) {
        return EmployeeUser::find($employeeUserId);
    }

    public function getEmployeeUsers() {
        $organizationId = auth()->user()->organizations->first()->id;
        return EmployeeUser::where(EmployeeUser::ORGANIZATION_ID, $organizationId)->get();
    }

    public function getEmployeeUsersWithFullDisplayName($cn) {
        $result        = [];
        $employeeUsers = $this->getEmployeeUsers();
        foreach ($employeeUsers as $employeeUser) {
            if (isset($employeeUser->employee_id)) 
                $employee = Employee::on($cn)->find($employeeUser->employee_id);
            else
                $employee = Employee::on($cn)->where(Employee::PHONE, $employeeUser->phone)->first();    
            if (!isset($employee))
                continue;
            $result[$employeeUser->id] = $employee->department->short_name . ', ' . $employee->position->short_name  . ', '. $employee->last_name . ' ' . $employee->first_name;
        }
        return $result;

    }  
}
