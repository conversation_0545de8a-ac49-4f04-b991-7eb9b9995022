<?php

namespace App\Filament\Resources\Admin\SuperSalaryPeriodResource\RelationManagers;

use App\Models\Constant\ConstData;
use App\Models\Customer\Department;
use App\Models\Customer\Time\SuperSalaryPeriod\SuperSalaryPeriodDtl;
use App\Http\Tools\DateTool;
use App\Models\Customer\Time\SuperSalaryPeriod\SuperSalaryPeriodSubTwoDtl;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class SuperSalaryPeriodDtlsRelationManager extends RelationManager
{
    protected static string $relationship = 'super_other_salary_period_dtls';
    protected static ?string $title = 'Бусад үечлэл';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make(SuperSalaryPeriodDtl::WD_COUNT)
                    ->label('Нийт ажиллах өдрийн тоо')
                    ->default(21)
                    ->numeric()
                    ->live()
                    ->afterStateUpdated(fn (callable $get, callable $set, ?string $state) => $set(SuperSalaryPeriodDtl::WD_TOTAL_TIME, $state * DateTool::convertMin($get(SuperSalaryPeriodDtl::WD_TIME))/60))
                    ->inputMode('decimal'),
                
                Forms\Components\TimePicker::make(SuperSalaryPeriodDtl::WD_TIME)
                    ->label('Нэг өдрийн ажиллах цаг')
                    ->default('08:00')
                    ->seconds(false)
                    ->afterStateUpdated(fn (callable $get, callable $set, ?string $state) => $set(SuperSalaryPeriodDtl::WD_TOTAL_TIME, DateTool::convertMin($state)/60 * $get(SuperSalaryPeriodDtl::WD_COUNT)))
                    ->live(),

                Forms\Components\TextInput::make(SuperSalaryPeriodDtl::WD_TOTAL_TIME)
                    ->label('Нийт ажиллах цаг')
                    ->default(168)
                    ->disabled(),

                Forms\Components\Select::make(SuperSalaryPeriodDtl::RELATION_SUPER_SALARY_PERIOD_SUB_ONE_DTLS)
                    ->label('Хэлтэс')
                    ->relationship()
                    ->options(Department::all()->pluck(ConstData::NAME, ConstData::ID))
                    ->searchable()
                    ->multiple()
                    ->required(),
                Forms\Components\Repeater::make(SuperSalaryPeriodDtl::RELATION_SUPER_SALARY_PERIOD_SUB_TWO_DTLS)
                    ->relationship()
                    ->schema([
                        Forms\Components\DatePicker::make(SuperSalaryPeriodSubTwoDtl::BEGIN_DATE)
                            ->label('Эхлэх огноо')
                            ->default(DateTool::createDateWithFormat(now()->year, now()->month, 1))
                            ->displayFormat('d/m/Y'),

                        Forms\Components\DatePicker::make(SuperSalaryPeriodSubTwoDtl::END_DATE)
                            ->label('Дуусах огноо')
                            ->default(DateTool::getLastDayOfMonth(now()))
                            ->displayFormat('d/m/Y'),
                    ])
                    ->maxItems(2)
                    ->grid(2)
                    ->label('Үечлэлийн задаргаа')
                    ->addActionLabel('Үечлэл нэмэх'),
            ])->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('title')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('department_names')->label('Хэлтэс'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Нэмэх')
                    ->icon('heroicon-m-plus')
                    ->mutateFormDataUsing(function (array $data): array {
                        $data[SuperSalaryPeriodDtl::TYPE]   = 1;
                        $data[SuperSalaryPeriodDtl::WD_MIN] = DateTool::convertMin($data[SuperSalaryPeriodDtl::WD_TIME]);
                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->icon('heroicon-m-pencil')
                    ->mutateRecordDataUsing(function (Model $record,array $data): array {
                        $data[SuperSalaryPeriodDtl::WD_TIME]       = DateTool::convertTime($record->wd_min);
                        $data[SuperSalaryPeriodDtl::WD_TOTAL_TIME] = $record->wd_count * $record->wd_min / 60;
                        return $data;
                    }),
                Tables\Actions\DeleteAction::make()
                    ->after(function (SuperSalaryPeriodDtl $record) {
                        $record->super_salary_period_sub_one_dtls()->detach();
                        $record->super_salary_period_sub_two_dtls()->delete();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
