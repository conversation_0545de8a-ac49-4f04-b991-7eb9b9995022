<?php

namespace App\Http\Requests\Customer\CashRequest;

use App\Enums\CashRequestStatusEnum;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Foundation\Http\FormRequest;

class GetCashRequest extends FormRequest
{
    const LIMIT                  = 'limit';
    const FILTER                 = 'filter';
    const FILTER_STATUS          = 'status';
    const SORT                   = 'sort';
    const SORT_FIELD             = 'field';
    const SORT_TYPE              = 'type';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'limit'         => 'nullable|integer',
            'filter'        => 'nullable|array',
            'filter.status' => ['nullable', new Enum(CashRequestStatusEnum::class)],
            'sort'          => 'nullable|array',
            'sort.field'    => 'nullable|string|in:id,amount,created_at',
            'sort.type'     => 'nullable|in:asc,desc',
        ];
    }
}
