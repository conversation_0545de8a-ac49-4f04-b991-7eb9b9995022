<?php

namespace App\Filament\Resources\Admin\SuperSalaryPeriodResource\Pages;

use App\Models\Customer\Time\SuperSalaryPeriod\SuperSalaryPeriod;
use App\Filament\Resources\Admin\SuperSalaryPeriodResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateSuperSalaryPeriod extends CreateRecord
{
    protected static string $resource = SuperSalaryPeriodResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data[SuperSalaryPeriod::CREATED_BY] = auth()->user()->id;
        return $data;
    }
}
