<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOtherTimeScheduleDtlsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('other_time_schedule_dtls'))
            return;
        Schema::create('other_time_schedule_dtls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('other_time_schedule_id')->constrained('other_time_schedules')->onDelete('cascade');
            $table->unsignedBigInteger('type')->default(1);
            $table->unsignedBigInteger('day_of_week');
            $table->unsignedBigInteger('bfr_min')->default(0);
            $table->unsignedBigInteger('afr_min')->default(0);
            $table->string('begin_time_str')->nullable();
            $table->string('end_time_str')->nullable();
            $table->string('rb_time_str')->nullable();
            $table->string('re_time_str')->nullable();
            $table->unsignedBigInteger('work_min')->nullable();
            $table->boolean('has_break_time')->default(0);
            $table->unsignedBigInteger('admit_late_min')->default(0);
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('other_time_schedule_dtls');
    }
}
