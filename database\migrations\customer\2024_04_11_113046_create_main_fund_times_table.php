<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('main_fund_times'))
            return;
        Schema::create('main_fund_times', function (Blueprint $table) {
            $table->id();
            $table->foreignId('salary_period_id')->constrained('salary_periods');
            $table->enum('type', ['constant', 'inconstant']);
            $table->integer('wd_count')->nullable();
            $table->integer('wd_min')->nullable();
            $table->integer('monday_min')->nullable();
            $table->integer('tuesday_min')->nullable();
            $table->integer('wednesday_min')->nullable();
            $table->integer('thursday_min')->nullable();
            $table->integer('friday_min')->nullable();
            $table->integer('saturday_min')->nullable();
            $table->integer('sunday_min')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('main_fund_times');
    }
};
