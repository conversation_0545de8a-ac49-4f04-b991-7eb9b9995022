<?php

namespace App\Models\Customer;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MobileDashboard extends Model
{
    use HasFactory;

    const IPSEN_TSAG                                         = 'ipsen_tsag';
    const TARAKH_TSAG                                        = 'tarakh_tsag';
    const ENE_SARD_AJILVAL_ZOKHIKH_TSAG                      = 'ene_sard_ajilval_zokhikh_tsag';
    const TANI_ENE_SARIIN_UNUUDRIIG_KHURTLEKH_AJILAAGUI_TSAG = 'tani_ene_sariin_unuudriig_khurtlekh_ajilaagui_tsag';
    const NIIT_KHUSELTIIN_TOO                                = 'niit_khuseltiin_too';
    const BATLAGDSAN_KHUSELTIIN_TOO                          = 'batlagdsan_khuseltiin_too';
    const TSUTSLAGSAN_KHUSELTIIN_TOO                         = 'tsutslagsan_khuseltiin_too';
    const KHU<PERSON><PERSON><PERSON>J_BUI_KHUSELTIIN_TOO                      = 'khuleegdej_bui_khuseltiin_too';

    protected $fillable = [
        self::IPSEN_TSAG,
        self::TARAKH_TSAG,
        self::ENE_SARD_AJILVAL_ZOKHIKH_TSAG,
        self::TANI_ENE_SARIIN_UNUUDRIIG_KHURTLEKH_AJILAAGUI_TSAG,
        self::NIIT_KHUSELTIIN_TOO,
        self::BATLAGDSAN_KHUSELTIIN_TOO,
        self::TSUTSLAGSAN_KHUSELTIIN_TOO,
        self::KHULEEGDEJ_BUI_KHUSELTIIN_TOO,
    ];
}
