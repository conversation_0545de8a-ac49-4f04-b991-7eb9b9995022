<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class DecisionMaternity
 *
 * @mixin \App\Models\Customer\Time\Decision\DecisionMaternity
 */
class DecisionMaternity extends JsonResource
{
    const ID                         = 'id';
    const DECISION_ID                = 'decision_id';
    const TYPE                       = 'type';
    const BEGIN_DATE                 = 'begin_date';
    const END_DATE                   = 'end_date';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                            => $this->id,
            self::DECISION_ID                   => $this->decision_id,
            self::TYPE                          => $this->type,
            self::BEGIN_DATE                    => $this->begin_date,
            self::END_DATE                      => $this->end_date,
        ];
    }
}
