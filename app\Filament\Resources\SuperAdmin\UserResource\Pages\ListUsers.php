<?php

namespace App\Filament\Resources\SuperAdmin\UserResource\Pages;

use App\Models\Constant\ConstData;
use App\Filament\Resources\SuperAdmin\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-o-plus'),
        ];
    }

    public function getTabs(): array
    {
        return [
            // 'Бүгд' => Tab::make()
            //     ->modifyQueryUsing(fn (Builder $query) => $query->where('name', '!=', ConstData::ROLE_SUPER_ADMIN)),
            // 'Role-гүй' => Tab::make()
            //     ->modifyQueryUsing(fn (Builder $query) => $query->where('name', '!=', ConstData::ROLE_SUPER_ADMIN)),
            // 'Role-той' => Tab::make()
            //     ->modifyQueryUsing(fn (Builder $query) => $query->withoutRole(ConstData::ROLE_SUPER_ADMIN)),
        ];
    }
}
