<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Models\Constant\ConstData;
use App\Models\Customer\Position;
use App\Models\Customer\Department;
use App\Models\Customer\Hierarchy;

use App\Filament\Resources\Admin\PositionResource\Pages;
use App\Filament\Resources\Admin\PositionResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Validation\Rules\Unique;

class PositionResource extends Can
{
    protected static ?string $model = Position::class;

    protected static ?string $navigationIcon = 'heroicon-o-newspaper';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Албан тушаал';
    protected static ?string $modelLabel = 'албан тушаал';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationGroup = 'Бүртгэл';
    protected static ?string $slug = 'positions';

    public static function form(Form $form): Form
    {
        return $form
                ->schema([
                    Forms\Components\Section::make()
                        ->schema([
                            Forms\Components\TextInput::make(Position::NAME)
                                ->label('Нэр')
                                ->maxLength(50)
                                ->unique(ignoreRecord: true, modifyRuleUsing: function (Unique $rule, callable $get) {
                                    return $rule->where('department_id', $get('department_id'));
                                })
                                ->required(),

                            Forms\Components\TextInput::make(Position::SHORT_NAME)
                                ->label('Богино нэр')
                                ->maxLength(3)
                                ->required(),

                            Forms\Components\Select::make(Position::DEPARTMENT_ID)
                                ->label('Хэлтэс')
                                ->options(Department::all()->pluck(ConstData::NAME, ConstData::ID))
                                ->searchable()
                                ->required(),

                            Forms\Components\Select::make(Position::RELATION_HIERARCHY)
                                ->label('Шатлал')
                                ->relationship(name: Position::RELATION_HIERARCHY, titleAttribute: Hierarchy::NAME)
                                ->options(Hierarchy::all()->pluck(ConstData::NAME, ConstData::ID))
                                ->searchable()
                                ->required(),

                        ])
                        ->columns(1)
                        ->columnSpan(['lg' => fn (?Position $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Position $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Position $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Position $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('short_name')->label('Богино нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('department.name')->label('Хэлтэс')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPositions::route('/'),
            'create' => Pages\CreatePosition::route('/create'),
            'edit' => Pages\EditPosition::route('/{record}/edit'),
        ];
    }
}
