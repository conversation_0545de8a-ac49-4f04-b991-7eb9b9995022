<?php

namespace App\Filament\Resources\Admin;

use App\Models\Customer\BenefitGroup;
use App\Filament\Resources\Admin\BenefitGroupResource\Pages;
use App\Filament\Resources\Admin\BenefitGroupResource\RelationManagers;
use App\Models\Class\Can;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;

class BenefitGroupResource extends Can
{
    protected static ?string $model = BenefitGroup::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Бүлэг';
    protected static ?string $modelLabel = 'Бүлэг';
    protected static ?int $navigationSort = 10;
    protected static ?string $navigationGroup = 'Урамшуулал';
    protected static ?string $slug = 'benefit-groups';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make(BenefitGroup::NAME)
                            ->label('Нэр')
                            ->maxLength(255)
                            ->required(),
                        Forms\Components\Textarea::make(BenefitGroup::DESCRIPTION)
                            ->label('Тайлбар')
                            ->maxLength(65535),
                        Forms\Components\TextInput::make(BenefitGroup::EMOJI)
                            ->label('Emoji')
                            ->maxLength(255),
                        Forms\Components\FileUpload::make(BenefitGroup::IMAGE_URL)
                            ->label("Зураг оруулна уу!")
                            ->image()
                            ->imageResizeMode('cover')
                            ->acceptedFileTypes(['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml', 'image/webp'])
                            ->preserveFilenames()
                            ->directory(strtolower(config('app.name')) . '/benefit-groups/' . ($record?->id ?? Str::uuid()))
                            ->disk('minio')
                            ->visibility('private')
                            ->maxSize(100000),
                    ])
                    ->columns(1)
                    ->columnSpan(['lg' => fn (?BenefitGroup $record) => $record === null ? 3 : 2]),

            Forms\Components\Section::make()
                ->schema([
                    Forms\Components\Placeholder::make('created_at')
                        ->label('Created at')
                        ->content(fn (BenefitGroup $record): ?string => $record->created_at?->diffForHumans()),

                    Forms\Components\Placeholder::make('updated_at')
                        ->label('Last modified at')
                        ->content(fn (BenefitGroup $record): ?string => $record->updated_at?->diffForHumans()),
                ])
                ->columnSpan(['lg' => 1])
                ->hidden(fn (?BenefitGroup $record) => $record === null),
        ])
        ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(BenefitGroup::NAME)->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(BenefitGroup::DESCRIPTION)->label('Тайлбар')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(BenefitGroup::EMOJI)->label('Emoji')->sortable()->searchable(),
                Tables\Columns\ImageColumn::make('image_preview_url')->label('Зураг')->size(50),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBenefitGroups::route('/'),
            'create' => Pages\CreateBenefitGroup::route('/create'),
            'edit' => Pages\EditBenefitGroup::route('/{record}/edit'),
        ];
    }
}
