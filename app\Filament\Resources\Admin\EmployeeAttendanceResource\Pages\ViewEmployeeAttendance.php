<?php

namespace App\Filament\Resources\Admin\EmployeeAttendanceResource\Pages;

use App\Filament\Resources\Admin\EmployeeAttendanceResource;
use Filament\Resources\Pages\ViewRecord;
use Livewire\Attributes\On;

class ViewEmployeeAttendance extends ViewRecord
{
    protected static string $resource = EmployeeAttendanceResource::class;

    #[On('employee-status-changed')]
    public function refresh() {
        $this->data['getWorkTimeByHBAttribute']   = $this->record->getWorkTimeByHBAttribute();
        $this->data['getLateTimeByHBAttribute']   = $this->record->getLateTimeByHBAttribute();
        $this->data['getAbsentTimeByHBAttribute'] = $this->record->getAbsentTimeByHBAttribute();
        $this->data['getFurloughTimeByHBAttribute'] = $this->record->getFurloughTimeByHBAttribute();
        $this->data['getSickTimeByHBAttribute'] = $this->record->getSickTimeByHBAttribute();
        $this->refreshFormData(['getWorkTimeByHBAttribute', 'getLateTimeByHBAttribute', 'getAbsentTimeByHBAttribute', 'getFurloughTimeByHBAttribute', 'getSickTimeByHBAttribute']);
    }
}
