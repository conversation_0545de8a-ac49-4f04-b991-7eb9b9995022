<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TimeRequestValidDatesEndpointTest extends TestCase
{
    /** @test */
    public function it_returns_valid_dates_endpoint_structure()
    {
        // Create a mock user for authentication
        $user = \Mockery::mock('App\Models\User');
        $user->id = 1;
        $user->phone = '99887766';
        $this->be($user);

        // Test the endpoint with a valid attendance status
        $response = $this->getJson('/api/time-requests/valid-dates/' . EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'type',
                'min_date',
                'max_date',
                'message'
            ]
        ]);

        $this->assertTrue($response->json('success'));
    }

    /** @test */
    public function it_handles_invalid_attendance_status()
    {
        // Create a mock user for authentication
        $user = \Mockery::mock('App\Models\User');
        $user->id = 1;
        $user->phone = '99887766';
        $this->be($user);

        // Test with an invalid attendance status
        $response = $this->getJson('/api/time-requests/valid-dates/999');

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'data' => [
                'type' => 'none',
                'min_date' => null,
                'max_date' => null,
                'message' => ''
            ]
        ]);
    }

    /** @test */
    public function it_requires_authentication()
    {
        // Test without authentication
        $response = $this->getJson('/api/time-requests/valid-dates/' . EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK);

        $response->assertStatus(401);
    }

    /** @test */
    public function it_returns_expected_response_format_for_different_statuses()
    {
        // Create a mock user for authentication
        $user = \Mockery::mock('App\Models\User');
        $user->id = 1;
        $user->phone = '99887766';
        $this->be($user);

        // Test different attendance statuses
        $statuses = [
            EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK,
            EmployeeAttendanceDtl::ATTENDANCE_STATUS_VACATION,
            EmployeeAttendanceDtl::ATTENDANCE_STATUS_OUT_WORK,
            EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH_LONG,
            EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY
        ];

        foreach ($statuses as $status) {
            $response = $this->getJson('/api/time-requests/valid-dates/' . $status);

            $response->assertStatus(200);
            $response->assertJsonStructure([
                'success',
                'data' => [
                    'type',
                    'min_date',
                    'max_date',
                    'message'
                ]
            ]);

            $this->assertTrue($response->json('success'));
            
            // The type should be either 'none' or 'min_date'
            $type = $response->json('data.type');
            $this->assertContains($type, ['none', 'min_date']);
        }
    }
}
