<?php

namespace App\Models\Customer\Decision;

use App\Models\Customer\Employee;
use App\Models\Customer\Decision\DecisionHire;
use App\Models\Customer\Decision\DecisionFire;
use App\Models\Customer\Decision\DecisionLongTermFurlough;
use App\Models\Customer\Decision\DecisionMaternity;
use App\Models\Customer\Decision\DecisionVacation;
use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Decision extends Model
{
    use HasFactory;

    const TABLE = 'decisions';

    const ID                   = 'id';
    const NUMBER               = 'number';
    const TYPE                 = 'type';
    const DECISION_DATE        = 'decision_date';
    const EMPLOYEE_ID          = 'employee_id';
    const CREATED_BY           = 'created_by';
    const UPDATED_BY           = 'updated_by';

    const TYPE_HIRE                 = 1;
    const TYPE_FIRE                 = 2;
    const TYPE_VACATION             = 3;
    const TYPE_LONG_TERM_FURLOUGH   = 4;
    const TYPE_MATERNITY            = 5;

    const RELATION_HIRE                    = 'hire';
    const RELATION_FIRE                    = 'fire';
    const RELATION_LONG_TERM_FURLOUGH      = 'long_term_furlough';
    const RELATION_MATERNITIES             = 'maternities';
    const RELATION_VACATIONS               = 'vacations';
    const RELATION_EMPLOYEE                = 'employee';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::ID,
        self::NUMBER,
        self::TYPE,
        self::EMPLOYEE_ID,
        self::DECISION_DATE,
        self::CREATED_BY,
    ];

    public function getTypeNameAttribute()
    {
        $decisionName = '';
        switch ($this->type) {
            case self::TYPE_HIRE:
                $decisionName = 'Ажилд авах';
                break;

            default:
                # code...
                break;
        }
        return $decisionName;
    }

    public function hire(): HasOne  {
        return $this->hasOne(DecisionHire::class);
    }

    public function fire() {
        return $this->hasOne(DecisionFire::class);
    }

    public function long_term_furlough() {
        return $this->hasOne(DecisionLongTermFurlough::class);
    }

    public function maternities() {
        return $this->hasMany(DecisionMaternity::class);
    }

    public function vacations() {
        return $this->hasMany(DecisionVacation::class);
    }

    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }
}
