<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Models\Constant\ConstData;
use App\Models\User;
use App\Models\Customer\Time\Schedule\MainTimeSchedule\MainTimeSchedule;

use App\Filament\Resources\Admin\MainTimeScheduleResource\Pages;
use App\Filament\Resources\Admin\MainTimeScheduleResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class MainTimeScheduleResource extends Can
{
    protected static ?string $model = MainTimeSchedule::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Үндсэн цагийн хуваарь';
    protected static ?string $modelLabel = 'үндсэн цагийн хуваарь';
    protected static ?int $navigationSort = 9;
    protected static ?string $navigationGroup = 'Цаг';
    protected static ?string $slug = 'main_time_schedules';

    public static function form(Form $form): Form
    {
        return $form
                ->schema([
                    Forms\Components\Section::make()
                        ->schema([
                            Forms\Components\TextInput::make(MainTimeSchedule::NAME)
                                ->label('Нэр')
                                ->default(MainTimeSchedule::DEFAULT_NAME)
                                ->disabled(),

                            Forms\Components\TextInput::make(MainTimeSchedule::SHORT_NAME)
                                ->label('Богино нэр')
                                ->default(MainTimeSchedule::DEFAULT_SHORT_NAME)
                                ->disabled(),

                            Forms\Components\DatePicker::make(MainTimeSchedule::BEGIN_DATE)
                                ->label('Эхлэх огноо')
                                ->native(false)
                                ->default(now())
                                ->displayFormat('Y/m/d'),
                            
                            Forms\Components\Hidden::make(MainTimeSchedule::CREATED_BY)
                                    ->default(auth()->user()->id)
                                    ->disabled(fn (?MainTimeSchedule $record) => $record != null),
                        ])
                        ->columns(1)
                        ->columnSpan(['lg' => fn (?MainTimeSchedule $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Үүссэн огноо')
                            ->content(fn (MainTimeSchedule $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('created_by')
                            ->label('Үүсгэсэн хэрэглэгч')
                            ->content(fn (MainTimeSchedule $record): ?string => User::find($record->created_by)?->id),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Өөрчилсөн огноо')
                            ->content(fn (MainTimeSchedule $record): ?string => $record->updated_at ? $record->updated_at?->diffForHumans(): ConstData::BAIHGUI),

                        Forms\Components\Placeholder::make('updated_by')
                            ->label('Өөрчилсөн хэрэглэгч')
                            ->content(fn (MainTimeSchedule $record): ?string => $record->updated_by ? User::find($record->updated_by)->name : ConstData::BAIHGUI),
                    ])->columns(2)
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?MainTimeSchedule $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('short_name')->label('Богино нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('begin_date')->label('Эхлэх огноо')->sortable()->searchable(),
            ])
            ->defaultSort('begin_date', 'desc')
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\MainTimeScheduleDtlsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMainTimeSchedules::route('/'),
            'create' => Pages\CreateMainTimeSchedule::route('/create'),
            'edit' => Pages\EditMainTimeSchedule::route('/{record}/edit'),
        ];
    }
}
