<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Models\Customer\Time\Schedule\OtherTimeSchedule\OtherTimeSchedule;
use App\Models\Customer\Time\Schedule\TimeScheduleEmployee;
use App\Models\Constant\ConstData;
use App\Models\Customer\Employee;
use App\Models\User;
use App\Http\Tools\DateTool;
use App\Filament\Resources\Admin\OtherTimeScheduleResource\Pages;
use App\Filament\Resources\Admin\OtherTimeScheduleResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\Repeater;
use Filament\Tables;
use Filament\Tables\Table;

class OtherTimeScheduleResource extends Can
{
    protected static ?string $model = OtherTimeSchedule::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Бусад цагийн хуваарь';
    protected static ?string $modelLabel = 'бусад цагийн хуваарь';
    protected static ?int $navigationSort = 10;
    protected static ?string $navigationGroup = 'Цаг';
    protected static ?string $slug = 'other_time_schedules';

    public static function form(Form $form): Form
    {
        return $form
                ->schema([
                    Forms\Components\Section::make()
                        ->schema([
                            Forms\Components\TextInput::make(OtherTimeSchedule::NAME)
                                ->label('Нэр')
                                ->required(),

                            Forms\Components\TextInput::make(OtherTimeSchedule::SHORT_NAME)
                                ->label('Богино нэр')
                                ->required()
                                ->unique(ignoreRecord: true),

                            Forms\Components\DatePicker::make(OtherTimeSchedule::BEGIN_DATE)
                                ->label('Эхлэх огноо')
                                ->native(false)
                                ->default(now())
                                ->displayFormat('Y/m/d')
                                ->disabled(fn (?OtherTimeSchedule $record) => $record != null)
                                ->seconds(false)
                                ->live(),
                            
                            Forms\Components\Hidden::make(OtherTimeSchedule::CREATED_BY)
                                    ->default(auth()->user()->id)
                                    ->disabled(fn (?OtherTimeSchedule $record) => $record != null),
                        ])
                        ->columns(1)
                        ->columnSpan(['lg' => fn (?OtherTimeSchedule $record) => $record === null ? 3 : 2]),

                    
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Үүссэн огноо')
                            ->content(fn (OtherTimeSchedule $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('created_by')
                            ->label('Үүсгэсэн хэрэглэгч')
                            ->content(fn (OtherTimeSchedule $record): ?string => User::find($record->created_by)->name),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Өөрчилсөн огноо')
                            ->content(fn (OtherTimeSchedule $record): ?string => $record->updated_at ? $record->updated_at?->diffForHumans(): ConstData::BAIHGUI),

                        Forms\Components\Placeholder::make('updated_by')
                            ->label('Өөрчилсөн хэрэглэгч')
                            ->content(fn (OtherTimeSchedule $record): ?string => $record->updated_by ? User::find($record->updated_by)->name : ConstData::BAIHGUI),
                    ])->columns(2)
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?OtherTimeSchedule $record) => $record === null),
                    
                Forms\Components\Section::make()
                    ->schema([
                        Repeater::make(OtherTimeSchedule::RELATION_TIME_SCHEDULE_EMPLOYEES)
                            ->schema([
                                Forms\Components\Select::make(TimeScheduleEmployee::EMPLOYEE_ID)
                                    ->label('Ажилтан')
                                    ->options(Employee::all()->pluck(Employee::FULL_NAME, Employee::ID))
                                    ->searchable()
                                    ->distinct()
                                    ->required(),
                                Forms\Components\Toggle::make(TimeScheduleEmployee::HAS_END_DATE)
                                    ->label('Дуусах огноотой эсэх')
                                    ->default(false)
                                    ->inline(false)
                                    ->live()
                                    ->onIcon('heroicon-m-bolt')
                                    ->offIcon('heroicon-m-bolt-slash'),
                                Forms\Components\DatePicker::make(TimeScheduleEmployee::BEGIN_DATE)
                                    ->label('Эхлэх огноо')
                                    ->default(now())
                                    ->seconds(false)
                                    ->minDate(function (callable $get) {
                                        return DateTool::setZeroTime($get('../../'.OtherTimeSchedule::BEGIN_DATE));
                                    })
                                    ->live(),
                                Forms\Components\DatePicker::make(TimeScheduleEmployee::END_DATE)
                                    ->label('Дуусах огноо')
                                    ->seconds(false)
                                    ->minDate(function (callable $get) {
                                        return DateTool::setZeroTime($get(TimeScheduleEmployee::BEGIN_DATE));
                                    })
                                    ->hidden(function (callable $get) {
                                        return ! $get(TimeScheduleEmployee::HAS_END_DATE);
                                    })
                                    ->live(),
                                Forms\Components\Hidden::make(TimeScheduleEmployee::TYPE)
                                    ->default(TimeScheduleEmployee::TYPE_OTHER),
                                Forms\Components\Hidden::make(TimeScheduleEmployee::CREATED_BY)
                                    ->default(auth()->user()->id),
                            ])
                            ->columns(2)
                            ->grid(3)
                            ->label('Хамрагдсан ажилчид')
                            ->addActionLabel('Ажилтан хамруулах'),
                    ])
                    ->columns(1),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('short_name')->label('Богино нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('begin_date')->label('Эхлэх огноо')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('employee_count')->label('Тоо')->tooltip('Хамаарагдаж байгаа ажилчидын тоо')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\OtherTimeScheduleDtlsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOtherTimeSchedules::route('/'),
            'create' => Pages\CreateOtherTimeSchedule::route('/create'),
            'edit' => Pages\EditOtherTimeSchedule::route('/{record}/edit'),
        ];
    }
}
