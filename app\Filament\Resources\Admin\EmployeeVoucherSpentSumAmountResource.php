<?php

namespace App\Filament\Resources\Admin;

use App\Filament\Resources\Admin\EmployeeVoucherSpentSumAmountResource\Pages;
use App\Filament\Resources\Admin\EmployeeVoucherSpentSumAmountResource\RelationManagers;
use App\Http\Tools\DateTool;
use App\Filament\Exports\EmployeeVoucherSpentExporter;
use App\Models\Class\Can;
use App\Models\Customer\Employee;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Actions\ExportAction;
use Illuminate\Database\Eloquent\Builder;

class EmployeeVoucherSpentSumAmountResource extends Can
{
    protected static ?string $model = Employee::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Зарцуулалт Ажилчин';
    protected static ?string $modelLabel = 'Зарцуулалт Ажилчин';
    protected static ?int $navigationSort = 113;
    protected static ?string $navigationGroup = 'Ваучер';
    protected static ?string $slug = 'employee-voucher-spent-sum-amount';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('last_name')->label('Овог')->disabled(true),
                Forms\Components\TextInput::make('first_name')->label('Нэр')->disabled(true),
                Forms\Components\TextInput::make('department_id')->label('Хэлтэс')
                    ->formatStateUsing(fn ($record) => $record->department->name)
                    ->disabled(true),
                Forms\Components\TextInput::make('position_id')->label('Албан тушаал')
                    ->formatStateUsing(fn ($record) => $record->position->name)
                    ->disabled(true),
                Forms\Components\TextInput::make('sum_amount')->label('Үнэ')->disabled(true)
                    ->formatStateUsing(fn ($record) => $record->employee_voucher_spents->sum('amount'))
                    ->disabled(true),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->heading('Posts')
            ->defaultSort('created_at', 'desc')
            ->headerActions([
                ExportAction::make()
                    ->exporter(EmployeeVoucherSpentExporter::class)
            ])
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('full_name')->label('Овог нэр')->sortable()->searchable(['first_name', 'last_name']),
                Tables\Columns\TextColumn::make('department.name')->label('Хэлтэс')->sortable()->searchable()->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('position.name')->label('Албан тушаал')->sortable()->searchable()->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('employee_voucher_spents')->label('Дүн')->sortable()->formatStateUsing(fn ($record) => $record->employee_voucher_spents->sum('amount')),
            ])
            ->filters([
                Filter::make('created_at')
                    ->form([
                        DatePicker::make('begin_date')->default(DateTool::setOnlyDay(now(), 1)),
                        DatePicker::make('end_date')->default(DateTool::getLastDayOfMonth(now())),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['begin_date'],
                                fn (Builder $query, $date): Builder => $query->whereHas('employee_voucher_spents', function (Builder $query) use ($date) {
                                    $query->whereDate('created_at', '>=', $date);
                                }),
                            )
                            ->when(
                                $data['end_date'],
                                fn (Builder $query, $date): Builder => $query->whereHas('employee_voucher_spents', function (Builder $query) use ($date) {
                                    $query->whereDate('created_at', '<=', $date);
                                }),
                            );
                    })
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\EmployeeVoucherSpentsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEmployeeVoucherSpentSumAmounts::route('/'),
            'view' => Pages\ViewEmployeeVoucherSpentSumAmount::route('/{record}'),
        ];
    }
}
