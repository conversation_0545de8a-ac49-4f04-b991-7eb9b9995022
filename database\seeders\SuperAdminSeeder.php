<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Role;
use App\Models\Constant\ConstData;
use Illuminate\Database\Seeder;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $sadminEmail = '<EMAIL>';

        if (User::where('email', $sadminEmail)->exists()) {
            return;
        }

        $superUser = User::create([
            'name'     => 'superadmin',
            'email'    => $sadminEmail,
            'password' => '$2y$10$5OMbpq3wC4D8DDdFEtzmj.fBrpqE/vADDQxpCNbjJ9B5XLpnq/6x2', // hashed password
        ]);

        $sadminRole = Role::where('name', ConstData::ROLE_SUPER_ADMIN)->first();
        if ($sadminRole) {
            $superUser->roles()->attach($sadminRole->id);
        }
    }
}
