<?php

namespace App\Models\Customer;

use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Hierarchy extends Model
{
    use HasFactory;

    const NAME = 'name';

    const RELATION_BENEFITS = 'benefits';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::NAME,
    ];

    public function benefits()
    {
        return $this->belongsToMany(Benefit::class, 'hierarchy_benefit');
    }
}
