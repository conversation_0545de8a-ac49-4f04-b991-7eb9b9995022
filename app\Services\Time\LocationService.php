<?php

namespace App\Service\Time;

use App\Models\Customer\Time\Location;
use App\Models\Customer\Time\LocationDtl;
use App\Models\Constant\ConstData;
use App\Exceptions\SystemException;

use Illuminate\Database\Eloquent\Builder;

class LocationService
{
    public function getLocationIfEmployeeInside($connectionName, $employeeId, $longitude, $latitude) {
        $locations = $this->getLocationsInEmployee($connectionName, $employeeId);

        if (count($locations) == 0)
            throw new SystemException(ConstData::TIME_EXCEPTION, 331);

        $employeeLocation = null;
        foreach ($locations as $key => $location) {
            $isInside = $this->isInside($location->latitude, $location->longitude, $location->radius, $latitude, $longitude);
            if (!$isInside)
                continue;

            $employeeLocation = $location;
            break;
        }
        return $employeeLocation;
    }

    public function getLocationsInEmployee($connectionName, $employeeId) {
        $locations = new Location();
        $locations = $locations->on($connectionName)->whereHas(Location::RELATION_LOCATION_DTLS, function (Builder $query) use ($employeeId) {
            $query->where(LocationDtl::EMPLOYEE_ID, $employeeId);
        })->get();
        return $locations;
    }

    private function isInside($circle_x, $circle_y, $rad, $x, $y) {
        $rad = $rad / 100000;
        $employeeLocation = (pow($x - $circle_x, 2) + pow($y - $circle_y, 2));
        $companyLocation  = pow($rad, 2);
        return $employeeLocation < $companyLocation;
    }

    public function isLocationUser($connectionName, $employeeId) {
        $locationDtl = new LocationDtl();
        $locationDtl = $locationDtl->on($connectionName)->where(LocationDtl::EMPLOYEE_ID, $employeeId)->first();
        return isset($locationDtl);
    }
}
