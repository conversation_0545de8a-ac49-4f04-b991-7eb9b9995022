<?php

namespace App\Models\Customer\Time\Schedule;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TimeSchedule extends Model
{
    use HasFactory;

    const ID                    = 'id';
    const NAME                  = 'name';
    const SHORT_NAME            = 'short_name';
    const TYPE                  = 'type';
    const BEGIN_DATE            = 'begin_date';
    const END_DATE              = 'end_date';
    const HASH_STR              = 'hash_str';
    const CREATED_BY            = 'created_by';
    const UPDATED_BY            = 'updated_by';

    const RELATION_TIME_SCHEDULE_DTLS       = 'time_schedule_dtls';
    const RELATION_TIME_SCHEDULE_EMPLOYEES  = 'time_schedule_employees';

    const TYPE_MAIN        = 1;
    const TYPE_OTHER       = 2;

    protected $fillable = [
        self::ID,
        self::NAME,
        self::SHORT_NAME,
        self::TYPE,
        self::BEGIN_DATE,
        self::END_DATE,
        self::HASH_STR,
        self::CREATED_BY,
        self::UPDATED_BY,
    ];

    public function getHashStr() {
        $beginDate        = $this->begin_date;
        $hashTimeStr      = $beginDate.'-';
        foreach ($this->time_schedule_dtls as $key => $timeScheduleDtl) {
            $hashTimeStr .= '-' . $timeScheduleDtl->getHashStr();
        }
        $this->hash_str = $hashTimeStr;
        return $hashTimeStr;
    }

    public function getHashStrZeroDayOfWeek() {
        $timeScheduleDtl = collect($this->time_schedule_dtls)->first(function ($value, $key) {
            return $value->day_of_week == 0;
        });
        $hashTimeStr     = $timeScheduleDtl->getHashStr();
        $this->hash_str  = $hashTimeStr;
        return $hashTimeStr;
    }

    public function clearOtherColumns() {
        unset($this->time_schedule_dtls);
    }
}
