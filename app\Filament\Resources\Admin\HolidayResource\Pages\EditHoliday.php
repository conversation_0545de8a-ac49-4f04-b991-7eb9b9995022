<?php

namespace App\Filament\Resources\Admin\HolidayResource\Pages;

use App\Filament\Resources\Admin\HolidayResource;
use App\Models\Customer\Time\Holiday;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditHoliday extends EditRecord
{
    protected static string $resource = HolidayResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data[Holiday::UPDATED_BY] = auth()->user()->id;
        return $data;
    }
}
