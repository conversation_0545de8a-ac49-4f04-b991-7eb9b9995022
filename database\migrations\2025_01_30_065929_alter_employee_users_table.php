<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('employee_users', function (Blueprint $table) {
            $table->dropUnique('employee_users_phone_unique');
            $table->unique(['organization_id', 'phone']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employee_users', function (Blueprint $table) {
            $table->unique(['phone']);
            $table->dropUnique(['organization_id', 'phone']);
        });
    }
};
