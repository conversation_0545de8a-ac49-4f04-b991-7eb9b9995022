<?php

namespace App\Http\Requests\Customer\Time\Export;

use Illuminate\Foundation\Http\FormRequest;

class TimeExportRequest extends FormRequest
{
    const PARAMETER_LIMIT                   = 'limit';
    const PARAMETER_YEAR                    = 'year';
    const PARAMETER_MONTH                   = 'month';
    const PARAMETER_SF_TYPE                 = 'salary_frequency_type';
    const PARAMETER_FILTER_TYPE             = 'filter_type';
    const PARAMETER_HAS_DETAIL              = 'has_detail';
    const FILTER_DEPARTMENT_ID              = 'department_id';
    const FILTER_EMPLOYEE_IDS               = 'employee_ids';
    const FILTER_EMPLOYEE_ID                = 'employee_ids.*';
    const FILTER_GROUP_EMPLOYEE_ID          = 'group_employee_id';
    const SORT                              = 'sort';
    const SORT_FIELD                        = 'field';
    const SORT_TYPE                         = 'type';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::PARAMETER_YEAR                    => 'required|integer',
            self::PARAMETER_MONTH                   => 'required|integer|between:1,12',
            self::PARAMETER_SF_TYPE                 => 'required|integer|between:0,2',
            self::PARAMETER_FILTER_TYPE             => 'nullable|integer|between:0,3',
            self::PARAMETER_HAS_DETAIL              => 'required|boolean',
            self::FILTER_DEPARTMENT_ID              => 'required_if:filter_type,1|numeric',
            self::FILTER_EMPLOYEE_IDS               => 'required_if:filter_type,2|array',
            self::FILTER_EMPLOYEE_ID                => 'required_with:employee_ids|numeric',
            self::FILTER_GROUP_EMPLOYEE_ID          => 'required_if:filter_type,3|numeric',
        ];
    }
}
