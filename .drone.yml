---
kind: pipeline
type: exec
name: deploy

steps:
  - name: dev
    commands:
      - cd /home/<USER>/app/dev/php/rollers
      - ./time.sh
    when:
      branch: main
      event:
        - push
  - name: prod
    commands:
      - cd /home/<USER>/app/prod/php/rollers
      - ./time.sh
    when:
      branch: production
      event:
        - push
---
kind: signature
hmac: 45c9216ace3af388e1140efafb7ac0d74f94f6c64dc0438ddaed0abaa710be9e

...