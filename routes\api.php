<?php

use App\Http\Controllers\BankController;
use App\Http\Controllers\Customer\BenefitGroupController;
use App\Http\Controllers\Customer\Info\EmployeeUserController;
use App\Http\Controllers\Customer\Time\TimeRequestController;
use App\Http\Controllers\Customer\Mobile\MobileDashboardController;
use App\Http\Controllers\Customer\EmployeeVoucherController;
use App\Http\Controllers\Customer\EmployeeVoucherAmountController;
use App\Http\Controllers\Customer\EmployeeVoucherSpentController;
use App\Http\Controllers\Customer\CashRequestController;
use App\Services\EmployeeVoucherService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::prefix('auth')->group(function () {
    Route::post('login', [EmployeeUserController::class, 'login']);
});

Route::get('test', function () {
    return resolve(EmployeeVoucherService::class)->createEmployeeVouchersByAllOrganizations();
});

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::middleware('auth:sanctum')->group(function () {
    Route::prefix('auth')->group(function () {
        Route::get('me', [EmployeeUserController::class, 'me']);
        Route::post('update-password', [EmployeeUserController::class, 'updatePassword']);
    });
    Route::prefix('time-requests')->group(function () {
        Route::get('', [TimeRequestController::class, 'index']);
        Route::get('/discussed', [TimeRequestController::class, 'getDiscussedTimeRequestsForEmployeeUser']);
        Route::get('/undiscussed', [TimeRequestController::class, 'getUnDiscussedTimeRequestsForEmployeeUser']);
        Route::get('/{id}/discuss', [TimeRequestController::class, 'discuss']);
        Route::get('/valid-dates/{att_status}', [TimeRequestController::class, 'getValidDates']);
        Route::post('', [TimeRequestController::class, 'store']);
    });
    Route::prefix('employee-vouchers')->group(function () {
        Route::get('', [EmployeeVoucherController::class, 'index']);
        Route::get('me', [EmployeeVoucherController::class, 'me']);

    });
    Route::prefix('employee-voucher-amounts')->group(function () {
        Route::get('uniq_id', [EmployeeVoucherAmountController::class, 'uniqId']);
        Route::get('amount', [EmployeeVoucherAmountController::class, 'getEmployeeVoucherAmountByUniqId']);
        Route::get('spend', [EmployeeVoucherAmountController::class, 'spendVoucher']);
    });
    Route::prefix('employee-voucher-spents')->group(function () {
        Route::get('', [EmployeeVoucherSpentController::class, 'index']);
    });
    Route::prefix('mobile-dashboard')->group(function () {
        Route::get('', [MobileDashboardController::class, 'index']);
        Route::get('not-work-details', [MobileDashboardController::class, 'notWorkTimeDetail']);
        Route::get('not-work-details/{attendanceId}/sub', [MobileDashboardController::class, 'notWorkSubDetail']);
        Route::get('not-work-details2/{attendanceId}/sub', [MobileDashboardController::class, 'notWorkSubDetail2']);
    });
    Route::apiResource('benefit-groups', BenefitGroupController::class)->only(['index']);
    Route::apiResource('banks', BankController::class)->only(['index']);
    Route::apiResource('cash-requests', CashRequestController::class)->only(['index', 'store']);
});

