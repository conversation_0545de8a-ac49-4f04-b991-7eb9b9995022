<?php

namespace App\Filament\Resources\Admin\ApprovalConfigHdrResource\Pages;

use App\Models\Customer\Time\Approval\ApprovalConfigHdr;
use App\Models\Customer\Time\Approval\ApprovalConfigDtl;
use App\Filament\Resources\Admin\ApprovalConfigHdrResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditApprovalConfigHdr extends EditRecord
{
    protected static string $resource = ApprovalConfigHdrResource::class;
    
    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
