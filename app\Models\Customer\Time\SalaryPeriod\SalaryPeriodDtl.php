<?php

namespace App\Models\Customer\Time\SalaryPeriod;

use App\Http\Tools\DateTool;
use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalaryPeriodDtl extends Model
{
    use HasFactory;
    const TABLE = 'salary_period_dtls';

    const ID                            = 'id';
    const SALARY_PERIOD_ID              = 'salary_period_id';
    const BEGIN_DATE                    = 'begin_date';
    const END_DATE                      = 'end_date';
    const STATUS                        = 'status';

    const FULL_DATE                     = 'full_date';

    const RELATION_SALARY_PERIOD          = 'salary_period';
    const RELATION_SALARY_PERIOD_SUB_DTLS = 'salary_period_sub_dtls';

    const STATUS_CREATED   = 0;
    const STATUS_ARCHIVED  = 1;
    const STATUS_SENT      = 2;

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::ID,
        self::SALARY_PERIOD_ID,
        self::BEGIN_DATE,
        self::END_DATE,
        self::STATUS,
    ];

    public function getFullDateAttribute()
    {
        return "$this->begin_date - $this->end_date";
    }

    public function salary_period() {
        return $this->belongsTo(SalaryPeriod::class);
    }

    public function salary_period_sub_dtls() {
        return $this->hasMany(SalaryPeriodSubDtl::class)->orderBy(SalaryPeriodSubDtl::SALARY_DATE);
    }

    public function getNewSalaryDayFrequencySubDtls() {
        $beginDate = $this->begin_date;
        $endDate   = $this->end_date;
        $newSalaryDayFrequencySubDtls = [];
        while ($beginDate <= $endDate) {
            $newSalaryDayFrequencySubDtl = new SalaryPeriodSubDtl();
            $newSalaryDayFrequencySubDtl->salary_date = $beginDate;
            $newSalaryDayFrequencySubDtl->status      = SalaryPeriodSubDtl::STATUS_CREATED;
            $newSalaryDayFrequencySubDtls[] = $newSalaryDayFrequencySubDtl;
            $beginDate = DateTool::addDays($beginDate, 1)->format('Y-m-d');
        }
        return $newSalaryDayFrequencySubDtls;
    }

}
