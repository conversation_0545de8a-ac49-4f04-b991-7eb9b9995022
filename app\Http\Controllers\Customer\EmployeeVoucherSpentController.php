<?php

namespace App\Http\Controllers\Customer;

use App\Models\Customer\Employee;
use App\Models\Customer\Voucher\EmployeeVoucherSpent;
use App\Http\Resources\EmployeeVoucherSpent as EmployeeVoucherSpentResource;
use App\Services\ConnectionService;
use App\Http\Controllers\Controller;
use App\Http\Requests\GetEmployeeVoucherSpentRequest;

class EmployeeVoucherSpentController extends Controller
{
    public function getCN() {
        $service = resolve(ConnectionService::class);
        return $service->getCNForEmployeeUser();
    }

    public function index(GetEmployeeVoucherSpentRequest $request)
    {   
        $isMerchant        = $request->input(GetEmployeeVoucherSpentRequest::PARAMETER_IS_MERCHANT);
        $cn                = $this->getCN();
        $employeeUser      = auth()->user();
        $employee          = Employee::on($cn)->where(Employee::PHONE, $employeeUser->phone)->first();
        $emplVoucherSpents = EmployeeVoucherSpent::on($cn)->where(function ($query) use ($isMerchant, $employeeUser, $employee) {
            if ($isMerchant) 
                return $query->where(EmployeeVoucherSpent::MERCHANT_EMPLOYEE_USER_ID, $employeeUser->id);
            else
                return $query->where(EmployeeVoucherSpent::EMPLOYEE_ID, $employee->id);
        })->orderByDesc('created_at')->get();
        return EmployeeVoucherSpentResource::collection($emplVoucherSpents);
    }
}
