<?php

namespace App\Models\Customer\Time\RequestConfig;

use App\Models\Customer\Employee;
use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RequestConfigEmployee extends Model
{
    use HasFactory;

    const REQUEST_CONFIG_ID = 'request_config_id';
    const EMPLOYEE_ID       = 'employee_id';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::REQUEST_CONFIG_ID,
        self::EMPLOYEE_ID,
    ];

    public function request_config() {
        return $this->belongsTo(RequestConfig::class);
    }

    public function employee() {
        return $this->belongsTo(Employee::class);
    }
}
