<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Models\Constant\ConstData;
use App\Models\Customer\Department;
use App\Models\Customer\Position;
use App\Models\Customer\Employee;
use App\Models\Customer\EmployeeDtl;
use App\Models\Customer\Time\DeviceConfig;
use App\Services\Time\BioTimeService;

use App\Filament\Resources\Admin\EmployeeResource\Pages;
use App\Filament\Resources\Admin\EmployeeResource\RelationManagers;
use App\Models\Customer\Decision\Decision;
use Filament\Forms;
use Filament\Forms\Form; 
use Filament\Tables;
use Filament\Tables\Table;

class EmployeeResource extends Can
{
    protected static ?string $model = Employee::class;
    protected static ?string $navigationIcon = 'heroicon-o-face-smile';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Ажилтан';
    protected static ?string $modelLabel = 'ажилтан';
    protected static ?int $navigationSort = 3;
    protected static ?string $navigationGroup = 'Бүртгэл';
    protected static ?string $slug = 'employees';


    public static function form(Form $form): Form
    {
        return $form
                ->schema([
                    Forms\Components\Section::make()
                        ->schema([
                            Forms\Components\TextInput::make(Employee::LAST_NAME)
                                ->label('Овог')
                                ->maxLength(50)
                                ->required(),

                            Forms\Components\TextInput::make(Employee::FIRST_NAME)
                                ->label('Нэр')
                                ->maxLength(50)
                                ->required(),

                            Forms\Components\TextInput::make(Employee::CITIZEN_CODE)
                                ->label('Регистрийн №')
                                ->unique(ignoreRecord: true)
                                ->minLength(10)
                                ->maxLength(10)
                                ->required(),

                            Forms\Components\TextInput::make(Employee::PHONE)
                                ->label('Утас')
                                ->unique(ignoreRecord: true)
                                ->maxLength(50)
                                // ->disabled(fn (?Employee $record) => $record != null)
                                ->required(),

                            Forms\Components\Select::make(Employee::DEPARTMENT_ID)
                                ->label('Хэлтэс')
                                ->options(Department::all()->pluck(ConstData::NAME, ConstData::ID))
                                ->required()
                                ->searchable(),

                            Forms\Components\Select::make(Employee::POSITION_ID)
                                ->label('Албан тушаал')
                                ->options(
                                    function (callable $get) {
                                        return Position::getPositionsByDepartmentId($get('department_id'));
                                    })
                                ->required()
                                ->searchable(),

                            Forms\Components\TextInput::make(Decision::NUMBER)
                                ->label('Тушаалын №')
                                ->unique(table: Decision::class)
                                ->maxLength(10)
                                ->required()
                                ->hidden(fn (?Employee $record) => $record != null),

                            Forms\Components\DatePicker::make(Decision::DECISION_DATE)
                                ->label('Тушаалын огноо')
                                ->native(false)
                                ->default(now())
                                ->displayFormat('d/m/Y')
                                ->hidden(fn (?Employee $record) => $record != null),

                            Forms\Components\Select::make(Employee::RELATION_DEVICE_CONFIGS)
                                ->label('Төхөөрөмж')
                                ->relationship(name: Employee::RELATION_DEVICE_CONFIGS, titleAttribute: DeviceConfig::NAME)
                                ->multiple()
                                ->getOptionLabelFromRecordUsing(function (DeviceConfig $record, Employee $employee): ?string {
                                    $user = null;
                                    if (isset($employee->id)) {
                                        $host = $record->serial_number;
                                        $port = ConstData::DEFAULT_BIOTIME_PORT;
                                        $response = resolve(BioTimeService::class)->getUser($host, $port, $employee->employee_dtl->attendance_code);
                                        if (isset($response) && $response->status() == 200) {
                                            $user = $response->json();
                                        } else {
                                            return $record->name. ' - Холболт тасарсан';
                                        }
                                    }
                                    return $record->name. ' ' . (isset($user) ? 'Үүссэн' : 'Үүсээгүй');
                                })
                                ->searchable()
                                ->distinct()
                                ->searchingMessage('Хайж байна...')
                                ->loadingMessage('Ачаалж байна байна...')
                                ->noSearchResultsMessage('Хайлт олдсонгүй.')
                                ->suffixIcon('heroicon-m-finger-print'),    

                            Forms\Components\Fieldset::make('Employee_dtl')->label('Бусад')
                                    ->relationship('employee_dtl')
                                    ->schema([
                                        Forms\Components\DatePicker::make(EmployeeDtl::COMPANY_WORK_DATE)
                                            ->label('Ажиллаж эхлэх огноо')
                                            ->native(false)
                                            ->default(now())
                                            ->displayFormat('d/m/Y')
                                            ->hidden(fn (?EmployeeDtl $record) => $record != null),
                                        Forms\Components\TextInput::make(EmployeeDtl::ATTENDANCE_CODE)
                                            ->label('Ирцийн код')
                                            ->unique(table: EmployeeDtl::class, column: EmployeeDtl::ATTENDANCE_CODE, ignoreRecord: true)
                                            ->maxLength(10),
                                        Forms\Components\Toggle::make(EmployeeDtl::IS_SPECIAL)
                                            ->label('Онцгой эсэх')
                                            ->default(false)
                                            ->inline(false)
                                            ->onIcon('heroicon-m-bolt')
                                            ->offIcon('heroicon-m-bolt-slash'),
                                        Forms\Components\Hidden::make(EmployeeDtl::CREATED_BY)
                                            ->default(auth()->user()->id)
                                            ->disabled(fn (?EmployeeDtl $record) => $record != null),
                                    ])
                        ])
                        ->columns(2)
                        ->columnSpan(['lg' => fn (?Employee $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Employee $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Employee $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Employee $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('last_name')->label('Овог')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('first_name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('citizen_code')->label('Регистрийн №')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('phone')->label('Утас')->sortable()->searchable()->icon('heroicon-m-phone'),
                Tables\Columns\TextColumn::make('department.name')->label('Хэлтэс')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('position.name')->label('Албан тушаал')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('employee_dtl.attendance_code')->label('Ирцийн код')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEmployees::route('/'),
            'create' => Pages\CreateEmployee::route('/create'),
            'edit' => Pages\EditEmployee::route('/{record}/edit'),
        ];
    }
}
