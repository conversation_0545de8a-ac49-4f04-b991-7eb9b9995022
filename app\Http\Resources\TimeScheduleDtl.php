<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class TimeScheduleDtl
 *
 * @mixin \App\Models\Customer\Time\TimeScheduleDtl
 */
class TimeScheduleDtl extends JsonResource
{
    const ID                                = 'id';
    const TIME_SCHEDULE_ID                  = 'time_schedule_id';
    const TYPE                              = 'type';
    const DAY_OF_WEEK                       = 'day_of_week';
    const RB_TIME_STR                       = 'rb_time_str';
    const RE_TIME_STR                       = 're_time_str';
    const WORK_MIN                          = 'work_min';
    const BEGIN_TIME_STR                    = 'begin_time_str';
    const END_TIME_STR                      = 'end_time_str';
    const HAS_BREAK_TIME                    = 'has_break_time';
    const ADMIT_LATE_MIN                    = 'admit_late_min';
    const BFR_MIN                           = 'bfr_min';
    const AFR_MIN                           = 'afr_min';
    const OVER_BEGIN_TIME_STR               = 'over_begin_time_str';
    const OVER_END_TIME_STR                 = 'over_end_time_str';

    const TIME_SCHEDULE_DTL_BTS             = 'time_schedule_dtl_bts';
    const TIME_SCHEDULE                     = 'time_schedule';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                        => $this->id,
            self::TIME_SCHEDULE_ID          => $this->time_schedule_id,
            self::TYPE                      => $this->type,
            self::DAY_OF_WEEK               => $this->day_of_week,
            self::RB_TIME_STR               => $this->rb_time_str,
            self::RE_TIME_STR               => $this->re_time_str,
            self::WORK_MIN                  => $this->work_min,
            self::BEGIN_TIME_STR            => $this->begin_time_str,
            self::END_TIME_STR              => $this->end_time_str,
            self::HAS_BREAK_TIME            => $this->has_break_time,
            self::ADMIT_LATE_MIN            => $this->admit_late_min,
            self::BFR_MIN                   => $this->bfr_min,
            self::AFR_MIN                   => $this->afr_min,
            self::OVER_BEGIN_TIME_STR       => $this->over_begin_time_str,
            self::OVER_END_TIME_STR         => $this->over_end_time_str,
            self::TIME_SCHEDULE_DTL_BTS     => TimeScheduleDtlBt::collection($this->time_schedule_dtl_bts),
            self::TIME_SCHEDULE             => new TimeSchedule($this->time_schedule),
        ];
    }
}
