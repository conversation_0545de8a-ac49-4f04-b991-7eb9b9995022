<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class SalaryDayFrequency
 *
 * @mixin \App\Models\Customer\Time\SalaryDayFrequency
 */
class TimeRequestDtl extends JsonResource
{
    const ID                  = 'id';
    const TIME_REQUEST_ID     = 'time_request_id';
    const USER_ID             = 'user_id';
    const DESCRIPTION         = 'description';
    const CONFIRM_STATUS      = 'confirm_status';
    const USER                = 'user';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'                => $this->id,
            'time_request_id'   => $this->time_request_id,
            'user_id'           => $this->user_id,
            'description'       => $this->description,
            'confirm_status'    => $this->confirm_status,
            'user'              => new User($this->user),
        ];
    }
}
