<?php

namespace App\Http\Resources;

// use App\Models\Customer\Time\TimeScheduleEmployee;
use App\Models\Customer\Time\TimeSchedule as TimeScheduleModel;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class TimeSchedule
 *
 * @mixin \App\Models\Customer\Time\TimeSchedule
 */
class TimeSchedule extends JsonResource
{
    const ID                            = 'id';
    const NAME                          = 'name';
    const SHORT_NAME                    = 'short_name';
    const TYPE                          = 'type';
    const BEGIN_DATE                    = 'begin_date';
    const END_DATE                      = 'end_date';
    const TIME_SCHEDULE_DTLS            = 'time_schedule_dtls';
    const TIME_SCHEDULE_EMPLOYEES       = 'time_schedule_employees';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                        => $this->id,
            self::NAME                      => $this->name,
            self::SHORT_NAME                => $this->short_name,
            self::TYPE                      => $this->type,
            self::BEGIN_DATE                => $this->begin_date,
            self::END_DATE                  => $this->end_date,
            self::TIME_SCHEDULE_DTLS        => TimeScheduleDtl::collection($this->whenLoaded(TimeScheduleModel::RELATION_TIME_SCHEDULE_DTLS)),
            self::TIME_SCHEDULE_EMPLOYEES   => TimeScheduleEmployee::collection($this->whenLoaded(TimeScheduleModel::RELATION_TIME_SCHEDULE_EMPLOYEES)),
        ];
    }
}
