<?php

namespace App\Models\Customer\CashRequest;

use App\Services\EmployeeUserService;
use App\Services\ConnectionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CashRequestConfigDtl extends Model
{
    use HasFactory;

    const CASH_REQUEST_CONFIG_ID = 'cash_request_config_id';
    const LIMIT_AMOUNT           = 'limit_amount';
    const EMPLOYEE_USER_ID       = 'employee_user_id';

    const RELATION_CASH_REQUEST_CONFIG = 'cash_request_config';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::CASH_REQUEST_CONFIG_ID,
        self::LIMIT_AMOUNT,
        self::EMPLOYEE_USER_ID,
    ];

    public function cash_request_config()
    {
        return $this->belongsTo(CashRequestConfig::class);
    }

    public function getEmployeeDepartmentNameAttribute(): string
    {
        $employeeUser = resolve(EmployeeUserService::class)->getEmployeeUser($this->employee_user_id);
        if (!isset($employeeUser))
            return '';
        return $employeeUser->getEmployeeDepartmentName();
    }

    public function getEmployeePositionNameAttribute(): string
    {
        $employeeUser = resolve(EmployeeUserService::class)->getEmployeeUser($this->employee_user_id);
        if (!isset($employeeUser))
            return '';
        return $employeeUser->getEmployeePositionName();
    }

    public function getEmployeeDisplayNameAttribute(): string
    {
        $employeeUser = resolve(EmployeeUserService::class)->getEmployeeUser($this->employee_user_id);
        if (!isset($employeeUser))
            return '';
        return $employeeUser->getEmployeeDisplayName();
    }
}
