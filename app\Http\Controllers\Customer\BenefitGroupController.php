<?php

namespace App\Http\Controllers\Customer;    

use App\Models\Customer\Employee;
use App\Models\Customer\BenefitGroup;
use App\Http\Resources\BenefitGroup as BenefitGroupResource;
use App\Http\Controllers\Controller;
use App\Services\ConnectionService;

class BenefitGroupController extends Controller
{
    public function getCN() {
        $service = resolve(ConnectionService::class);
        return $service->getCNForEmployeeUser();
    }

    public function getAUEmployee() 
    {
        $cn           = $this->getCN();
        $employeeUser = auth()->user();
        $employee     = Employee::on($cn)->where(Employee::PHONE, $employeeUser->phone)->first();
        return $employee;
    }

    public function index()
    {
        $employee = $this->getAUEmployee();
        if (!$employee->position->hierarchy)
            return BenefitGroupResource::collection([]);

        $cn = $this->getCN();
         
        // 1. Ажилтны бүх benefit-ууд
        $benefits = $employee->position->hierarchy->benefits()->get();

        // 2. Benefit-ийг group-р бүлэглэх
        $grouped = $benefits->groupBy('benefit_group_id');
        
        $newGrouped = collect();
        foreach ($grouped as $groupId => $groupedBenefits) {
            $group = BenefitGroup::on($cn)->find($groupId);
            if ($group) {
                $group->setRelation('benefits', $groupedBenefits);
                $newGrouped->push($group);
            }
        }
        return BenefitGroupResource::collection($newGrouped);
    }
}
