<?php

use App\Models\DatabaseConfig;
use App\Models\Constant\ConstData;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDatabaseConfigsTable extends Migration
{
    public function up()
    {
        Schema::connection(ConstData::ADMIN_DB)->create(DatabaseConfig::TABLE, function (Blueprint $table) {
            $table->id();
            $table->string('connection_name')->unique();
            $table->string('driver');
            $table->string('host');
            $table->string('port');
            $table->string('database')->unique();
            $table->string('user_name');
            $table->string('password');
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists(DatabaseConfig::TABLE);
    }
}
