<?php

namespace App\Http\Resources;

use App\Http\Tools\DateTool;

use App\Commons\Methods;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class Notification extends JsonResource
{
    const ID                    = 'id';
    const TYPE                  = 'type';
    const NOTIFIABLE_TYPE       = 'notifiable_type';
    const NOTIFIABLE_ID         = 'notifiable_id';
    const DATA                  = 'data';
    const READ_AT               = 'read_at';
    const CREATED_AT            = 'created_at';
    const UPDATED_AT            = 'updated_at';
    const CREATED_AT_PARSE      = 'created_at_parse';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                    => $this->id,
            self::TYPE                  => $this->type,
            self::NOTIFIABLE_TYPE       => $this->notifiable_type,
            self::NOTIFIABLE_ID         => $this->notifiable_id,
            self::DATA                  => $this->data,
            self::READ_AT               => $this->read_at,
            self::CREATED_AT            => $this->created_at,
            self::UPDATED_AT            => $this->updated_at,
            self::CREATED_AT_PARSE      => Carbon::parse(DateTool::createdAtToUb($this->created_at))->toDateTimeString(),
        ];
    }
}
