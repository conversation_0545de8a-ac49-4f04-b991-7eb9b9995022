<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class Apartment
 *
 * @mixin \App\Models\Apartment
 */
class Organization extends JsonResource
{
    const ID                  = 'id';
    const NAME                = 'name';
    const REGISTRATION_NUMBER = 'registration_number';
    const CITY_ID             = 'city_id';
    const DISTRICT_ID         = 'district_id';
    const HOROO_ID            = 'horoo_id';
    const ADDRESS             = 'address';
    const LOGO                = 'logo';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            self::ID                       => $this->id,
            self::NAME                     => $this->name,
            self::REGISTRATION_NUMBER      => $this->registration_number,
            self::CITY_ID                  => $this->city_id,
            self::DISTRICT_ID              => $this->district_id,
            self::HOROO_ID                 => $this->horoo_id,
            self::ADDRESS                  => $this->address,
            self::LOGO                     => $this->logo,
        ];
    }
}
