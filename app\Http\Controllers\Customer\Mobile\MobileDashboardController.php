<?php

namespace App\Http\Controllers\Customer\Mobile;

use App\Exceptions\SystemException;
use App\Models\Customer\MobileDashboard;
use App\Models\Customer\Time\TimeRequest\TimeRequest;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Models\Customer\Employee;
use App\Models\Customer\Time\Attendance\EmployeeAttendance;
use App\Http\Controllers\Controller;
use App\Http\Resources\MobileDashboard as MobileDashboardResource;
use App\Http\Resources\NotWorkTimeDetail as NotWorkTimeDetailResource;
use App\Http\Resources\NotWorkTimeSubDetail as NotWorkTimeSubDetailResource;
use App\Services\Time\SuperSalaryPeriodService;
use App\Services\Time\EmployeeAttendanceService;
use App\Services\Time\TimeRequestService;
use App\Services\Time\MainTimeScheduleService;
use App\Services\Time\HolidayService;
use App\Services\ConnectionService;
use App\Http\Tools\DateTool;
use App\Models\Constant\ConstData;

class MobileDashboardController extends Controller
{
    public function getCN() {
        $service = resolve(ConnectionService::class);
        return $service->getCNForEmployeeUser();
    }
   
    /**
     * @authenticated
     */
    public function index()
    {
        $cn              = $this->getCN();
        $employeeUser    = auth()->user();
        $employee        = Employee::on($cn)->where(Employee::PHONE, $employeeUser->phone)->first();
        $superSalaryPeriodDtl = resolve(SuperSalaryPeriodService::class)->getSuperSalaryPeriodDtlByNowDate($cn, $employee->department_id);
        if (!isset($superSalaryPeriodDtl))
            throw new SystemException(ConstData::TIME_EXCEPTION, 311);
        $beginDate    = $superSalaryPeriodDtl->getBeginDate();
        $endDate      = $superSalaryPeriodDtl->getEndDate();
        $timeRequests = resolve(TimeRequestService::class)->getEmployeeTimeRequestsByBetweenDates($cn, $beginDate, $endDate);
        $niitKhuseltiinToo = $timeRequests->count();
        $batlagdsanKhuseltiinToo = $niitKhuseltiinToo > 0 ? $timeRequests->filter(function ($timeRequest) {
            return $timeRequest->confirm_status == TimeRequest::CONFIRM_STATUS_CONFIRMED;
        })->count() : 0;
        $tsutslagsanKhuseltiinToo = $niitKhuseltiinToo > 0 ? $timeRequests->filter(function ($timeRequest) {
            return $timeRequest->confirm_status == TimeRequest::CONFIRM_STATUS_UNCONFIRMED;
        })->count() : 0;
        $khuleegdejBuiKhuseltiinToo = $niitKhuseltiinToo > 0 ? $timeRequests->filter(function ($timeRequest) {
            return $timeRequest->confirm_status == TimeRequest::CONFIRM_STATUS_SENT;
        })->count() : 0;

        $wdTotalTime        = $superSalaryPeriodDtl->getWdTotalTimeAttribute();
        $employeeAttendance = resolve(EmployeeAttendanceService::class)->getEmployeeAttendanceGroupByDateAndEmployee($cn, $employee->id, $beginDate, $endDate);
        $taniEneSariinUnuudriigKhurtlekhAjilaaguiTsag = $employeeAttendance ? $employeeAttendance->total_not_work_time : "0";

        $nowDate                   = DateTool::nowDate();
        $mainTimeSchedule          = resolve(MainTimeScheduleService::class)->getMainTimeScheduleByAttDate($cn, $nowDate);
        if (!$mainTimeSchedule)
            throw new SystemException(ConstData::TIME_EXCEPTION, 301);
        $mainTimeScheduleDtl       = $mainTimeSchedule->getMainTimeScheduleDtlByDate($nowDate);
        
        $otherTimeScheduleEmployee = $employee->other_time_shedule_employees->first(function ($value, $key) use ($nowDate, $employee) {
            return $value->begin_date <= $nowDate && $value->employee_id == $employee->id;
        });
        $otherTimeScheduleEmployeeDtl = $otherTimeScheduleEmployee && $otherTimeScheduleEmployee->other_time_schedule ? $otherTimeScheduleEmployee->other_time_schedule->getOtherTimeScheduleDtlByDate($nowDate) : null;
        $holidates        = resolve(HolidayService::class)->getHolidatesByBetweenDates($cn, $beginDate, $endDate);
        $hasHoliday       = collect($holidates)->contains($nowDate);
        $timeScheduleDtl  = !$otherTimeScheduleEmployeeDtl && !$hasHoliday ? $mainTimeScheduleDtl : $otherTimeScheduleEmployeeDtl;

        if (2 === $timeScheduleDtl->type) {
            // Dynamic
            $bDate = DateTool::setZeroTime($nowDate);
            $eDate = DateTool::setZeroTime(DateTool::addDays($nowDate, 1));

            $filteredDeviceAttendances = $employee->employee_dtl->raw_attendances->filter(function ($value, $key) use ($bDate, $eDate) {
                return $value->register_date >= $bDate && $value->register_date <= $eDate;
            })->values();

            $timeScheduleDtl->setDynamicBeginAndEndTime($nowDate, $filteredDeviceAttendances, $timeRequests);
        } else if (1 == $timeScheduleDtl->type) {
            // Simple
            $timeScheduleDtl->setSimpleBeginAndEndTime($nowDate);
        }

        $rawAttendanceTodayFirst = $employee->employee_dtl->raw_attendances()->where(fn ($q) => $q->whereDate('register_date', $nowDate))->first();
        $mobileDashboard  = new MobileDashboard([
            MobileDashboard::IPSEN_TSAG                                         => $rawAttendanceTodayFirst ? $rawAttendanceTodayFirst->register_date : null,
            MobileDashboard::TARAKH_TSAG                                        => $timeScheduleDtl->end_date_time ? DateTool::getDateWithTime($timeScheduleDtl->end_date_time) : null,
            MobileDashboard::ENE_SARD_AJILVAL_ZOKHIKH_TSAG                      => $wdTotalTime,
            MobileDashboard::TANI_ENE_SARIIN_UNUUDRIIG_KHURTLEKH_AJILAAGUI_TSAG => DateTool::convertHourAndMin($taniEneSariinUnuudriigKhurtlekhAjilaaguiTsag),
            MobileDashboard::NIIT_KHUSELTIIN_TOO                                => $niitKhuseltiinToo,
            MobileDashboard::BATLAGDSAN_KHUSELTIIN_TOO                          => $batlagdsanKhuseltiinToo,
            MobileDashboard::TSUTSLAGSAN_KHUSELTIIN_TOO                         => $tsutslagsanKhuseltiinToo,
            MobileDashboard::KHULEEGDEJ_BUI_KHUSELTIIN_TOO                      => $khuleegdejBuiKhuseltiinToo,
        ]);
        return new MobileDashboardResource($mobileDashboard);
    }

    /**
     * @authenticated
     */
    public function notWorkTimeDetail()
    {
        $cn              = $this->getCN();
        $sspService      = resolve(SuperSalaryPeriodService::class);
        $employeeUser    = auth()->user();
        $employee        = Employee::on($cn)->where(Employee::PHONE, $employeeUser->phone)->first();
        $salaryPeriodDtl = $sspService->getSuperSalaryPeriodDtlByNowDate($cn, $employee->department_id);
        if (!isset($salaryPeriodDtl))
            return 'Salary period not found';

        $ssDate     = $salaryPeriodDtl->super_salary_period->getDate();
        $prevSSDate = DateTool::subMonth($ssDate, 1);
        $year       = $prevSSDate->year;
        $month      = $prevSSDate->month;
        $prevSSPDtl = $sspService->getSuperSalaryPeriodDtlByYearMonth($cn, $year, $month, $employee->department_id);
        $beginDate  = isset($prevSSPDtl) ? $prevSSPDtl->getBeginDate() : $salaryPeriodDtl->getBeginDate();
        $endDate    = $salaryPeriodDtl->getEndDate();
        
        $mobileDashboardDetails = resolve(EmployeeAttendanceService::class)->getTimes($cn, $employee->id, $beginDate, $endDate);
        return NotWorkTimeDetailResource::collection($mobileDashboardDetails);
    }

    /**
     * @authenticated
     */
    public function notWorkSubDetail($attendanceId)
    {
        $cn = $this->getCN();
        $employeeAttendance = EmployeeAttendance::on($cn)->find($attendanceId);
        return NotWorkTimeSubDetailResource::collection($employeeAttendance->employee_attendance_dtls);
    }

    /**
     * @authenticated
     */
    public function notWorkSubDetail2($attendanceId)
    {
        $cn                 = $this->getCN();
        $employeeAttendance = EmployeeAttendance::on($cn)->find($attendanceId);

        if (!$employeeAttendance || $employeeAttendance->employee_attendance_dtls->isEmpty())
            return [];

        $timeRequests       = [];
        $datum              = null;
        $attendanceDetails  = $employeeAttendance->employee_attendance_dtls;
        $absentCount        = $attendanceDetails->where(EmployeeAttendanceDtl::ATTENDANCE_STATUS, EmployeeAttendanceDtl::ATTENDANCE_STATUS_ABSENT)->count();
        $diffMin            = 0;
        foreach ($attendanceDetails as $key => $value) {
            if (!in_array($value->attendance_status, [EmployeeAttendanceDtl::ATTENDANCE_STATUS_ABSENT, EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK]))
                continue;

            $beginDate     = DateTool::getDateWithTime($value->begin_date);
            $endDate       = DateTool::getDateWithTime($value->end_date);
            $diffMin      += DateTool::getMinuteBetweenDates($beginDate, $endDate);
            $name          = DateTool::showHourAndMinuteByMinute($diffMin). ' тасалсан';

            if ($datum == null && $value->attendance_status == EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK)
                continue;

            if ($datum == null && $value->attendance_status == EmployeeAttendanceDtl::ATTENDANCE_STATUS_ABSENT) {
                $datum     = [ 'begin_date' => $beginDate];
                if ($absentCount === 1) {
                    $datum['end_date'] = $endDate;
                    $datum['name']     = $name;
                    $timeRequests[]    = $datum;
                    break;
                }
            } else {
                $datum['end_date'] = $endDate;
                $datum['name']     = $name;
                $timeRequests[]    = $datum;
                $diffMin           = 0;
                $datum             = null;
            }
        }
        return $timeRequests;
    }
}
