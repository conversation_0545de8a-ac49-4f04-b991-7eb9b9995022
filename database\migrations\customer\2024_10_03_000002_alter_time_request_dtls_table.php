<?php

use App\Models\Customer\Time\TimeRequestDtl;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('time_request_dtls', function (Blueprint $table) {
            $table->foreignId('employee_user_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('time_request_dtls', function (Blueprint $table) {
            $table->dropColumn('employee_user_id');
        });
    }
};
