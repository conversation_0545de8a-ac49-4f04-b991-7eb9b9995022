<?php

namespace App\Http\Tools;

use Carbon\Carbon;

/**
 * class Apartment
 *
 * @mixin \App\Models\Apartment
 */
class DateTool
{
    public static function getPrevDate($date) {
        return Carbon::parse($date)->subDay();
    }

    public static function nowDate() {
        return Carbon::now()->format('Y-m-d');
    }

    public static function nowYearMonth() {
        return Carbon::now()->format('Ym');
    }

    public static function nowDateTime() {
        return Carbon::now()->format('Y-m-d H:i:s');
    }

    public static function nowDateWithoutTime() {
        return Carbon::now()->hour(0)->minute(0)->second(0);
    }

    public static function getYesterday() {
        return self::getPrevDate(self::nowDate())->format('Y-m-d');
    }

    public static function getTomorrowFromDate($date) {
        return Carbon::parse($date)->addDay();
    }

    public static function greaterThan($date1, $date2) {
        return Carbon::parse($date1)->gt(Carbon::parse($date2));
    }

    public static function addDays($date, $day) {
        return Carbon::parse($date)->addDays($day);
    }

    public static function addMinute($date, $minute) {
        return Carbon::parse($date)->addMinute($minute);
    }

    public static function subMinute($date, $minute) {
        return Carbon::parse($date)->subMinute($minute);
    }

    public static function getTomorrowBeginDate($date) {
        return Carbon::parse($date)->addDays(1)->hour(0)->minute(0)->second(0);
    }

    public static function getDateOfWeek($date) {
        $dayOfWeek = Carbon::parse($date)->dayOfWeek;
        $dayOfWeek = $dayOfWeek === 0 ? 7 : $dayOfWeek;
        return $dayOfWeek;
    }

    public static function createDateWithTime($year, $month, $day, $hour, $minute) {
        return Carbon::create($year, $month, $day, $hour, $minute, 0);
    }

    public static function createDate($year, $month, $day) {
        return Carbon::create($year, $month, $day, 0, 0, 0);
    }

    public static function setDateTime($date, $hour, $minute) {
        return Carbon::parse($date)->hour($hour)->minute($minute)->second(0);
    }

    public static function setZeroSecond($date) {
        return Carbon::parse($date)->second(0);
    }

    public static function setOnlyDay($date, $day) {
        return Carbon::parse($date)->day($day);
    }

    public static function setZeroTime($date) {
        return self::getDateWithTime(Carbon::parse($date)->hour(0)->minute(0)->second(0));
    }

    public static function setEndDateTime($date) {
        return self::getDateWithTime(Carbon::parse($date)->hour(23)->minute(59)->second(59));
    }

    public static function getMinuteBetweenDates($startDate, $finishDate) {
        $startTime = Carbon::parse($startDate);
        $finishTime = Carbon::parse($finishDate);
        $totalDuration = $finishTime->diffInMinutes($startTime);
        return $totalDuration;
    }

    public static function parseCarbonDate($date) {
        return Carbon::parse($date);
    }

    public static function createDateWithFormat($year, $month, $day) {
        return Carbon::create($year, $month, $day, 0, 0, 0)->format('Y-m-d');
    }

    public static function getDate($date) {
        return Carbon::parse($date)->format('Y-m-d');
    }

    public static function getTime($date) {
        return Carbon::parse($date)->format('H:i');
    }

    public static function subMonth($date, $month)
    {
        return Carbon::parse($date)->subMonth($month);
    }

    public static function getDateWithTime($date) {
        return Carbon::parse($date)->format('Y-m-d H:i:s');
    }

    public static function getYear($date) {
        return intval(Carbon::parse($date)->format("Y"));
    }

    public static function getMonth($date) {
        return intval(Carbon::parse($date)->format("m"));
    }

    public static function getDay($date) {
        return intval(Carbon::parse($date)->format("d"));
    }

    public static function getDifferenceMonth($beginDate, $endDate) {
        $ts1 = strtotime($beginDate);
        $ts2 = strtotime($endDate);

        $year1 = date('Y', $ts1);
        $year2 = date('Y', $ts2);

        $month1 = date('m', $ts1);
        $month2 = date('m', $ts2);

        $diff = (($year2 - $year1) * 12) + ($month2 - $month1);
        return $diff;
    }

    public static function getLastDayOfMonth($date) {
        return date("Y-m-t", strtotime($date));
    }

    public static function showHourAndMinuteByMinute($minutes) {
        $hh = floor($minutes / 60);
        $hh = strlen($hh) === 1 ? '0'.$hh : $hh;
        $mm = ($minutes % 60);
        $mm = strlen($mm) === 1 ? '0'.$mm : $mm;
        return $hh.':'.$mm;
    }

    public static function createdAtToUb($createdAt){
        return Carbon::parse($createdAt)->addHours(8)->toDateTimeString();
    }

    public static function convertHourAndMin($totalMinutes) {
        $hours   = floor($totalMinutes / 60);
        $minutes = $totalMinutes % 60;
        return $hours == 0 && $minutes == 0 ? '0' : trim((($hours == 0 ? " " : "{$hours} цаг") .' '. ($minutes == 0 ? "" : "{$minutes} минут")));
    }

    // 23:59-с дотогш байх ёстой
    public static function convertTime($minutes) {
        $hour   = floor($minutes / 60);
        $minute = $minutes % 60;
        $dd     = self::setDateTime(now(), $hour, $minute);
        return self::getTime($dd);
    }

    public static function convertMin($date) {
        $hour   = intval(Carbon::parse($date)->format("H"));
        $minute = intval(Carbon::parse($date)->format("i"));
        return ($hour * 60) + $minute;
    }
}
