<?php

namespace App\Models\Customer\Time\TimeRequest;

use App\Models\User;
use App\Models\EmployeeUser;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TimeRequestDtl extends Model
{
    use HasFactory;
    const TABLE                     = 'time_requests';

    const ID                        = 'id';
    const TIME_REQUEST_ID           = 'time_request_id';
    const USER_ID                   = 'user_id';
    const EMPLOYEE_USER_ID          = 'employee_user_id';
    const DESCRIPTION               = 'description';
    const CONFIRM_STATUS            = 'confirm_status';

    const RELATION_USER             = 'user';
    const RELATION_TIME_REQUEST     = 'time_request';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        self::ID,
        self::TIME_REQUEST_ID,
        self::USER_ID,
        self::EMPLOYEE_USER_ID,
        self::CONFIRM_STATUS
    ];

    public function user() {
        return $this->belongsTo(User::class);
    }

    public function employee_user() {
        return $this->belongsTo(EmployeeUser::class);
    }

    public function time_request() {
        return $this->belongsTo(TimeRequest::class);
    }
}
