<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('request_config_dtls'))
            return;
        Schema::create('request_config_dtls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('request_config_id')->constrained('request_configs')->onDelete('cascade');
            $table->integer('limit_hours')->default(0);
            $table->string('type');
            $table->unsignedBigInteger('employee_user_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('request_config_dtls');
    }
};
