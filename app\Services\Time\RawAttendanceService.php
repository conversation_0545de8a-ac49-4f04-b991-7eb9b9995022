<?php

namespace App\Services\Time;

use App\Models\Customer\Time\DeviceConfig;
use App\Models\Customer\Time\Attendance\RawAttendance;
use App\Http\Tools\DateTool;
use App\Services\Time\BioTimeService;
use App\Services\AdminService;
use App\Services\ConnectionService;
use App\Jobs\ProcessRawAttendance;
use App\Models\Constant\ConstData;
use Carbon\Carbon;

class RawAttendanceService
{
    /**
     * RawAttendance шинэчлэн бэлтгэж байх
     */
    public function prepareRawAttendancesForAllAdminUsers() {
        $service = resolve(AdminService::class);
        $users   = $service->getAdminUsers();
        foreach ($users as $key => $user) {
            $cn = ConnectionService::connectionNameByUser($user);
            ProcessRawAttendance::dispatch($cn, $user->id);
        }
    }

    public function downloadDeviceAttendances($cn) {
        $deviceConfigs  = DeviceConfig::on($cn)->get();
        $bioTimeService = resolve(BioTimeService::class);
        $before15Days   = Carbon::now()->subDays(15)->hour(0)->minute(0)->second(0)->format('Y-m-d H:i:s');
        foreach ($deviceConfigs as $key => $deviceConfig) {
            try {
                $response  = $bioTimeService->getRawAttendances($deviceConfig->serial_number, ConstData::DEFAULT_BIOTIME_PORT, $before15Days, null);
                if (empty($response))
                    continue;
                $newAttendances = [];
                foreach ($response as $key => $value) {
                    $attendanceCode = $value['user_id'];
                    $registerDate   = $value['timestamp'];
                    if (RawAttendance::on($cn)
                        ->where(RawAttendance::ATTENDANCE_CODE, $attendanceCode)
                        ->where(RawAttendance::REGISTER_DATE, $registerDate)
                        ->exists()) {
                        continue;
                    }

                    $newAttendances[] = [
                        RawAttendance::ATTENDANCE_CODE => $attendanceCode,
                        RawAttendance::REGISTER_DATE   => $registerDate,
                        RawAttendance::TYPE            => RawAttendance::TYPE_DEFAULT,
                        RawAttendance::FROM_NAME       => $deviceConfig->name,
                        RawAttendance::CREATED_AT      => now(),
                        RawAttendance::UPDATED_AT      => now(),
                    ];
                }
                if (!empty($newAttendances))
                    RawAttendance::on($cn)->insert($newAttendances);
                $deviceConfig->update([DeviceConfig::LAST_DOWNLOAD_DATE => DateTool::nowDateTime()]);    
            } catch (\Throwable $th) {
                continue;
            }
        }
    }

    public function getRawAttendancesByBetweenDates($connectionName, $beginDate, $endDate) {
        return RawAttendance::on($connectionName)->whereBetween(RawAttendance::REGISTER_DATE, [$beginDate, DateTool::getTomorrowBeginDate($endDate)])->orderBy(RawAttendance::REGISTER_DATE)->get();
    }
}
