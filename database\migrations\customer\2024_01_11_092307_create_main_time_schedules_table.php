<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMainTimeSchedulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('main_time_schedules'))
            return;
        Schema::create('main_time_schedules', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('short_name');
            $table->date('begin_date')->unique();
            $table->longText('hash_str');
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('main_time_schedules');
    }
}
