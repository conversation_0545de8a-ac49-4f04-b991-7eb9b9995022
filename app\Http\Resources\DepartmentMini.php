<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * class Apartment
 *
 * @mixin \App\Models\Apartment
 */
class DepartmentMini extends JsonResource
{
    const ID                    = 'id';
    const NAME                  = 'name';
    const SHORT_NAME            = 'short_name';

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'             => $this->id,
            'name'           => $this->name,
            'short_name'     => $this->short_name,
        ];
    }
}
