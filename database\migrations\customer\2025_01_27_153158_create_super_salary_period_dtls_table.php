<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('super_salary_period_dtls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('super_salary_period_id')->constrained('super_salary_periods')->onDelete('cascade');
            $table->tinyInteger('type')->default(1);
            $table->integer('wd_count')->nullable();
            $table->integer('wd_min')->nullable();
            $table->timestamps();

            $table->index('super_salary_period_id'); 
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('super_salary_period_dtls');
    }
};
