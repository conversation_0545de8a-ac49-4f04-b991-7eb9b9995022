<?php

namespace App\Filament\Components;

use Filament\Forms;

class ChronoCountComponent
{
    public static function make(string $name = 'chrono_count'): Forms\Components\Group
    {
        return Forms\Components\Group::make([
            Forms\Components\Select::make("{$name}_static_unit")
                ->label('Static Chrono Unit')
                ->options([
                    'minute' => 'Minute',
                    'hour' => 'Hour',
                    'halfday' => 'Half Day',
                    'day' => 'Day',
                    'month' => 'Month',
                    'quarter' => 'Quarter',
                    'halfyear' => 'Half Year',
                    'year' => 'Year'
                ])
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\TextInput::make("{$name}_count")
                ->label('Count')
                ->numeric()
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\Select::make("{$name}_chrono_unit")
                ->label('Chrono Unit')
                ->options([
                    'minute' => 'Minute',
                    'hour' => 'Hour',
                    'halfday' => 'Half Day',
                    'day' => 'Day',
                    'month' => 'Month',
                    'quarter' => 'Quarter',
                    'halfyear' => 'Half Year',
                    'year' => 'Year'
                ])
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\TextInput::make("{$name}_chrono_value")
                ->label('Chrono Value')
                ->numeric()
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\Hidden::make("{$name}_formula")
        ])->columns(4);
    }

    private static function updateFormula(string $name, callable $set, callable $get): void
    {
        $staticUnit = $get("{$name}_static_unit");
        $count = $get("{$name}_count");
        $chronoUnit = $get("{$name}_chrono_unit");
        $chronoValue = $get("{$name}_chrono_value");

        if ($staticUnit && $count !== null && $count !== '' && $chronoUnit && $chronoValue !== null && $chronoValue !== '') {
            // New format: count,staticUnit,count,chronoUnit,chronoValue
            $formula = "count,{$staticUnit},{$count},{$chronoUnit},{$chronoValue}";
            $set("{$name}_formula", $formula);
        }
    }

    /**
     * Parse a formula string back into component values
     * Formula format: "count,staticUnit,count,chronoUnit,chronoValue"
     */
    public static function parseFormula(string $formula): array
    {
        $result = [
            'static_unit' => null,
            'count' => null,
            'chrono_unit' => null,
            'chrono_value' => null,
        ];

        // Split by comma and check if it's the new format
        $parts = explode(',', $formula);

        if (count($parts) === 5 && $parts[0] === 'count') {
            $staticUnit = $parts[1];
            $count = (int)$parts[2];
            $chronoUnit = $parts[3];
            $chronoValue = (int)$parts[4];

            $result['static_unit'] = $staticUnit;
            $result['count'] = $count;
            $result['chrono_unit'] = $chronoUnit;
            $result['chrono_value'] = $chronoValue;
        }

        return $result;
    }
}