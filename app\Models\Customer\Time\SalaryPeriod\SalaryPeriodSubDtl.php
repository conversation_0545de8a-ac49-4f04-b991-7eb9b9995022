<?php

namespace App\Models\Customer\Time\SalaryPeriod;

use App\Http\Tools\DateTool;
use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalaryPeriodSubDtl extends Model
{
    use HasFactory;
    const TABLE = 'salary_period_sub_dtls';

    const ID                            = 'id';
    const SALARY_PERIOD_DTL_ID          = 'salary_period_dtl_id';
    const SALARY_DATE                   = 'salary_date';
    const STATUS                        = 'status';

    const RELATION_SALARY_PERIOD_DTL    = 'salary_period_dtl';

    const STATUS_CREATED          = 0;
    const STATUS_CONFIRMED        = 1;

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    protected $fillable = [
        self::ID,
        self::SALARY_PERIOD_DTL_ID,
        self::SALARY_DATE,
        self::STATUS,
    ];

    public function isLastDayOfMonth() {
        return DateTool::getLastDayOfMonth($this->salary_date) === $this->salary_date;
    }

    public function salary_period_dtl() {
        return $this->belongsTo(SalaryPeriodDtl::class);
    }
}
