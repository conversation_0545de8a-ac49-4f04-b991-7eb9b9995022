<?php

namespace App\Filament\Resources\Admin\EmployeeVoucherSpentSumAmountResource\Pages;

use App\Filament\Resources\Admin\EmployeeVoucherSpentSumAmountResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewEmployeeVoucherSpentSumAmount extends ViewRecord
{
    protected static string $resource = EmployeeVoucherSpentSumAmountResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }
}
