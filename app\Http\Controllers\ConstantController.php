<?php

namespace App\Http\Controllers;

use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Http\Controllers\Controller;

class ConstantController extends Controller
{
    public function getAttStatusesInTimeRequest()
    {
        $data = [];
        foreach (EmployeeAttendanceDtl::REQUEST_ATTENDANCE_STATUS as $key => $value) {
            switch ($value) {
                case EmployeeAttendanceDtl::ATTENDANCE_STATUS_WORK:
                    $datum = (object) ['id' => $value, 'name' => 'Ажилласан'];
                    break;
                case EmployeeAttendanceDtl::ATTENDANCE_STATUS_FURLOUGH:
                    $datum = (object) ['id' => $value, 'name' => 'Чөлөө'];
                    break;
                case EmployeeAttendanceDtl::ATTENDANCE_STATUS_SALARY_FURLOUGH:
                    $datum = (object) ['id' => $value, 'name' => 'Цалинтай чөлөө'];
                    break;
                case EmployeeAttendanceDtl::ATTENDANCE_STATUS_OVER:
                    $datum = (object) ['id' => $value, 'name' => 'Илүү цаг'];
                    break;
                case EmployeeAttendanceDtl::ATTENDANCE_STATUS_SICK:
                    $datum = (object) ['id' => $value, 'name' => 'Өвчтэй'];
                    break;
                case EmployeeAttendanceDtl::ATTENDANCE_STATUS_COMPENSATORY_REST:
                    $datum = (object) ['id' => $value, 'name' => 'Нөхөж амарсан'];
                    break;
                case EmployeeAttendanceDtl::ATTENDANCE_STATUS_REQUEST_WORK:
                    $datum = (object) ['id' => $value, 'name' => 'Хүсэлтээр ажилласан'];
                    break;
                default:
                    # code...
                    break;
            }
            $data[] = $datum;
        }
        return $data;
    }
}
