<?php

namespace App\Http\Controllers\Customer;

use App\Enums\CashRequestStatusEnum;
use App\Models\Bank;
use App\Models\Customer\Employee;
use App\Models\Customer\CashRequest\CashRequest;
use App\Http\Requests\Customer\CashRequest\GetCashRequest;
use App\Http\Requests\Customer\CashRequest\StoreCashRequest;
use App\Http\Resources\CashRequest as CashRequestResource;  
use App\Http\Controllers\Controller;
use App\Models\Customer\CashRequest\CashRequestAttachment;
use App\Services\ConnectionService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CashRequestController extends Controller
{
    public function getCN() {
        $service = resolve(ConnectionService::class);
        return $service->getCNForEmployeeUser();
    }

    public function getOrganizationId() {
        $service = resolve(ConnectionService::class);
        return $service->getOrganizationIdForEmployeeUser();
    }


    public function getAUEmployee() 
    {
        $cn           = $this->getCN();
        $employeeUser = auth()->user();
        $employee     = Employee::on($cn)->where(Employee::PHONE, $employeeUser->phone)->first();
        return $employee;
    }

    public function index(GetCashRequest $request)
    {
        $cn       = $this->getCN();
        $employee = $this->getAUEmployee();
        $limit    = $request->input(GetCashRequest::LIMIT, 10);
        $filters  = $request->input(GetCashRequest::FILTER, []);
        $status   = Arr::get($filters, GetCashRequest::FILTER_STATUS);

        $sort      = $request->input(GetCashRequest::SORT, []);
        $sortField = Arr::get($sort, GetCashRequest::SORT_FIELD);
        $sortType  = Arr::get($sort, GetCashRequest::SORT_TYPE, 'asc');

        $cashrRequests = CashRequest::on($cn)->where(CashRequest::EMPLOYEE_ID, $employee->id)
            ->when(!is_null($status), fn($q) => $q->where(CashRequest::STATUS, $status));

        if ($sortField && $sortType) 
            $cashrRequests->orderBy($sortField, $sortType);

        return CashRequestResource::collection($cashrRequests->paginate($limit));
    }
    
    public function store(StoreCashRequest $request)
    {
        $cn                     = $this->getCN();
        $employee               = $this->getAUEmployee();
        $isCompany              = $request->input(StoreCashRequest::IS_COMPANY);
        $receiverName           = $request->input(StoreCashRequest::RECEIVER_NAME);
        $amount                 = $request->input(StoreCashRequest::AMOUNT);
        $transactionDescription = $request->input(StoreCashRequest::TRANSACTION_DESCRIPTION);
        $bankId                 = $request->input(StoreCashRequest::BANK_ID);
        $isIban                 = $request->input(StoreCashRequest::IS_IBAN);
        $ibanNumber             = $request->input(StoreCashRequest::IBAN_NUMBER);
        $accountNumber          = $request->input(StoreCashRequest::ACCOUNT_NUMBER);
        $attachments            = $request->file(StoreCashRequest::ATTACHMENTS);

        $bank = Bank::find($bankId);

        $cashRequest = CashRequest::on($cn)->create([
            CashRequest::DEPARTMENT_NAME         => $employee->department->full_department_name(),
            CashRequest::EMPLOYEE_ID             => $employee->id,
            CashRequest::EMPLOYEE_LAST_NAME      => $employee->last_name,
            CashRequest::EMPLOYEE_FIRST_NAME     => $employee->first_name,
            CashRequest::IS_COMPANY              => $isCompany,
            CashRequest::RECEIVER_NAME           => $receiverName,
            CashRequest::TRANSACTION_DESCRIPTION => $transactionDescription,
            CashRequest::BANK_ID                 => $bankId,
            CashRequest::BANK_NAME               => $bank->name,
            CashRequest::IBAN_NUMBER             => $isIban ? $ibanNumber : null,
            CashRequest::ACCOUNT_NUMBER          => $isIban ? null : $accountNumber,
            CashRequest::AMOUNT                  => $amount,
            CashRequest::STATUS                  => CashRequestStatusEnum::PENDING,
        ]);

        $organizationId = $this->getOrganizationId();
        collect($attachments)->map(function ($attachment, $index) use ($cashRequest, $organizationId, $cn) {
            $originalName = $attachment->getClientOriginalName();
            $extension    = $attachment->getClientOriginalExtension();
            $minioPath = "time-astron/{$organizationId}/cash-requests/{$cashRequest->id}/attachments/{$index}_" . Str::uuid() . '.' . $extension;
                // MinIO руу хадгалах
            Storage::disk('minio')->put($minioPath, file_get_contents($attachment->getRealPath()));
            CashRequestAttachment::on($cn)->create([
                CashRequestAttachment::CASH_REQUEST_ID => $cashRequest->id,
                CashRequestAttachment::FILE_PATH       => $minioPath,
                CashRequestAttachment::ORIGINAL_NAME   => $originalName
            ]);
        });
        return new CashRequestResource($cashRequest);
    }
}
