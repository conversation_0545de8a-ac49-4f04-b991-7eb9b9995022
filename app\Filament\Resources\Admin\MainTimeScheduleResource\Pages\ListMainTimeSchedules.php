<?php

namespace App\Filament\Resources\Admin\MainTimeScheduleResource\Pages;

use App\Filament\Resources\Admin\MainTimeScheduleResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMainTimeSchedules extends ListRecords
{
    protected static string $resource = MainTimeScheduleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх'),
        ];
    }
}
