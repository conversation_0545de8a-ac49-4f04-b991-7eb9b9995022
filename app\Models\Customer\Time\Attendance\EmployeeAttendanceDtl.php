<?php

namespace App\Models\Customer\Time\Attendance;

use App\Http\Resources\FundTime;
use App\Models\Customer\Decision\Decision;
use App\Models\Customer\Time\Attendance\EmployeeAttendance;
use Carbon\Carbon;
use App\Http\Tools\DateTool;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EmployeeAttendanceDtl extends Model
{
    use HasFactory;
    const TABLE = 'employee_attendance_dtls';

    const ID                            = 'id';
    const EMPLOYEE_ATTENDANCE_ID        = 'employee_attendance_id';
    const BEGIN_DATE                    = 'begin_date';
    const END_DATE                      = 'end_date';
    const BEGIN_SIGN                    = 'begin_sign';
    const END_SIGN                      = 'end_sign';
    const TYPE                          = 'type';
    const ATTENDANCE_STATUS             = 'attendance_status';
    const CREATED_BY                    = 'created_by';
    const UPDATED_BY                    = 'updated_by';

    const CHECK_IO                      = 'check_io';
    const FIRST_STR                     = 'first_str';
    const SECOND_STR                    = 'second_str';
    const FIRST_BEHAVIOR                = 'first_behavior';
    const SECOND_BEHAVIOR               = 'second_behavior';
    const ADMIN_LATE_MIN                = 'admin_late_min';
    const IS_SPECIAL                    = 'is_special';
    const DECISION_TYPE                 = 'decision_type';
    const ATTENDANCE_DATE               = 'attendance_date';
    const IS_LATE_TYPE                  = 'is_late_type';

    const RELATION_EMPLOYEE_ATTENDANCE  = 'employee_attendance';

    const PREV_EMPLOYEE_ATTENDANCE_DTL  = 'prev_employee_attendance_dtl';
    const NEXT_EMPLOYEE_ATTENDANCE_DTL  = 'next_employee_attendance_dtl';

    const INPUT_STR                     = 'I';
    const OUTPUT_STR                    = 'O';
    const SCHEDULE_STR                  = 'S';
    const MARK_STR                      = 'M';
    const LIMIT_STR                     = 'L';
    const REQUEST_STR                   = 'R';
    const NIGHT_STR                     = 'N';

    const SI                            = 'SI';
    const SO                            = 'SO';
    const MI                            = 'MI';
    const MO                            = 'MO';
    const RI                            = 'RI';
    const RO                            = 'RO';

    const TYPE_BEFORE_WORK              = 1;
    const TYPE_AFTER_WORK               = 2;
    const TYPE_WORK                     = 3;
    const TYPE_BREAK                    = 4;
    const TYPE_OVER                     = 5;
    const TYPE_EFFORT                   = 6;

    const ATTENDANCE_STATUS_UNKNOWN_WORK        = 0; // Тодорхойгүй
    const ATTENDANCE_STATUS_UNKNOWN_EFFORT      = 1; // Тодорхойгүй идэвх
    const ATTENDANCE_STATUS_EFFORT              = 2; // Идэвх
    const ATTENDANCE_STATUS_WORK                = 3; // Ажилласан
    const ATTENDANCE_STATUS_BREAK               = 4; // Цайны цаг
    const ATTENDANCE_STATUS_BREAK_EFFORT        = 5; // Цайны цагийн
    const ATTENDANCE_STATUS_LATE                = 6; // Хоцорсон
    const ATTENDANCE_STATUS_ABSENT              = 7; // Тасалсан
    const ATTENDANCE_STATUS_FURLOUGH            = 8; // Чөлөө
    const ATTENDANCE_STATUS_SALARY_FURLOUGH     = 9; // Цалинтай чөлөө
    const ATTENDANCE_STATUS_OVER                = 10; // Илүү цаг
    const ATTENDANCE_STATUS_MATERNITY           = 11; // Жирэмсэнийн амралт
    const ATTENDANCE_STATUS_VACATION            = 12; // Ээлжийн амралт
    const ATTENDANCE_STATUS_SICK                = 13; // Өвчтэй
    const ATTENDANCE_STATUS_COMPENSATORY_REST   = 15; // Шөнө ажилласан
    const ATTENDANCE_STATUS_NIGHT_WORK          = 14; // Нөхөж амарсан
    const ATTENDANCE_STATUS_OUT_WORK            = 17; // Хүсэлтээр ажилласан
    const ATTENDANCE_STATUS_REQUEST_WORK        = 16; // Гадуур ажилласан

    // sub statuses
    const ATTENDANCE_STATUS_FURLOUGH_SHORT                  = 81;
    const ATTENDANCE_STATUS_FURLOUGH_MEDIUM                 = 82;
    const ATTENDANCE_STATUS_FURLOUGH_LONG                   = 83;
    const ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY        = 91;
    const ATTENDANCE_STATUS_SALARY_FURLOUGH_KID_BIRTHDAY    = 92;
    const ATTENDANCE_STATUS_SALARY_FURLOUGH_HALFDAY         = 93;
    const ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE         = 94;

    const ATTENDANCE_STATUS_NAME_UNKNOWN_WORK       = 'Тодорхойгүй';
    const ATTENDANCE_STATUS_NAME_UNKNOWN_EFFORT     = 'Тодорхойгүй идэвх';
    const ATTENDANCE_STATUS_NAME_EFFORT             = 'Идэвх';
    const ATTENDANCE_STATUS_NAME_WORK               = 'Ажилласан';
    const ATTENDANCE_STATUS_NAME_BREAK              = 'Цайны цаг';
    const ATTENDANCE_STATUS_NAME_BREAK_EFFORT       = 'Цайны цагийн идэвх';
    const ATTENDANCE_STATUS_NAME_LATE               = 'Хоцорсон';
    const ATTENDANCE_STATUS_NAME_ABSENT             = 'Тасалсан';
    const ATTENDANCE_STATUS_NAME_FURLOUGH           = 'Чөлөө';
    const ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH    = 'Цалинтай чөлөө';
    const ATTENDANCE_STATUS_NAME_OVER               = 'Илүү цаг';
    const ATTENDANCE_STATUS_NAME_MATERNITY          = 'Жирэмсэнийн амралт';
    const ATTENDANCE_STATUS_NAME_VACATION           = 'Ээлжийн амралт';
    const ATTENDANCE_STATUS_NAME_SICK               = 'Өвчтэй';
    const ATTENDANCE_STATUS_NAME_NIGHT_WORK         = 'Шөнө ажилласан';
    const ATTENDANCE_STATUS_NAME_COMPENSATORY_REST  = 'Нөхөж амарсан';
    const ATTENDANCE_STATUS_NAME_REQUEST_WORK       = 'Хүсэлтээр ажилласан';
    const ATTENDANCE_STATUS_NAME_OUT_WORK           = 'Гадуур ажилласан';

    # Чөлөө дэд төрлүүдийн нэр
    const ATTENDANCE_STATUS_NAME_FURLOUGH_SHORT     = 'Богино';
    const ATTENDANCE_STATUS_NAME_FURLOUGH_MEDIUM    = 'Дунд';
    const ATTENDANCE_STATUS_NAME_FURLOUGH_LONG      = 'Урт';

    # Цалинтай чөлөө дэд төрлүүдийн нэр
    const ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_BIRTHDAY       = 'Төрсөн өдөр';
    const ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_KID_BIRTHDAY   = 'Хүүхдийн төрсөн өдөр';
    const ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_HALFDAY        = 'Хагас өдөр';
    const ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_NOSMOKE        = 'Тамхи татдаггүй';

    protected $fillable = [
        self::ID,
        self::EMPLOYEE_ATTENDANCE_ID,
        self::BEGIN_DATE,
        self::END_DATE,
        self::BEGIN_SIGN,
        self::END_SIGN,
        self::TYPE,
        self::ATTENDANCE_STATUS,
        self::CREATED_BY,
        self::UPDATED_BY,
    ];

    public function employee_attendance(): BelongsTo {
        return $this->belongsTo(EmployeeAttendance::class);
    }

    const ENABLED_ATTENDANCE_STATUS = [
        self::ATTENDANCE_STATUS_EFFORT,
        self::ATTENDANCE_STATUS_WORK,
        self::ATTENDANCE_STATUS_LATE,
        self::ATTENDANCE_STATUS_ABSENT,
        self::ATTENDANCE_STATUS_SICK,
    ];

    const REQUEST_ATTENDANCE_STATUS = [
        self::ATTENDANCE_STATUS_WORK,
        self::ATTENDANCE_STATUS_SICK,
        self::ATTENDANCE_STATUS_VACATION,
        self::ATTENDANCE_STATUS_OUT_WORK,
        self::ATTENDANCE_STATUS_FURLOUGH_SHORT,
        self::ATTENDANCE_STATUS_FURLOUGH_MEDIUM,
        self::ATTENDANCE_STATUS_FURLOUGH_LONG,
        self::ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY,
        self::ATTENDANCE_STATUS_SALARY_FURLOUGH_KID_BIRTHDAY,
        self::ATTENDANCE_STATUS_SALARY_FURLOUGH_HALFDAY,
        self::ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE,
    ];

    const DISABLED_ATTENDANCE_STATUS = [
        self::ATTENDANCE_STATUS_NIGHT_WORK,
        self::ATTENDANCE_STATUS_BREAK,
        self::ATTENDANCE_STATUS_BREAK_EFFORT,
        self::ATTENDANCE_STATUS_OVER,
        self::ATTENDANCE_STATUS_FURLOUGH,
        self::ATTENDANCE_STATUS_SALARY_FURLOUGH,
        self::ATTENDANCE_STATUS_MATERNITY,
        self::ATTENDANCE_STATUS_VACATION,
    ];

    const CONFIG_ATTENDANCE_STATUS = [
        self::ATTENDANCE_STATUS_NAME_EFFORT,
        self::ATTENDANCE_STATUS_NAME_WORK,
        self::ATTENDANCE_STATUS_NAME_BREAK,
        self::ATTENDANCE_STATUS_NAME_BREAK_EFFORT,
        self::ATTENDANCE_STATUS_NAME_LATE,
        self::ATTENDANCE_STATUS_NAME_ABSENT,
        self::ATTENDANCE_STATUS_NAME_FURLOUGH,
        self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH,
        self::ATTENDANCE_STATUS_NAME_OVER,
        self::ATTENDANCE_STATUS_NAME_MATERNITY,
        self::ATTENDANCE_STATUS_NAME_VACATION,
        self::ATTENDANCE_STATUS_NAME_SICK,
        self::ATTENDANCE_STATUS_NAME_NIGHT_WORK,
        self::ATTENDANCE_STATUS_NAME_COMPENSATORY_REST,
        self::ATTENDANCE_STATUS_NAME_REQUEST_WORK,
        self::ATTENDANCE_STATUS_NAME_OUT_WORK,
    ];

    public function isDisabledAttStatus() : bool {
        return in_array($this->attendance_status, [4]);
    }

    public function getEnabledAttStatusNames() {
        $attendanceStatuses[self::ATTENDANCE_STATUS_WORK]           = self::ATTENDANCE_STATUS_NAME_WORK;
        $attendanceStatuses[self::ATTENDANCE_STATUS_LATE]           = self::ATTENDANCE_STATUS_NAME_LATE;
        $attendanceStatuses[self::ATTENDANCE_STATUS_ABSENT]         = self::ATTENDANCE_STATUS_NAME_ABSENT;
        $attendanceStatuses[self::ATTENDANCE_STATUS_SICK]           = self::ATTENDANCE_STATUS_NAME_SICK;
        $attendanceStatuses[self::ATTENDANCE_STATUS_UNKNOWN_WORK]   = self::ATTENDANCE_STATUS_NAME_UNKNOWN_WORK;
        $attendanceStatuses[self::ATTENDANCE_STATUS_EFFORT]         = self::ATTENDANCE_STATUS_NAME_EFFORT;
        $attendanceStatuses[self::ATTENDANCE_STATUS_UNKNOWN_EFFORT] = self::ATTENDANCE_STATUS_NAME_UNKNOWN_EFFORT;
        $attendanceStatuses[self::ATTENDANCE_STATUS_FURLOUGH]       = self::ATTENDANCE_STATUS_NAME_FURLOUGH;
        return $attendanceStatuses;
    }

    public function getDisabledAttStatusNames() {
        $attendanceStatuses[self::ATTENDANCE_STATUS_BREAK]           = self::ATTENDANCE_STATUS_NAME_BREAK;
        $attendanceStatuses[self::ATTENDANCE_STATUS_OVER]            = self::ATTENDANCE_STATUS_NAME_OVER;
        $attendanceStatuses[self::ATTENDANCE_STATUS_SALARY_FURLOUGH] = self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH;
        $attendanceStatuses[self::ATTENDANCE_STATUS_MATERNITY]       = self::ATTENDANCE_STATUS_NAME_MATERNITY;
        $attendanceStatuses[self::ATTENDANCE_STATUS_VACATION]        = self::ATTENDANCE_STATUS_NAME_VACATION;
        return $attendanceStatuses;
    }

    public function getConfigAttStatusNames() {
        $attendanceStatuses['ATTENDANCE_STATUS_VACATION']           = self::ATTENDANCE_STATUS_NAME_VACATION;
        $attendanceStatuses['ATTENDANCE_STATUS_SICK']               = self::ATTENDANCE_STATUS_NAME_SICK;
        $attendanceStatuses['ATTENDANCE_STATUS_OUT_WORK']           = self::ATTENDANCE_STATUS_NAME_OUT_WORK;
        $attendanceStatuses['ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_SHORT']                        = self::ATTENDANCE_STATUS_NAME_FURLOUGH.' ('.self::ATTENDANCE_STATUS_NAME_FURLOUGH_SHORT.')';
        $attendanceStatuses['ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_MEDIUM']                       = self::ATTENDANCE_STATUS_NAME_FURLOUGH.' ('.self::ATTENDANCE_STATUS_NAME_FURLOUGH_MEDIUM.')';
        $attendanceStatuses['ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_LONG']                         = self::ATTENDANCE_STATUS_NAME_FURLOUGH.' ('.self::ATTENDANCE_STATUS_NAME_FURLOUGH_LONG.')';
        $attendanceStatuses['ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY']       = self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH.' ('.self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_BIRTHDAY.')';
        $attendanceStatuses['ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_KID_BIRTHDAY']   = self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH.' ('.self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_KID_BIRTHDAY.')';
        $attendanceStatuses['ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_HALFDAY']        = self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH.' ('.self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_HALFDAY.')';
        $attendanceStatuses['ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE']        = self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH.' ('.self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_NOSMOKE.')';
        return $attendanceStatuses;
    }

    public function getAttStatusId($att_status) {
        switch ($att_status) {
            case self::ATTENDANCE_STATUS_UNKNOWN_WORK:
                return 'ATTENDANCE_STATUS_UNKNOWN_WORK';
            case self::ATTENDANCE_STATUS_UNKNOWN_EFFORT:
                return 'ATTENDANCE_STATUS_UNKNOWN_EFFORT';
            case self::ATTENDANCE_STATUS_EFFORT:
                return 'ATTENDANCE_STATUS_EFFORT';
            case self::ATTENDANCE_STATUS_WORK:
                return 'ATTENDANCE_STATUS_WORK';
            case self::ATTENDANCE_STATUS_BREAK:
                return 'ATTENDANCE_STATUS_BREAK';
            case self::ATTENDANCE_STATUS_BREAK_EFFORT:
                return 'ATTENDANCE_STATUS_BREAK_EFFORT';
            case self::ATTENDANCE_STATUS_LATE:
                return 'ATTENDANCE_STATUS_LATE';
            case self::ATTENDANCE_STATUS_ABSENT:
                return 'ATTENDANCE_STATUS_ABSENT';
            case self::ATTENDANCE_STATUS_FURLOUGH:
                return 'ATTENDANCE_STATUS_FURLOUGH';
            case self::ATTENDANCE_STATUS_SALARY_FURLOUGH:
                return 'ATTENDANCE_STATUS_SALARY_FURLOUGH';
            case self::ATTENDANCE_STATUS_OVER:
                return 'ATTENDANCE_STATUS_OVER';
            case self::ATTENDANCE_STATUS_MATERNITY:
                return 'ATTENDANCE_STATUS_MATERNITY';
            case self::ATTENDANCE_STATUS_VACATION:
                return 'ATTENDANCE_STATUS_VACATION';
            case self::ATTENDANCE_STATUS_SICK:
                return 'ATTENDANCE_STATUS_SICK';
            case self::ATTENDANCE_STATUS_NIGHT_WORK:
                return 'ATTENDANCE_STATUS_NIGHT_WORK';
            case self::ATTENDANCE_STATUS_COMPENSATORY_REST:
                return 'ATTENDANCE_STATUS_COMPENSATORY_REST';
            case self::ATTENDANCE_STATUS_REQUEST_WORK:
                return 'ATTENDANCE_STATUS_REQUEST_WORK'; 
            case self::ATTENDANCE_STATUS_OUT_WORK:
                return 'ATTENDANCE_STATUS_OUT_WORK';
            case self::ATTENDANCE_STATUS_FURLOUGH_SHORT:
                return 'ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_SHORT';
            case self::ATTENDANCE_STATUS_FURLOUGH_MEDIUM:
                return 'ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_MEDIUM';
            case self::ATTENDANCE_STATUS_FURLOUGH_LONG:
                return 'ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_LONG';
            case self::ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY:
                return 'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY';
            case self::ATTENDANCE_STATUS_SALARY_FURLOUGH_KID_BIRTHDAY:
                return 'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_KID_BIRTHDAY';
            case self::ATTENDANCE_STATUS_SALARY_FURLOUGH_HALFDAY:
                return 'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_HALFDAY';
            case self::ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE:
                return 'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE';
            default:
                return '';
        }
    }

    public function getBeginSign() {
        return $this->first_behavior  . $this->first_str;
    }

    public function getEndSign() {
        return $this->second_behavior . $this->second_str;
    }

    public function expandBeginSign() {
        $this->first_behavior =  $this->begin_sign[0];
        $this->first_str      =  $this->begin_sign[1];
    }

    public function expandEndSign() {
        $this->second_behavior =  $this->end_sign[0];
        $this->second_str      =  $this->end_sign[1];
    }

    public function checkPrev() {
        $marked                    = false;
        $prevEmployeeAttendanceDtl = $this->prev_employee_attendance_dtl;
        if ($prevEmployeeAttendanceDtl) {
            $beginSign             = $prevEmployeeAttendanceDtl->getBeginSign();
            $endSign               = $prevEmployeeAttendanceDtl->getEndSign();
            $marked = $beginSign === static::MI || $endSign === static::MI;

            if ($endSign === static::MO)
                return false;

            if (!$marked) {
                $marked = $prevEmployeeAttendanceDtl->checkPrev();
            }
        }
        return $marked;
    }

    public function checkNext() {
        $marked                    = false;
        $nextEmployeeAttendanceDtl = $this->next_employee_attendance_dtl;
        if ($nextEmployeeAttendanceDtl) {
            $beginSign             = $nextEmployeeAttendanceDtl->getBeginSign();
            $endSign               = $nextEmployeeAttendanceDtl->getEndSign();
            $marked = $beginSign === static::MO || $endSign === static::MO;

            if ($beginSign === static::MI)
                return false;

            if (!$marked) {
                $marked = $nextEmployeeAttendanceDtl->checkNext();
            }
        }
        return $marked;
    }

    public function setStatus() {
        $beginSign          = $this->getBeginSign();
        $endSign            = $this->getEndSign();
        $differenceMinutes  = $this->getDifferenceMinutes();
        $adminLateMin       = $this->admin_late_min;
        $type               = $this->type;
        $isOverTime         = self::TYPE_OVER == $type;
        $status             = null;
        switch ($type) {
            case self::TYPE_BEFORE_WORK:
            case self::TYPE_AFTER_WORK:
            case self::TYPE_EFFORT:
                $status = static::ATTENDANCE_STATUS_EFFORT;
                break;
            case self::TYPE_OVER:
            case self::TYPE_WORK:
                if ($this->decision_type != 0) {
                    switch ($this->decision_type) {
                        case Decision::TYPE_VACATION:
                            $status = static::ATTENDANCE_STATUS_VACATION;
                            break;
                        case Decision::TYPE_LONG_TERM_FURLOUGH:
                            $status = static::ATTENDANCE_STATUS_FURLOUGH;
                            break;
                        case Decision::TYPE_MATERNITY:
                            $status = static::ATTENDANCE_STATUS_MATERNITY;
                            break;
                        default:
                            # code...
                            break;
                    }
                } else {
                    if ($this->is_special) {
                        $status = $isOverTime ? static::ATTENDANCE_STATUS_OVER : static::ATTENDANCE_STATUS_WORK;
                    } else {
                        if ($beginSign === static::SI && $endSign === static::SO) {
                            if ($this->checkPrev() && $this->checkNext()) {
                                $status = $isOverTime ? static::ATTENDANCE_STATUS_OVER : static::ATTENDANCE_STATUS_WORK;
                            } else {
                                $status = static::ATTENDANCE_STATUS_ABSENT;
                            }
                        } else if ($beginSign === static::MI && $endSign === static::SO) {
                            if ($this->checkNext())
                                $status = $isOverTime ? static::ATTENDANCE_STATUS_OVER : static::ATTENDANCE_STATUS_WORK;
                            else
                                $status = static::ATTENDANCE_STATUS_ABSENT;
                        } else if ($beginSign === static::SI && $endSign === static::MI) {
                            $status = static::ATTENDANCE_STATUS_ABSENT;
                        } else if ($beginSign === static::MI && $endSign === static::MO) {
                            $status = $isOverTime ? static::ATTENDANCE_STATUS_OVER : static::ATTENDANCE_STATUS_WORK;
                        } else if ($beginSign === static::MO && $endSign === static::SO) {
                            $status = static::ATTENDANCE_STATUS_ABSENT;
                        } else if ($beginSign === static::SI && $endSign === static::MO) {
                            if ($this->checkPrev()) {
                                $status = $isOverTime ? static::ATTENDANCE_STATUS_OVER : static::ATTENDANCE_STATUS_WORK;
                            } else {
                                $status = static::ATTENDANCE_STATUS_ABSENT;
                            }
                        } else if ($beginSign === static::MO && $endSign === static::MI) {
                            $status = static::ATTENDANCE_STATUS_ABSENT;
                        }
                    }

                    /**
                     * ЗӨВХӨН АЖИЛД ИРЭХ ЦАГААС ХОЦОРСОН БОЛ ХОЦРОЛТ ГЭЖ ТООЦОНО
                     * Хоцролт тооцох
                     */
                    if ($this->is_late_type && $status == static::ATTENDANCE_STATUS_ABSENT && $adminLateMin >= $differenceMinutes) {
                        $status = static::ATTENDANCE_STATUS_LATE;
                    }
                }
                break;
            case self::TYPE_BREAK:
                $status = static::ATTENDANCE_STATUS_BREAK;
                break;
        }
        $this->attendance_status    = $status != null ? $status : $this->attendance_status;
    }

    public function setBeginAndEndSign() {
        $this->begin_sign   = $this->getBeginSign();
        $this->end_sign     = $this->getEndSign();
    }

    public function getDifferenceMinutes() {
        $startTime     = Carbon::parse($this->begin_date);
        $finishTime    = Carbon::parse($this->end_date);
        $totalDuration = $finishTime->diffInMinutes($startTime);
        return $totalDuration;
    }

    public function getScheduleTime() {
        if ($this->type !== self::TYPE_WORK)
            return;
        return $this->getDifferenceMinutes();
    }

    public function clearOtherColumns() {
        unset($this->check_io);
        unset($this->first_str);
        unset($this->second_str);
        unset($this->first_behavior);
        unset($this->second_behavior);
        unset($this->admin_late_min);
        unset($this->is_special);
        unset($this->decision_type);
        unset($this->attendance_date);
        unset($this->prev_employee_attendance_dtl);
        unset($this->next_employee_attendance_dtl);
        unset($this->is_late_type);
    }

    public function getTimeStr() {
        return DateTool::getTime($this->begin_date) . '-' . DateTool::getTime($this->end_date);
    }

    public function getDiffMinutes() {
        $start = strtotime($this->begin_date);
        $end   = strtotime($this->end_date);
        $mins  = ($end - $start) / 60;
        return $mins;
    }

    public function setAttendanceDate() {
        return $this->attendance_date = DateTool::getDate($this->begin_date);
    }

    public function getAttStatusName() {
        switch ($this->attendance_status) {
            case self::ATTENDANCE_STATUS_UNKNOWN_WORK:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_UNKNOWN_WORK;
                break;
            case self::ATTENDANCE_STATUS_UNKNOWN_EFFORT:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_UNKNOWN_EFFORT;
                break;
            case self::ATTENDANCE_STATUS_EFFORT:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_EFFORT;
                break;
            case self::ATTENDANCE_STATUS_WORK:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_WORK;
                break;
            case self::ATTENDANCE_STATUS_BREAK:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_BREAK;
                break;
            case self::ATTENDANCE_STATUS_BREAK_EFFORT:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_BREAK_EFFORT;
                break;
            case self::ATTENDANCE_STATUS_LATE:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_LATE;
                break;
            case self::ATTENDANCE_STATUS_ABSENT:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_ABSENT;
                break;
            case self::ATTENDANCE_STATUS_FURLOUGH:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_FURLOUGH;
                break;
            case self::ATTENDANCE_STATUS_SALARY_FURLOUGH:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH;
                break;
            case self::ATTENDANCE_STATUS_OVER:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_OVER;
                break;
            case self::ATTENDANCE_STATUS_MATERNITY:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_MATERNITY;
                break;
            case self::ATTENDANCE_STATUS_VACATION:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_VACATION;
                break;
            case self::ATTENDANCE_STATUS_SICK:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_SICK;
                break;
            case self::ATTENDANCE_STATUS_NIGHT_WORK:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_NIGHT_WORK;
                break;
            case self::ATTENDANCE_STATUS_COMPENSATORY_REST:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_COMPENSATORY_REST;
                break;
            case self::ATTENDANCE_STATUS_REQUEST_WORK:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_REQUEST_WORK;
                break;
            case self::ATTENDANCE_STATUS_OUT_WORK:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_OUT_WORK;
                break;
            case self::ATTENDANCE_STATUS_FURLOUGH_SHORT:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_FURLOUGH.' ('.self::ATTENDANCE_STATUS_NAME_FURLOUGH_SHORT.')';
                break;
            case self::ATTENDANCE_STATUS_FURLOUGH_MEDIUM:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_FURLOUGH.' ('.self::ATTENDANCE_STATUS_NAME_FURLOUGH_MEDIUM.')';
                break;
            case self::ATTENDANCE_STATUS_FURLOUGH_LONG:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_FURLOUGH.' ('.self::ATTENDANCE_STATUS_NAME_FURLOUGH_LONG.')';
                break;
            case self::ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH.' ('.self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_BIRTHDAY.')';
                break;
            case self::ATTENDANCE_STATUS_SALARY_FURLOUGH_KID_BIRTHDAY:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH.' ('.self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_KID_BIRTHDAY.')';
                break;
            case self::ATTENDANCE_STATUS_SALARY_FURLOUGH_HALFDAY:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH.' ('.self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_HALFDAY.')';
                break;
            case self::ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE:
                $attStatusName = self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH.' ('.self::ATTENDANCE_STATUS_NAME_SALARY_FURLOUGH_NOSMOKE.')';
                break;
            default:
                # code...
                break;
        }
        return $attStatusName;
    }
}
