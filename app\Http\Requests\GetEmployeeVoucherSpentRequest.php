<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetEmployeeVoucherSpentRequest extends FormRequest
{
    const PARAMETER_IS_MERCHANT = 'is_merchant';
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'is_merchant' => 'required|boolean',
        ];
    }
}
