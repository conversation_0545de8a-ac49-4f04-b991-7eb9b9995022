<?php

namespace App\Filament\Resources\Admin\CashRequestResource\Pages;

use App\Filament\Resources\Admin\CashRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCashRequests extends ListRecords
{
    protected static string $resource = CashRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}
