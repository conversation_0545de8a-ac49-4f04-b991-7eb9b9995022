<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Filament\Resources\Admin\HolidayResource\Pages;
use App\Filament\Resources\Admin\HolidayResource\RelationManagers;
use App\Models\Customer\Time\Holiday;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class HolidayResource extends Can
{
    protected static ?string $model = Holiday::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Баяр ёслол';
    protected static ?string $modelLabel = 'баяр ёслол';
    protected static ?int $navigationSort = 8;
    protected static ?string $navigationGroup = 'Цаг';
    protected static ?string $slug = 'holidays';

    public static function form(Form $form): Form
    {
        return $form
                ->schema([
                    Forms\Components\Section::make()
                        ->schema([
                            Forms\Components\TextInput::make(Holiday::NAME)
                                ->label('Нэр')
                                ->maxLength(50)
                                ->required(),

                            Forms\Components\DatePicker::make(Holiday::BEGIND_DATE)
                                ->label('Эхлэх огноо')
                                ->native(false)
                                ->default(now())
                                ->displayFormat('d/m/Y'),

                            Forms\Components\DatePicker::make(Holiday::END_DATE)
                                ->label('Дуусах огноо')
                                ->native(false)
                                ->default(now())
                                ->displayFormat('d/m/Y'),
                        ])
                        ->columns(1)
                        ->columnSpan(['lg' => fn (?Holiday $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Holiday $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Holiday $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Holiday $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('begin_date')->label('Эхлэх огноо')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('end_date')->label('Дуусах огноо')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHolidays::route('/'),
            'create' => Pages\CreateHoliday::route('/create'),
            'edit' => Pages\EditHoliday::route('/{record}/edit'),
        ];
    }
}
