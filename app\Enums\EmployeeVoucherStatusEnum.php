<?php

namespace App\Enums;
use Filament\Support\Contracts\HasLabel;
 
enum EmployeeVoucherStatusEnum: string implements HasLabel
{
    case BUREN   = 'buren';
    case DUTUU   = 'dutuu';
    case OGT     = 'ogt';


    public function getLabelColor(): ?string
    {
        return match ($this) {
            self::BUREN   => 'success',
            self::DUTUU   => 'warning',
            self::OGT     => 'gray',
        };
    }
    
    public function getLabel(): ?string
    {
        return match ($this) {
            self::BUREN   => 'Бүрэн ашигласан',
            self::DUTUU   => 'Дутуу ашигласан',
            self::OGT     => 'Огт ашиглаагүй',
        };
    }
}
