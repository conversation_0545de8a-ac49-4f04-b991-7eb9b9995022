<?php

namespace App\Filament\Resources\Admin\PositionResource\Pages;

use App\Models\Customer\Position;

use App\Filament\Resources\Admin\PositionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPosition extends EditRecord
{
    protected static string $resource = PositionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data[Position::UPDATED_BY] = auth()->user()->id;
        return $data;
    }
}
