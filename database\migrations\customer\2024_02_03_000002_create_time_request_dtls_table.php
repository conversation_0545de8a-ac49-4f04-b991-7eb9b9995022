<?php

use App\Models\Customer\Time\TimeRequestDtl;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTimeRequestDtlsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('time_request_dtls'))
            return;
        Schema::create('time_request_dtls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('time_request_id')->constrained();
            $table->unsignedBigInteger('user_id');
            $table->string('description')->nullable();
            $table->unsignedBigInteger('confirm_status');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('time_request_dtls');
    }
}
